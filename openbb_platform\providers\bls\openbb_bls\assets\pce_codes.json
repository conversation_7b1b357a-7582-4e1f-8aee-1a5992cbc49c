{"cx": {"category_code": {"ADDENDA": "Assets and liabilities, and other financial info", "CUCHARS": "Consumer Characteristics", "EXPEND": "Expenditures", "INCOME": "Income and Taxes"}, "subcategory_code": {"CHGASLI": "Net change in total assets and liabilities", "TITLEOFI": "Other financial informations:", "CONSUNIT": "Number of consumer units (in thousands)", "TITLECU": "Consumer unit characteristics:", "TITLEPD": "Percent distribution:", "ALCBEVG": "Alcoholic beverages", "APPAREL": "Apparel and services", "CASHCONT": "Cash contributions", "EDUCATN": "Education", "ENTRTAIN": "Entertainment", "FOODTOTL": "Food", "HEALTH": "Healthcare", "HOUSING": "Housing", "INSPENSN": "Personal insurance and pensions", "MISC": "Miscellaneous expenditures", "PERSCARE": "Personal care products and services", "READING": "Reading", "TOBACCO": "Tobacco products and smoking supplies", "TOTALEXP": "Total average annual expenditures", "TRANS": "Transportation", "INCAFTTX": "Income after taxes", "INCBEFTX": "Income before taxes", "PERSTAX": "Personal taxes"}, "item_code": {"200111": "Beer and ale", "200210": "Whiskey", "200310": "Wine", "200410": "Other alcoholic beverages", "200511": "Beer and ale at fast food, carry out, delivery, concession stands, buffets, and cafeterias", "200512": "Beer and ale at full service restaurants", "200513": "Beer and ale at vending machines and mobile vendors", "200514": "Beer at employer", "200515": "Beer at board", "200516": "Beer and ale at catered affairs", "200521": "Wine at fast food, carry out, delivery, concession stands, buffets, and cafeterias", "200522": "Wine at full service restaurants", "200523": "Wine at vending machines and mobile vendors", "200524": "Wine at employer", "200525": "Wine at board", "200526": "Wine at catered affairs", "200531": "Other alcoholic beverages at fast food, carry out, delivery, concession stands, buffets, and cafeterias", "200532": "Other alcoholic beverages at full service restaurants", "200533": "Other alcoholic beverages at vending machines and mobile vendors", "200534": "Other alcohol at employer", "200535": "Other alcohol at board", "200536": "Other alcoholic beverages at catered affairs", "200900": "Alcoholic beverages purchased on trips", "790420": "Alcoholic beverages at restaurants, taverns", "ALCAWAY": "Away from home", "ALCBEVG": "Alcoholic beverages", "ALCHOME": "At home", "BEERNALE": "Beer and ale", "OTHALCBV": "Other alcoholic beverages", "WINE": "Wine", "360110": "Men's suits", "360120": "Men's sportcoats and tailored jackets", "360210": "Men's coats and jackets", "360311": "Men's underwear", "360312": "Men's hosiery", "360320": "Men's nightwear", "360330": "Men's accessories", "360340": "Men's sweaters and vests", "360350": "Men's active sportswear", "360360": "Men's swimwear", "360410": "Men's shirts", "360420": "Men's shirts, sweaters, and vests", "360513": "Men's pants and shorts", "360901": "Men's uniforms", "360902": "Men's costumes", "370110": "Boys' coats and jackets", "370120": "Boys' sweaters", "370125": "Boys' shirts and sweaters", "370130": "Boys' shirts", "370211": "Boys' underwear", "370212": "Boys' nightwear", "370213": "Boys' hosiery", "370220": "Boys' accessories", "370311": "Boys' suits, sportcoats, and vests", "370314": "Boys' pants and shorts", "370360": "Boys' swimwear", "370901": "Boys' uniforms and active sportswear", "370902": "Boys' costumes", "370903": "Boys' uniforms", "370904": "Boys' active sportswear", "380110": "Women's coats and jackets", "380210": "Women's dresses", "380311": "Women's sportcoats and tailored jackets", "380312": "Women's vests and sweaters", "380313": "Women's shirts, tops, blouses", "380315": "Women's sweaters, shirts, tops, and vests", "380320": "Women's skirts", "380333": "Women's pants and shorts", "380340": "Women's active sportswear", "380360": "Women's swimwear", "380410": "Women's sleepwear", "380420": "Women's undergarments", "380430": "Women's hosiery", "380510": "Women's suits", "380901": "Women's accessories", "380902": "Women's uniforms", "380903": "Women's costumes", "390110": "Girls' coats and jackets", "390120": "Girls' dresses and suits", "390210": "Girls' shirts, blouses, sweaters, and vests", "390223": "Girls' skirts, pants, and shorts", "390230": "Girls' active sportswear", "390310": "Girls' underwear and sleepwear", "390321": "Girls' hosiery", "390322": "Girls' accessories", "390360": "Girls' swimwear", "390901": "Girls' uniforms", "390902": "Girls' costumes", "400110": "Men's footwear", "400210": "Boys' footwear", "400220": "Girls' footwear", "400310": "Women's footwear", "410110": "Infant coats, jackets, and snowsuits", "410120": "Infant dresses and outerwear", "410130": "Infant underwear", "410140": "Infant nightwear and loungewear", "410901": "Infant accessories", "420110": "Material for making clothes", "420115": "Material and supplies for sewing, needlework, and quilting (includes household items)", "420120": "Sewing patterns and notions", "430110": "Watches", "430120": "Jewelry", "440110": "Shoe repair and other shoe services", "440120": "Coin-operated apparel laundry and dry cleaning", "440130": "Alteration, repair, and tailoring of apparel and accessories", "440140": "Clothing rental", "440150": "Watch and jewelry repair", "440210": "Apparel laundry and dry cleaning not coin-operated", "440900": "Clothing storage", "APPAREL": "Apparel and services", "BOYS": "Boys, 2 to 15", "FOOTWEAR": "Footwear", "GIRLS": "Girls, 2 to 15", "INFANT": "Children under 2", "MENBOYS": "Men and boys", "MENS": "Men, 16 and over", "OTHAPPRL": "Other apparel products and services", "WMNSGRLS": "Women and girls", "WOMENS": "Women, 16 and over", "800111": "Alimony expenditures", "800121": "Child support expenditures", "800804": "Support for college students", "800811": "Gift to non-CU members of stocks, bonds, and mutual funds", "800821": "Cash contributions to charities and other organizations", "800831": "Cash contributions to churches and other religious organizations", "800841": "Cash contribution to educational institutions", "800851": "Cash contribution to political organizations", "800861": "Other cash gifts", "CASHCONT": "Cash contributions", "001000": "Purchase price of stocks, bonds or mutual funds", "001010": "Sale price of stocks, bonds, and mutual funds, net", "001210": "Investments to farm or business", "001220": "Assets taken from farm or business", "002010": "Change in savings account", "002020": "Change in checking account", "002030": "Change in U.S. savings bonds", "003000": "Change in money owed to consumer unit", "003100": "Change in surrender of insurance policies", "005100": "Value of savings, checking, money market, and CDs", "005110": "Value of savings, checking, money market, and CDs one year ago", "005200": "Value of retirement plans", "005210": "Value of retirement plans one year ago", "005300": "Surrender value of whole life insurance policies", "005310": "Surrender value of whole life insurance policies one year ago", "005400": "Amount owed on credit cards", "005410": "Amount owed on credit cards one year ago", "005500": "Amount owed on student loans", "005510": "Amount owed on student loans one year ago", "005600": "Amount owed on other loans", "005610": "Amount owed on other loans one year ago", "005700": "Value of other financial assets", "005710": "Value of other financial assets one year ago", "005800": "Value of stocks, bonds, and mutual funds", "005810": "Value of stocks, bonds, and mutual funds one year ago", "006003": "Total amount owed 2nd interview, CY Q1", "006004": "Total amount owed 5th interview, CY Q1", "006005": "Total amount owed 2nd interview, CY+1, Q1", "006006": "Total amount owed 5th interview, CY+1, Q1", "006007": "Total amt owed CY Q1", "006008": "Total amt owed CY+1, Q1", "220512": "Materials and supplies purchased for insulation, dwellings under construction, additions, finishing, remodeling, landscaping, etc.", "220513": "Supplies purchased for additions, maintenance, repairs, and new construction", "220611": "Capital improvements: labor and materials (owned home)", "220612": "Dishwasher, disposal, or range hood", "220615": "Capital improvements: labor and materials (owned vacation)", "220616": "Wall-to-wall carpeting", "790610": "Contractors labor and materials, supplies CU obtained, appliances provided by contractor, and other property", "790611": "Dishwasher, disposal, and range hood capital improvement (other property)", "790620": "Management services and improvements of other properties", "790630": "Special assessments (other property)", "790640": "Property management, security, parking (other property)", "790710": "Purchase price of property (other property)", "790730": "Closing costs purchase of property (other property)", "790810": "Sale price of property or trade-in amount (other property)", "790830": "Total expenses in sale of property (other property)", "790910": "Special lump sum mortgage payments (other property)", "790930": "Original loan amount (mortgage obtained during interview quarter) (other property)", "790940": "Reduction mortgage principal, home equity loan (other property)", "790950": "Original loan amount, home equity loan (loan obtained during interview quarter) (other property)", "810101": "Purchase price of property (owned home)", "810102": "Purchase price of property (owned vacation)", "810301": "Closing costs purchase of property (owned home)", "810302": "Closing costs on purchase of property (owned vacation)", "820101": "Sale price of property or trade-in amount (owned home)", "820102": "Sale price of property or trade-in amount (owned vacation)", "820202": "Mortgage principal held after sale of real estate (owned vacation)", "820301": "Total expenses in sale of property (owned home)", "820302": "Total expenses in sale of property (owned vacation)", "830101": "Special lump sum mortgage payment (owned home)", "830102": "Special lump sum mortgage payment (owned vacation)", "830201": "Reduction of mortgage principal (owned home)", "830202": "Reduction of mortgage principal (owned vacation)", "830203": "Reduction of mortgage principal, home equity loan (owned home)", "830204": "Reduction mortgage principal, home equity loan (owned vacation)", "830301": "Original loan amount (mortgage obtained during interview quarter) (owned home)", "830302": "Original loan amount (mortgage obtained during interview quarter) (owned vacation)", "830303": "Original loan amount, home equity loan (loan obtained during interview quarter) (owned home)", "830304": "Original loan amount, home equity loan (loan obtained during interview quarter) (owned vacation)", "840101": "Special assessments (owned home)", "840102": "Special assessments (owned vacation)", "850100": "Reduction of vehicle loan principal", "850200": "Vehicle principal balance (loan obtained during interview quarter)", "860100": "Sale of automobiles", "860200": "Sale of trucks, including vans", "860301": "Sale of motor camper", "860302": "Sale of other vehicles", "860400": "Sale of trailer type and other attachable campers", "860500": "Sale of motorcycles", "860600": "Sale of boats, with motors", "860700": "Sale of boats, without motors and boat trailers", "860800": "Sale of aircraft", "880120": "Principal paid, home equity line of credit (owned home)", "880220": "Principal paid, home equity line of credit (other property)", "880320": "Principal paid, home equity line of credit (owned vacation)", "990950": "Labor and materials for dwellings under construction and additions", "CAPIMPOH": "Capital improvement material (owned home)", "CAPIMPOP": "Capital improvement services, labor, materials, and equipment (other property)", "CHGACCNT": "Change in accounts", "CHGASLI": "Net change in total assets and liabilities", "CHGASSET": "Net change in total assets", "CHGCAPOH": "Change in capital improvements (owned home)", "CHGCAPOP": "Change in capital improvements (other property)", "CHGCAPOV": "Change in capital improvements (owned vacation)", "CHGCREDT": "Change in amount owed to creditors", "CHGINSUR": "Change in surrender of insurance policies", "CHGINVST": "Change in investments to own farm or business", "CHGLIAB": "Net change in total liabilities", "CHGMTGOH": "Change in mortgage principal (owned home)", "CHGMTGOP": "Change in mortgage principal (other property)", "CHGMTGOV": "Change in mortgage principal (owned vacation)", "CHGOTHFN": "Change in value of other financial assets", "CHGPRTOH": "Change in net property holdings (owned home)", "CHGPRTOP": "Change in net property holdings (other property)", "CHGPRTOV": "Change in net property holdings (owned vacation)", "CHGRET": "Change in value of retirement plans", "CHGSAVNG": "Change in value of savings, checking, money market, and CDs", "CHGSECUR": "Change in securities", "CHGVEH": "Change in vehicle sales", "CHGVEHPR": "Change in principal on vehicles", "CONSUNIT": "Number of consumer units (in thousands)", "005520": "Finance, late, and interest charges for student loans", "660000": "School supplies, etc., unspecified", "660110": "School books, supplies, equipment for college", "660115": "School books for college", "660210": "School books, supplies, equipment for elementary, high school", "660215": "School books for elementary and high schools", "660410": "School books, supplies, equipment for vocational and technical schools", "660415": "School books for vocational and technical schools", "660901": "School books, supplies, equipment for day care, nursery", "660902": "School books, supplies, equipment for other schools", "660903": "School books for day cares and nurseries", "660905": "School books for other schools", "660906": "School supplies and equipment", "670110": "College tuition", "670210": "Elementary and high school tuition", "670410": "Vocational and technical school tuition", "670901": "Other schools' tuition", "670902": "Other school expenses including rentals", "670903": "Test preparation and tutoring services", "EDUCATN": "Education", "270310": "Cable and satellite television services", "270311": "Satellite radio services", "310140": "Televisions", "310210": "VCR's and video disc players", "310220": "Video cassettes, tapes, and discs", "310230": "Video game hardware and software", "310231": "Video game software", "310232": "Video game hardware and accessories", "310240": "Streaming, downloading video", "310243": "Rental, streaming, and downloading videos", "310311": "Radios (thru Q20131)", "310312": "Phonographs", "310313": "Tape recorders and players", "310314": "Personal digital audio players", "310315": "Digital media players and recorders", "310316": "Stereos, radios, speakers, and sound components including those in vehicles", "310320": "Sound components and component systems", "310331": "Miscellaneous sound equipment", "310332": "Sound equipment accessories", "310334": "Satellite dishes", "310335": "Miscellaneous video equipment", "310340": "CDs, records, and audio tapes", "310350": "Streaming and downloading audio", "310400": "Applications, games, and ringtones for handheld devices", "340610": "Repair of televisions, radio, and sound equipment", "340902": "Rental of televisions", "340905": "Rental of VCR, radio, and sound equipment", "520901": "Docking and landing fees", "520904": "Rental noncamper trailers", "520907": "Boat and trailer rentals on out of town trips", "600110": "Outboard motors", "600121": "Boats without motor and boat trailers", "600122": "Trailer and other attachable campers", "600132": "Purchase of boats with motor", "600141": "Purchase of motorized campers", "600142": "Purchase of other vehicles", "600210": "Athletic gear, game tables, and exercise equipment", "600310": "Bicycles", "600311": "Bike and E-scooter sharing or rental", "600315": "Scooters and other single-rider transportation", "600410": "Camping equipment", "600420": "Hunting and fishing equipment", "600430": "Winter sports equipment", "600901": "Water sports equipment", "600902": "Other sports equipment", "600903": "Global positioning system devices", "610110": "Toys, games, arts and crafts, and tricycles", "610120": "Playground equipment", "610130": "Musical instruments and accessories", "610140": "Stamp and coin collecting", "610210": "Film", "610220": "Other photographic supplies", "610230": "Photographic equipment", "610310": "Pet food", "610320": "Pet purchase, supplies, and medicine", "610900": "Recreation expenses on out of town trips", "610901": "Fireworks", "610902": "Souvenirs", "610903": "Visual goods", "620111": "Social, recreation, and health club memberships", "620121": "Fees for participant sports", "620122": "Participant sports on out of town trips", "620211": "Movie, theater, amusement parks, and other", "620212": "Movie and other admissions on out of town trips", "620213": "Plays, theater, operas, and concerts", "620214": "Movies, parks, museums", "620215": "Tickets to movies", "620216": "Tickets to parks or museums", "620221": "Admission to sporting events", "620222": "Admission to sports events on out of town trips", "620310": "Fees for recreational lessons", "620320": "Photographer fees", "620330": "Photo processing", "620410": "Pet services", "620420": "Vet services", "620903": "Other entertainment services on out of town trips", "620904": "Rental and repair of musical instruments", "620905": "Repair and rental of photographic equipment", "620906": "Rental of boats", "620908": "Rental and repair of miscellaneous sports equipment", "620909": "Rental of campers on out of town trips", "620912": "Rental of video cassettes, tapes, films, and discs", "620913": "Pinball and electronic video games", "620916": "Rental of computer and video game hardware and software", "620917": "Rental of video hardware/accessories", "620918": "Rental of video software", "620919": "Rental of other vehicles on out of town trips", "620921": "Rental of motorized campers", "620922": "Rental of other recreational vehicles", "620930": "Online gaming services", "680310": "Live entertainment for catered affairs", "680320": "Rental of party supplies for catered affairs", "690320": "Installation of televisions", "690330": "Installation of satellite television equipment", "690340": "Installation of sound systems", "690350": "Installation of other video equipment or sound systems", "ENTEROTH": "Other entertainment supplies, equipment, and services", "ENTRTAIN": "Entertainment", "FEESADM": "Fees and admissions", "PETS": "Pets", "PETSPLAY": "Pets, toys, hobbies, and playground equipment", "PHOTOEQ": "Photographic equipment, supplies, and services", "PWRSPVEH": "Motorized recreational vehicles", "RECEQUIP": "Sports, recreation, and exercise equipment", "RNTSPVEH": "Rental of recreational vehicles", "TOYS": "Toys, hobbies, and playground equipment", "TVAUDIO": "Audio and visual equipment and services", "UNMTRBOT": "Un-motored recreational vehicles", "010110": "Flour", "010120": "Prepared flour mixes", "010210": "Ready-to-eat and cooked cereals", "010310": "Rice", "010320": "Pasta, cornmeal, and other cereal products", "020110": "White bread", "020210": "Bread, other than white", "020310": "Biscuits and rolls", "020410": "Cakes and cupcakes", "020510": "Cookies", "020610": "Crackers", "020620": "Bread and cracker products", "020710": "Sweetrolls, coffee cakes, and doughnuts", "020810": "Frozen and refrigerated bakery products", "020820": "Pies, tarts, and turnovers", "030110": "Ground beef", "030210": "Chuck roast", "030310": "Round roast", "030410": "Other roast", "030510": "Round steak", "030610": "Sirloin steak", "030710": "Other steak", "030810": "Other beef", "040110": "<PERSON>", "040210": "Pork chops", "040310": "Ham, not canned", "040410": "Other pork", "040510": "Sausage", "040610": "Canned ham", "050110": "Frankfurters", "050210": "Bologna, liverwurst, and salami", "050310": "Other lunchmeats", "050410": "Lamb and organ meats", "050900": "Mutton, goat, and game", "060110": "Fresh and frozen whole chickens", "060210": "Fresh and frozen chicken parts", "060310": "Other poultry", "070110": "Canned fish and seafood", "070230": "Fresh fish and shellfish", "070240": "Frozen fish and shellfish", "080110": "Eggs", "090110": "Fresh milk, all types", "090210": "Cream", "100110": "Butter", "100210": "Cheese", "100410": "Ice cream and related products", "100510": "Miscellaneous dairy products", "110110": "Apples", "110210": "Banana<PERSON>", "110310": "Oranges", "110410": "Other fresh fruits", "110510": "Citrus fruits, excluding oranges", "120110": "Potatoes", "120210": "Lettuce", "120310": "Tomatoes", "120410": "Other fresh vegetables", "130110": "Frozen orange juice", "130121": "Frozen fruits", "130122": "Frozen fruit juices", "130211": "Fresh fruit juice", "130212": "Canned and bottled fruit juice", "130310": "Canned fruits", "130320": "Dried fruits", "140110": "Frozen vegetables", "140210": "Canned beans", "140220": "Canned corn", "140230": "Canned miscellaneous vegetables", "140310": "Dried processed vegetables", "140320": "Dried peas", "140330": "Dried beans", "140340": "Dried miscellaneous vegetables", "140410": "Frozen vegetable juices", "140420": "Fresh and canned vegetable juices", "150110": "Candy and chewing gum", "150211": "Sugar", "150212": "Artificial sweeteners", "150310": "Jams, preserves, and other sweets", "160110": "Margarine", "160211": "Fats and oils", "160212": "Salad dressings", "160310": "Nondairy cream and imitation milk", "160320": "Peanut butter", "170110": "Cola", "170210": "Other carbonated drinks", "170310": "Roasted coffee", "170410": "Instant and freeze dried coffee", "170510": "Noncarbonated fruit flavored drinks, including non-frozen lemonade", "170520": "Tea", "170531": "Other noncarbonated beverages and ice", "170532": "Bottled water", "170533": "Sports drinks", "180110": "Canned and packaged soups", "180210": "Frozen meals", "180220": "Other frozen prepared foods", "180310": "Potato chips and other snacks", "180320": "Nuts", "180410": "Salt, spices, and other seasonings", "180420": "Olives, pickles, and relishes", "180510": "Sauces and gravies", "180520": "Baking needs and miscellaneous products", "180611": "Prepared salads", "180612": "Prepared desserts", "180620": "Baby food", "180710": "Miscellaneous prepared foods", "180720": "Vitamin supplements", "190111": "Lunch at fast food, carry out, delivery, concession stands, buffets, and cafeterias (other than employer and school cafeteria)", "190112": "Lunch at full service restaurants", "190113": "Lunch at vending machines and mobile vendors", "190114": "Lunch at employer and school cafeterias", "190211": "Dinner at fast food, carry out, delivery, concession stands, buffets, and cafeterias (other than employer and school cafeteria)", "190212": "Dinner at full service restaurants", "190213": "Dinner at vending machines and mobile vendors", "190214": "Dinner at employer and school cafeterias", "190311": "Snacks and nonalcoholic beverages at fast food, carry out, delivery, concession stands, buffets and cafeterias (other than employer and school cafeteria)", "190312": "Snacks and nonalcoholic beverages at full service restaurants", "190313": "Snacks and nonalcoholic beverages at vending machines and mobile vendors", "190314": "Snacks and nonalcoholic beverages at employer and school cafeterias", "190321": "Breakfast and brunch at fast food, carry out, delivery, concession stands, buffets, and cafeterias (other than employer and school cafeteria)", "190322": "Breakfast and brunch at full service restaurants", "190323": "Breakfast and brunch at vending machines and mobile vendors", "190324": "Breakfast and brunch at employer and school cafeterias", "190400": "Food and nonalcoholic beverages at fast food", "190500": "Food and nonalcoholic beverages at full service restaurants", "190600": "Food and nonalcoholic beverages at vending machines and mobile vendors", "190700": "Food and nonalcoholic beverages at employer", "190901": "Food or board at school", "190902": "Catered affairs", "190903": "Food on out of town trips", "190904": "Food prepared by consumer unit on out of town trips", "200112": "Nonalcoholic beer", "790430": "School lunches", "800700": "Meals as pay", "ANIMAL": "Meats, poultry, fish, and eggs", "BAKERY": "Bakery products", "BEEF": "<PERSON><PERSON>", "BREAD": "Bread", "BRKFBRUN": "Breakfast and brunch", "CANDVEG": "Canned and dried vegetables and juices", "CERBAKRY": "Cereals and bakery products", "CEREAL": "Cereals and cereal products", "CHICKEN": "Fresh and frozen chickens", "COFFEE": "Coffee", "CONDMNTS": "Condiments and seasonings", "CRAKCOOK": "Cookies and crackers", "DAIRY": "Dairy products", "DINNER": "Dinner", "FATSOILS": "Fats and oils", "FISHSEA": "Fish and seafood", "FOODAWAY": "Food away from home", "FOODHOME": "Food at home", "FOODTOTL": "Food", "FRESHVEG": "Fresh vegetables", "FRSHFRUT": "Fresh fruits", "FRUITVEG": "Fruits and vegetables", "FRZNFRUT": "Frozen fruits and fruit juices", "FRZNPREP": "Frozen prepared foods", "HAM": "Ham", "LAMBOTHR": "Lamb, organ meats, and others", "LNCHMEAT": "Lunch meats (cold cuts)", "LUNCH": "Lunch", "MILKCRM": "Fresh milk and cream", "MISCFOOD": "Miscellaneous foods", "NALCBEVG": "Nonalcoholic beverages", "OTHBAKRY": "Other bakery products", "OTHDAIRY": "Other dairy products", "OTHRFOOD": "Other food at home", "OTHRMEAT": "Other meats", "OTHRPREP": "Other canned and packaged prepared foods", "PORK": "Pork", "POULTRY": "Poultry", "PROCFRUT": "Processed fruits", "PROCVEG": "Processed vegetables", "RESTCOAO": "Meals at restaurants, carry out, delivery, and other", "ROAST": "Roast", "SNACKS": "Potato chips, nuts, and other snacks", "SNKNABEV": "Snacks and nonalcoholic beverages", "STEAK": "Steak", "SWEETS": "Sugar and other sweets", "540000": "Prescription drugs", "550110": "Eyeglasses and contact lenses", "550210": "Nonprescription drugs", "550310": "Topicals and dressings", "550320": "Medical equipment for general use", "550330": "Supportive and convalescent medical equipment", "550340": "Hearing aids", "550410": "Nonprescription vitamins", "560110": "Physician's services", "560210": "Dental services", "560310": "Eyecare services", "560330": "Lab tests and x-rays", "560400": "Service by professionals other than physician", "560410": "Non physician services inside the home", "560420": "Non physician services outside the home", "570111": "Hospital room and services", "570220": "Care in convalescent or nursing home", "570230": "Other medical care services", "570240": "Medical care in retirement community", "570901": "Rental of medical equipment", "570902": "Repair of medical equipment", "570903": "Rental of supportive and convalescent medical equipment", "580111": "Traditional fee for service health plan (not BCBS)", "580112": "Traditional fee for service health plan (BCBS)", "580113": "Preferred provider health plan (not BCBS)", "580114": "Preferred provider health plan (BCBS)", "580115": "Fee for service health plan (not BCBS)", "580116": "Fee for service health plan (BCBS)", "580311": "Health maintenance organization (not BCBS)", "580312": "Health maintenance organization (BCBS)", "580400": "Long term care insurance", "580401": "Long term care insurance (not BCBS)", "580402": "Long term care insurance (BCBS)", "580411": "Dental care insurance (not BCBS)", "580412": "Dental care insurance (BCBS)", "580421": "Prescription drug insurance (not BCBS)", "580422": "Prescription drug insurance (BCBS)", "580431": "Vision care insurance (not BCBS)", "580432": "Vision care insurance (BCBS)", "580441": "Other single service insurance (not BCBS)", "580442": "Other single service insurance (BCBS)", "580901": "Medicare payments", "580903": "Commercial medicare supplement (not BCBS)", "580904": "Commercial medicare supplement (BCBS)", "580905": "Other health insurance (not BCBS)", "580906": "Other health insurance (BCBS)", "580907": "Medicare prescription drug premiums", "580908": "Medicaid premiums", "580909": "Tricare/military premiums", "580910": "Children's Health Insurance Program (CHIP) premiums", "640430": "Adult diapers", "BCBS": "Blue Cross, Blue Shield", "COMEDOTH": "Commercial medicare supplements and other health insurance", "COMHLTIN": "Commercial health insurance", "DRUGS": "Drugs", "HEALTH": "Healthcare", "HLTHINSR": "Health insurance", "MEDSERVS": "Medical services", "MEDSUPPL": "Medical supplies", "210110": "Rent", "210210": "Lodging on out of town trips", "210310": "Housing while attending school", "210901": "Ground rent", "210902": "Ground rent", "220121": "Homeowners insurance", "220122": "Homeowners insurance", "220211": "Property taxes", "220212": "Property taxes", "220311": "Mortgage interest", "220312": "Mortgage interest", "220313": "Interest paid, home equity loan", "220314": "Interest paid, home equity loan", "220901": "Parking", "220902": "Parking", "230112": "Painting and papering", "230113": "Plumbing and water heating", "230114": "Heat, a/c, and electrical work", "230115": "Roofing and gutters", "230117": "Dishwashers (built-in), garbage disposals, and range hoods (renter)", "230118": "Dishwashers (built-in), garbage disposals, and range hoods (owned home)", "230121": "Repair and replacement of hard surface flooring", "230122": "Repair and replacement of hard surface flooring", "230123": "Repair and replacement of hard surface flooring", "230133": "Wall-to-wall carpet (replacement) (owned home)", "230134": "Wall-to-wall carpet (renter)", "230141": "Repair of built-in appliances", "230142": "Repair of built-in appliances", "230150": "Repair or maintenance services", "230151": "Other repair and maintenance services", "230152": "Repair and remodeling services", "230901": "Property management", "230902": "Property management", "240111": "Paint, wallpaper, and supplies", "240112": "Paints, wallpaper, and supplies", "240113": "Paints, wallpaper, and supplies", "240121": "Tools and equipment for painting and wallpapering", "240122": "Tools and equipment for painting and wallpapering", "240123": "Tools and equipment for painting and wallpapering", "240211": "Materials for plastering, panels, roofing, and gutters, etc.", "240212": "Materials for plaster, panel, siding, windows, doors, screens, and awnings", "240213": "Materials and equipment for roofs and gutters", "240214": "Materials for plastering, paneling, roofing, gutters, downspouts, siding, windows, doors, screens, and awnings", "240221": "Materials for patio, walk, fence, driveway, masonry, brick and stucco work", "240222": "Materials for patio, walk, fence, driveway, masonry, brick, and stucco work", "240223": "Material for patio, walk, fence, drive, masonry, brick, and stucco", "240311": "Plumbing supplies and equipment", "240312": "Plumbing supplies and equipment", "240313": "Plumbing supplies and equipment", "240321": "Electrical supplies, heating, and cooling equipment", "240322": "Electrical supplies, heating, and cooling equipment", "240323": "Electrical supplies, heating, and cooling equipment", "250111": "Fuel oil (renter)", "250112": "Fuel oil (owned home)", "250113": "Fuel oil (owned vacation)", "250114": "Fuel oil (rented vacation)", "250211": "Gas, bottled/tank (renter)", "250212": "Gas, bottled/tank (owned home)", "250213": "Gas, bottled/tank (owned vacation)", "250214": "Gas, bottled/tank (rented vacation)", "250911": "Coal, wood, and other fuels (renter)", "250912": "Coal, wood, and other fuels (owned home)", "250913": "Coal, wood, and other fuels (owned vacation)", "250914": "Coal, wood, and other fuels (rented vacation)", "260111": "Electricity (renter)", "260112": "Electricity (owned home)", "260113": "Electricity (owned vacation)", "260114": "Electricity (rented vacation)", "260211": "Natural gas (renter)", "260212": "Natural gas (owned home)", "260213": "Natural gas (owned vacation)", "260214": "Natural gas (rented vacation)", "270101": "Residential telephone/pay phones", "270102": "Cellular phone service", "270104": "Phone cards", "270105": "Voice over IP service", "270106": "Residential telephone including VOIP", "270211": "Water/sewer maintenance (renter)", "270212": "Water/sewer maintenance (owned home)", "270213": "Water/sewer maintenance (owned vacation)", "270214": "Water/sewer maintenance (rented vacation)", "270411": "Trash/garbage collection (renter)", "270412": "Trash/garbage collection (owned home)", "270413": "Trash/garbage collection (owned vacation)", "270414": "Trash/garbage collection (rented vacation)", "270901": "Septic tank cleaning (renter)", "270902": "Septic tank cleaning (owned home)", "270903": "Septic tank cleaning (owned vacation)", "270904": "Septic tank cleaning (rented vacation)", "280110": "Bathroom linens", "280120": "Bedroom linens", "280130": "Kitchen and dining room linens", "280140": "Kitchen, dining room, and other linens", "280210": "Curtains and draperies", "280220": "Slipcovers and decorative pillows", "280230": "Sewing materials for slipcovers, curtains, other", "280900": "Other linens (thru Q20124)", "290110": "Mattresses and springs", "290120": "Other bedroom furniture", "290210": "<PERSON><PERSON><PERSON>", "290310": "Living room chairs", "290320": "Living room tables", "290410": "Kitchen and dining room furniture", "290420": "Infants' furniture", "290430": "Outdoor furniture", "290440": "Wall units, cabinets, and other furniture", "300111": "Refrigerators and freezers (renter)", "300112": "Refrigerators and freezers (owned home)", "300211": "Washing machines (renter)", "300212": "Washing machines (owned home)", "300216": "Clothes washers or dryers (renter)", "300217": "Clothes washers or dryers (owned home)", "300221": "Clothes dryers (renter)", "300222": "Clothes dryers (owned home)", "300311": "Cooking stoves and ovens (renter)", "300312": "Cooking stoves and ovens (owned home)", "300321": "Microwave ovens (renter)", "300322": "Microwave ovens (owned home)", "300331": "Portable dishwashers (renter)", "300332": "Portable dishwashers (owned home)", "300411": "Window air conditioners (renter)", "300412": "Window air conditioners (owned home)", "300900": "Miscellaneous household appliances", "320111": "Floor coverings, nonpermanent", "320120": "Window coverings", "320130": "Infants' equipment", "320140": "Laundry and cleaning equipment", "320150": "Outdoor equipment", "320163": "Wall-to-wall carpet (replacement)(renter)", "320220": "Lamps and lighting fixtures", "320221": "Lamps, lighting fixtures, and ceiling fans", "320232": "Telephones and accessories", "320233": "Clocks and other household decorative items", "320310": "Plastic dinnerware", "320320": "China and other dinnerware", "320330": "Flatware", "320340": "Glassware", "320345": "Dinnerware, glassware, and serving pieces", "320350": "Silver serving pieces", "320360": "Other serving pieces", "320370": "Nonelectric cookware", "320380": "Tableware and nonelectric kitchenware", "320410": "Lawn and garden equipment", "320420": "Power tools", "320430": "Other hardware", "320511": "Electric floor cleaning equipment", "320512": "Sewing machines", "320521": "Small electric kitchen appliances", "320522": "Portable heating and cooling equipment", "320611": "Material for insulation, other maintenance, and repair", "320612": "Material for insulation, other maintenance, and repair", "320613": "Material for insulation, other maintenance, and repair", "320621": "Material for hard surface flooring", "320622": "Materials for hard surface flooring, repair and", "320623": "Materials for hard surface flooring (thru", "320624": "Flooring installation, repair, and replacement", "320625": "Flooring installation, repair, and replacement", "320626": "Flooring installation, repair, and replacement", "320631": "Material for landscape maintenance", "320632": "Materials for landscaping maintenance", "320633": "Materials for landscaping maintenance", "320901": "Office furniture for home use", "320902": "Hand tools", "320903": "Indoor plants and fresh flowers", "320904": "Closet and storage items", "320905": "Miscellaneous household equipment and parts", "330110": "Soaps and detergents", "330210": "Other laundry cleaning products", "330310": "Cleansing and toilet tissue, paper towels, and napkins", "330410": "Stationery, stationery supplies, and gift wrap", "330510": "Miscellaneous household products", "330511": "Termite/pest control products", "330610": "Lawn and garden supplies", "340110": "Postage", "340120": "Delivery services", "340210": "Babysitting and childcare", "340211": "and child care in your own home (thru", "340212": "and child care in someone else''s home", "340310": "Housekeeping services", "340410": "Gardening and lawn care service", "340420": "Water softening services", "340510": "Moving, storage, and freight", "340520": "Household laundry and dry cleaning, sent out (nonclothing) not coin-operated", "340530": "Coin-operated household laundry and dry cleaning (nonclothing)", "340620": "Appliance repair, including service center", "340630": "Reupholstering, furniture repair", "340901": "Repairs/rentals of lawn and garden equipment, hand or power tools, and other household equipment", "340903": "Other home services", "340904": "Rental of furniture", "340906": "Care for elderly, invalids, handicapped, etc.", "340907": "Appliance rentals", "340908": "Rental of office equipment for nonbusiness use", "340910": "Adult day care centers", "340911": "Management and upkeep services for security", "340912": "Management and upkeep services for security", "340913": "Repair of miscellaneous household equipment and furnishings", "340914": "Services for termite/pest control", "340915": "Home security system service fees", "350110": "Tenant's insurance", "430130": "Luggage", "670310": "Day care centers, nurseries, and preschools", "690111": "Computers and computer hardware for nonbusiness use", "690112": "Computer software and accessories for nonbusiness use", "690113": "Repair of computer systems for nonbusiness use", "690114": "Computer information services (internet)", "690115": "Personal digital assistants", "690116": "Internet services away from home", "690117": "Portable memory", "690119": "Computer software", "690120": "Computer accessories", "690210": "Telephone answering devices", "690230": "Business equipment for home use", "690241": "Smoke alarms (renter)", "690242": "Smoke alarms (owned home)", "690243": "Smoke alarms (owned vacation)", "690244": "Other household appliances (renter)", "690245": "Other household appliances (owned home)", "690310": "Installation of computers", "790690": "Construction materials for jobs not started", "800710": "Rent as pay", "880110": "Interest paid, home equity line of credit", "880310": "Interest paid, home equity line of credit", "990900": "Rental and installation of dishwashers, range hoods, and garbage disposals", "990920": "Materials for additions, finishing basements, and remodeling rooms", "990930": "Materials to finish basements, remodel rooms, or build patios or walkways", "990940": "Material for finishing basements and remodeling rooms", "BOTTLGAS": "Bottled gas", "CLWDOTFL": "Coal, wood, and other fuels", "ELECTRIC": "Electricity", "FLOORCOV": "Floor coverings", "FUELOIL": "Fuel oil", "FURNITUR": "Furniture", "HHFURNSH": "Household furnishings and equipment", "HHOPER": "Household operations", "HHOTHXPN": "Other household expenses", "HHPERSRV": "Personal services", "HHTXTILE": "Household textiles", "HKPGOTHR": "Other household products", "HKPGSUPP": "Housekeeping supplies", "HOUSING": "Housing", "HOUSWARE": "Housewares", "LAUNDRY": "Laundry and cleaning supplies", "MAJAPPL": "Major appliances", "MISCHHEQ": "Miscellaneous household equipment", "NATRLGAS": "Natural gas", "OTHLODGE": "Other lodging", "OTHRFUEL": "Fuel oil and other fuels", "OWNDWELL": "Owned dwellings", "OWNEXPEN": "Maintenance, repairs, insurance, and other expenses", "OWNMISC": "Miscellaneous supplies and equipment", "OWNMNAGE": "Property management and security", "OWNMORTG": "Mortgage interest and charges", "OWNREPSP": "Maintenance and repair commodities", "OWNREPSV": "Maintenance and repair services", "OWNVMORT": "Mortgage interest and charges", "OWVEXPEN": "Maintenance, insurance, and other expenses", "OWVHOME": "Owned vacation homes", "OWVMISC": "Miscellaneous supplies and equipment", "OWVMNAGE": "Property management and security", "OWVREPSP": "Maintenance and repair commodities", "OWVREPSV": "Maintenance and repair services", "PHONE": "Telephone services", "POSTAGE": "Postage and stationery", "RESPHONE": "Residential phone service, VOIP, and phone cards", "RNTCARPT": "Wall-to-wall carpeting (renter)", "RNTDWELL": "Rented dwellings", "RNTEXPEN": "Maintenance, insurance, and other expenses", "RNTMISC": "Miscellaneous supplies and equipment", "RNTREPSP": "Maintenance and repair commodities", "RNTREPSV": "Maintenance and repair services", "SEPTANK": "Septic tank cleaning", "SEWER": "Water and sewerage maintenance", "SHELTER": "Shelter", "SMAPPHWR": "Small appliances and miscellaneous housewares", "SMLLAPPL": "Small appliances", "TRASH": "Trash and garbage collection", "UTILS": "Utilities, fuels, and public services", "WATER": "Water and other public services", "980070": "Income after taxes", "980071": "Income after taxes", "INCAFTTX": "Income after taxes", "900000": "Wages and salaries", "900010": "Net business income", "900020": "Net farm income", "900030": "Social Security and railroad retirement income", "900040": "Pensions and annuities", "900050": "Dividends, royalties, estates, trusts", "900060": "Roomer and boarder income", "900070": "Other rental income", "900080": "Interest", "900090": "Supplemental Security Income", "900100": "Unemployment compensation", "900110": "Workers'' compensation and veterans'' benefits", "900120": "Public assistance", "900131": "Child support payments", "900132": "Other regular contributions including alimony", "900140": "Other income", "900150": "Supplementary Nutrition Assistance Program (SNAP)", "900160": "Self-employment income", "900170": "Retirement, survivors, and disability income", "900180": "Interest and dividends", "900190": "Net room/rental income", "900200": "Royalty, estate, and trust income", "900210": "Other regular income", "INCBEFTX": "Income before taxes", "INDIVRNT": "Interest, dividends, rental income, and other property income", "OTHBNFTS": "Unemployment and workers' compensation, veterans' benefits", "OTHREGIN": "Unemployment and workers' compensation, veterans' benefits, and regular contributions for support", "OTHRINC": "Other income", "REGCONT": "Regular contributions for support", "RETIRINC": "Social Security, private and government retirement", "SFEMPINC": "Self-employment income", "WELFARE": "Public assistance, Supplemental Security Income, Supplementary Nutrition Assistance Program (SNAP)", "002120": "Other non-health insurance", "700110": "Life, endowment, annuity, and other personal insurance", "800910": "Deductions for government retirement", "800920": "Deductions for railroad retirement", "800931": "Deductions for private pensions", "800932": "Non-payroll deposit to retirement plans", "800940": "Deductions for Social Security", "INSPENSN": "Personal insurance and pensions", "LIFEINSR": "Life and other personal insurance", "PENSIONS": "Pensions and Social Security", "005420": "Finance, late, and interest charges for credit cards", "005620": "Finance, late, and interest charges for other loans", "620112": "Credit card memberships", "620115": "Shopping club membership fees", "620925": "Miscellaneous fees", "620926": "Lotteries and pari-mutuel losses", "680110": "Legal fees", "680140": "Funeral expenses", "680210": "Safe deposit box rental", "680220": "Checking accounts, other bank service charges", "680901": "Cemetery lots, vaults, and maintenance fees", "680902": "Accounting fees", "680903": "Miscellaneous personal services", "680904": "Dating services", "680905": "Vacation clubs", "710110": "Finance charges excluding mortgage and vehicle", "790600": "Expenses for other properties", "880210": "Interest paid, home equity line of credit (other property)", "900002": "Occupational expenses", "MISC": "Miscellaneous", "640110": "Haircare products", "640120": "Nonelectric articles for hair", "640130": "Wigs and hairpieces", "640210": "Oral hygiene products and articles", "640220": "Shaving needs", "640310": "Cosmetics, perfume, and bath preparations", "640410": "Deodorants, feminine hygiene, and miscellaneous personal care", "640420": "Electric personal care appliances", "650310": "Personal care services", "650900": "Repair of personal care appliances", "PERSCARE": "Personal care products and services", "PERSPROD": "Personal care products", "PERSSERV": "Personal care services", "950001": "Federal income tax refunds", "950002": "Federal income tax deducted", "950003": "Additional federal income tax paid", "950004": "Federal income tax (imputed)", "950011": "State and local income tax refunds", "950012": "State and local income tax deducted", "950013": "Additional state and local income tax paid", "950014": "State and local income taxes (imputed)", "950021": "Other taxes", "950022": "Personal property taxes", "950023": "tax refunds", "950024": "Vehicle personal property taxes", "950040": "Stimulus payment", "FEDTAXES": "Federal income taxes", "OTHRTAX": "Other taxes", "PERSTAX": "Personal taxes (contains some imputed values)", "STATETAX": "State and local income taxes", "590110": "Newspapers", "590210": "Magazines", "590220": "Books through book clubs", "590230": "Books, digital books, or book subscriptions", "590310": "Newspaper, magazine by subscription", "590410": "Newspaper, magazine non-subscription", "590900": "Newsletters", "660310": "Encyclopedias and other sets of reference books", "690118": "Digital book readers", "READING": "Reading", "980010": "Number of People in CU", "980020": "Age of reference person", "980030": "Number of Earners", "980040": "Vehicles (owned)", "980050": "Number of Children under 18", "980060": "Adults 65 and older", "980360": "Vehicles (leased)", "INCAFTAX": "Income after taxes", "INCBFTAX": "Income before taxes", "TITLEACU": "Average number in consumer unit:", "VEHICLES": "Number of Vehicles", "790920": "Reduction of mortgage principal (other property)", "800721": "Estimated market value of owned home", "900220": "Lump sum payment received", "910000": "Lump sum receipts", "910010": "Money from sale of household furnishings, etc.", "910020": "Refund from overpayment on Social Security", "910030": "Refunds from insurance policies", "910040": "Refunds from property taxes", "910041": "Lump sum child support payment", "910042": "Monthly transit subsidy", "910050": "Estimated monthly rental value of owned home", "910101": "Estimated monthly rental value of vacation home not available for rent", "910102": "Estimated monthly rental value of vacation home available for rent", "910103": "Estimated annual rental value of timeshare", "920010": "Market value of all savings accounts", "920020": "Market value of all checking accounts", "920030": "Market value of all U.S. savings bonds", "920040": "Market value of all securities", "MRTPRINP": "Mortgage principal paid on owned property", "OTHRMONY": "Other money receipts", "VALUASST": "Market value of financial assets", "980210": "Percent Men reference persons", "980220": "Percent Women reference persons", "980230": "Percent Homeowner with mortgage", "980240": "Percent Homeowner without mortgage", "980260": "<PERSON><PERSON>", "980270": "Percent Black or African American", "980281": "White", "980282": "Asian", "980283": "All other races not including Black or African-American", "980285": "Percent Hispanic or Latino", "980286": "Percent Not Hispanic or Latino", "980290": "Percent Elementary (1-8)", "980300": "Percent High school (9-12)", "980310": "Percent College", "980320": "<PERSON><PERSON> attended school and other", "980330": "At least one vehicle owned", "980340": "At least one vehicle leased", "980350": "At least one vehicle owned or leased", "HOMEOWN": "Percent Homeowner", "TITLEEDU": "Education of reference person:", "TITLEHOP": "Hispanic or Latino origin of reference person:", "TITLEHT": "Housing tenure:", "TITLERRP": "Race of reference person:", "TITLESRP": "Reference person:", "WHTNDOTH": "Percent White, Asian, and All Other Races, not including African American", "630110": "Cigarettes", "630210": "Other tobacco products", "630220": "Smoking accessories", "630900": "Marijuana", "TOBACCO": "Tobacco products and smoking supplies", "TOTALEXP": "Average annual expenditures", "450110": "New cars", "450210": "New trucks", "450220": "New motorcycles", "450310": "Car lease payments", "450313": "Cash downpayment (car lease)", "450314": "Termination fee (car lease)", "450350": "Car/truck lease payments", "450351": "Extra fees for car/truck lease", "450352": "Trade in allowance for car/truck lease", "450353": "Cash down payment car/truck lease", "450354": "Termination fee for car/truck lease", "450410": "Truck lease payments", "450413": "Cash downpayment (truck lease)", "450414": "Termination fee (truck lease)", "450900": "New aircraft", "460110": "Used cars", "460901": "Used trucks", "460902": "Used motorcycles", "460903": "Used aircraft", "470111": "Gasoline", "470112": "Diesel fuel", "470113": "Gasoline on out of town trips", "470114": "Alternative fuels", "470211": "Motor oil", "470212": "Motor oil on out of town trips", "470220": "Coolant, brake fluid, transmission fluid, and other additives", "470311": "Electric vehicle charging", "480110": "Tires - purchased, replaced, and installed", "480212": "Vehicle products and cleaning services", "480213": "Parts, equipment, and accessories", "480214": "Vehicle audio equipment", "480215": "Vehicle video equipment", "490000": "Miscellaneous auto repair and servicing", "490110": "Body work and painting", "490211": "Clutch, transmission repair", "490212": "Drive shaft and rear-end repair", "490221": "Brake work, including adjustments", "490231": "Repair to steering or front-end", "490232": "Repair to engine cooling system", "490300": "Vehicle or engine repairs", "490311": "Motor tune up", "490312": "Lube, oil change, and oil filters", "490313": "Front end alignment, wheel balance, and rotation", "490314": "Shock absorber replacement", "490316": "Gas tank repair and replacement", "490318": "Repair tires and other repair work", "490319": "Vehicle air conditioning repair", "490411": "Exhaust system repair", "490412": "Electrical system repair", "490413": "Motor repair, replacement", "490900": "Auto repair service policy", "500110": "Vehicle insurance", "510110": "Automobile finance charges", "510901": "Truck finance charges", "510902": "Motorcycle and plane finance charges", "520111": "Vehicle registrations, state", "520112": "Vehicle registrations, local", "520310": "Drivers' licenses", "520410": "Vehicle inspections", "520511": "Auto rental", "520512": "Auto rental, out-of-town trips", "520516": "Auto/truck rental", "520517": "Auto/truck rental on out of town trips", "520521": "Truck rental", "520522": "Truck rental, out-of-town trips", "520531": "Parking fees in home city, excluding residence", "520532": "Parking fees on out of town trips", "520541": "Tolls or electronic toll passes", "520542": "Tolls on out of town trips", "520550": "Towing charges", "520560": "Global positioning services", "520902": "Motorcycle rental", "520903": "Aircraft rental", "520905": "Motorcycle rental on out of town trips", "520906": "Aircraft rental on out of town trips", "530110": "Airline fares", "530210": "Intercity bus fares", "530311": "Intracity mass transit fares", "530312": "Local transportation on out of town trips", "530411": "Taxi fares and limousine services on trips", "530412": "Taxi fares and limousine services", "530510": "Intercity train fares", "530901": "Ship fares", "530902": "School bus", "620113": "Automobile service clubs", "620114": "Automobile service clubs and GPS services", "850300": "Other vehicle finance charges", "CAREPAIR": "Maintenance and repairs", "GASOIL": "Gasoline, other fuels, and motor oil", "LEASVEH": "Leased vehicles", "NEWCARS": "Cars and trucks, new", "OTHVEHCL": "Other vehicles", "PARKING": "Parking fees", "PUBTRANS": "Public and other transportation", "RENTVEH": "Rented vehicles", "TRANS": "Transportation", "USEDCARS": "Cars and trucks, used", "VEHFINCH": "Vehicle finance charges", "VEHOTHXP": "Other vehicle expenses", "VEHPURCH": "Vehicle purchases (net outlay)", "VEHRENT": "Leased and rented vehicles", "VEHRNTLC": "Vehicle rental, leases, licenses, and other charges"}, "demographics_code": {"LB01": "Quintiles of income before taxes", "LB02": "Income before taxes", "LB04": "Age of reference person", "LB05": "Size of consumer unit", "LB06": "Composition of consumer unit", "LB07": "Number of earners", "LB09": "Race of reference person", "LB10": "Hispanic or Latino origin of reference person", "LB11": "Region of residence", "LB12": "Occupation of reference person", "LB13": "Education of reference person", "LB14": "Highest education level of any member", "LB15": "Deciles of income before taxes", "LB16": "Generation of reference person", "LB17": "Housing tenure", "LB18": "Type of area", "LB19": "Type of area", "LB20": "Population size of area of residence", "LB21": "Selected age of reference person"}, "characteristics_code": {"01": "All Consumer Units", "02": "Under 30 years", "03": "30 and older", "04": "Under 50 years", "05": "50 and older", "06": "Under 55 years", "A1": "Five people in consumer unit", "A2": "Six or more people in consumer unit", "07": "55 and older", "08": "Under 65 years", "09": "65 and older", "10": "Ninth 10 percent", "11": "Highest 10 percent", "12": "$70,000 to $79,999 before tax income(from 2003)", "13": "$80,000 to $99,999 before tax income(from 2003)", "14": "Before tax income of $100,000 and over(from 2003)", "15": "$100,000 to $119,999 before tax income(from 2003)", "16": "$120,000 to $149,999 before tax income(from 2003)", "17": "Before tax income of $150,000 and over(from 2003)", "18": "Less than $15,000", "19": "$15,000 to $29,999", "20": "$70,000 to $99,999", "21": "$100,000 to $149,999", "22": "$150,000 to $199,999", "23": "$200,000 and more"}, "process_code": {"M": "Means"}, "footnote_code": {"1": "Components of income and taxes are derived from \"complete income reporters\" only through 2003; (see glossary). Beginning in 2004 income imputation was implemented. As a result, all consumer units are considered to be complete income reporters.", "2": "Expenses for other properties was moved from \"Other lodging\" to \"Miscellaneous\" in 1991.", "3": "Prior to 2005 this item was titled, 'Television, radios, sound equipment'.", "4": "See https://www.bls.gov/cex/2019-vehicle-insurance.htm regarding the 2019 increase in vehicle insurance expenditures.", "5": "Data are not available in LABSTAT.", "6": "Data are suppressed due to the Relative Standard Error (RSE) being equal to or greater than 25 percent. See www.bls.gov/cex/tables-getting-started-guide.htm for more information."}}}