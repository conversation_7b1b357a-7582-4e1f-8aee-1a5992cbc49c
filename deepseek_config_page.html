<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 DeepSeek AI 配置</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Microsoft YaHei', Arial, sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .container {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 90%;
            border: 1px solid rgba(255,255,255,0.3);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .guide-steps {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            padding: 8px 0;
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .step-text {
            flex: 1;
            line-height: 1.5;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }
        
        .input-field {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: #3498db;
        }
        
        .button {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 10px;
        }
        
        .button:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        
        .button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }
        
        .button.secondary {
            background: #95a5a6;
        }
        
        .button.secondary:hover {
            background: #7f8c8d;
        }
        
        .status-message {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .feature-text {
            font-size: 14px;
            color: #6c757d;
        }
        
        .link {
            color: #3498db;
            text-decoration: none;
        }
        
        .link:hover {
            text-decoration: underline;
        }
        
        .loading {
            display: none;
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🧠</div>
            <div class="title">DeepSeek AI 配置</div>
            <div class="subtitle">配置DeepSeek API，获得强大的AI股票分析能力</div>
        </div>
        
        <!-- DeepSeek优势展示 -->
        <div class="section">
            <div class="section-title">
                🚀 DeepSeek AI 优势
            </div>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <div class="feature-text">专业金融分析</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <div class="feature-text">实时API调用</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">🧮</div>
                    <div class="feature-text">强大推理能力</div>
                </div>
                <div class="feature">
                    <div class="feature-icon">💻</div>
                    <div class="feature-text">无资源限制</div>
                </div>
            </div>
        </div>
        
        <!-- API获取指南 -->
        <div class="section">
            <div class="section-title">
                📋 API获取指南
            </div>
            <div class="guide-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-text">
                        访问 <a href="https://platform.deepseek.com/" target="_blank" class="link">DeepSeek官网</a>
                    </div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-text">注册账号并登录</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-text">进入API管理页面</div>
                </div>
                <div class="step">
                    <div class="step-number">4</div>
                    <div class="step-text">创建新的API密钥</div>
                </div>
                <div class="step">
                    <div class="step-number">5</div>
                    <div class="step-text">复制API密钥到下方输入框</div>
                </div>
            </div>
        </div>
        
        <!-- 状态消息 -->
        <div id="statusMessage" class="hidden"></div>
        
        <!-- API密钥配置 -->
        <div class="section">
            <div class="section-title">
                🔑 API密钥配置
            </div>
            <div class="input-group">
                <label for="apiKey" class="input-label">DeepSeek API密钥:</label>
                <input 
                    type="password" 
                    id="apiKey" 
                    class="input-field" 
                    placeholder="sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
                >
            </div>
            
            <button id="testBtn" class="button" onclick="testApiKey()">
                🧪 测试API连接
            </button>
            
            <button id="saveBtn" class="button secondary" onclick="saveApiKey()" disabled>
                💾 保存配置
            </button>
            
            <div class="loading" id="loading">
                正在测试API连接...
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="section">
            <div class="section-title">
                💡 使用说明
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 14px; line-height: 1.6;">
                <p><strong>配置完成后：</strong></p>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>AI分析将使用DeepSeek强大的推理能力</li>
                    <li>获得更专业的股票分析报告</li>
                    <li>支持复杂的多因子分析</li>
                    <li>提供详细的投资建议和风险提示</li>
                </ul>
                <p><strong>注意：</strong>新用户通常有免费额度，建议设置使用限额。</p>
            </div>
        </div>
    </div>
    
    <script>
        let apiKeyValid = false;
        
        // 页面加载时检查现有配置
        window.onload = function() {
            checkExistingConfig();
        };
        
        // 检查现有配置
        async function checkExistingConfig() {
            try {
                const response = await fetch('/api/deepseek/status');
                const data = await response.json();
                
                if (data.configured) {
                    showStatus('success', '✅ 已配置DeepSeek API，连接正常');
                    document.getElementById('saveBtn').textContent = '🔄 更新配置';
                    document.getElementById('saveBtn').disabled = false;
                }
            } catch (error) {
                console.log('检查配置失败:', error);
            }
        }
        
        // 测试API密钥
        async function testApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKey) {
                showStatus('error', '❌ 请输入API密钥');
                return;
            }
            
            if (!apiKey.startsWith('sk-')) {
                showStatus('error', '❌ API密钥格式不正确，应以 sk- 开头');
                return;
            }
            
            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('testBtn').disabled = true;
            
            try {
                const response = await fetch('/api/deepseek/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ api_key: apiKey })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', '✅ API连接测试成功！DeepSeek AI已就绪');
                    apiKeyValid = true;
                    document.getElementById('saveBtn').disabled = false;
                } else {
                    showStatus('error', `❌ API测试失败: ${data.error}`);
                    apiKeyValid = false;
                }
                
            } catch (error) {
                showStatus('error', `❌ 测试请求失败: ${error.message}`);
                apiKeyValid = false;
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('testBtn').disabled = false;
            }
        }
        
        // 保存API密钥
        async function saveApiKey() {
            const apiKey = document.getElementById('apiKey').value.trim();
            
            if (!apiKeyValid) {
                showStatus('error', '❌ 请先测试API密钥');
                return;
            }
            
            try {
                const response = await fetch('/api/deepseek/save', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ api_key: apiKey })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('success', '✅ API密钥保存成功！DeepSeek AI已启用');
                    
                    // 3秒后跳转回主页
                    setTimeout(() => {
                        window.location.href = '/';
                    }, 3000);
                } else {
                    showStatus('error', `❌ 保存失败: ${data.error}`);
                }
                
            } catch (error) {
                showStatus('error', `❌ 保存请求失败: ${error.message}`);
            }
        }
        
        // 显示状态消息
        function showStatus(type, message) {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.className = `status-message status-${type}`;
            statusDiv.textContent = message;
            statusDiv.classList.remove('hidden');
            
            // 自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    statusDiv.classList.add('hidden');
                }, 5000);
            }
        }
        
        // 监听回车键
        document.getElementById('apiKey').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                testApiKey();
            }
        });
    </script>
</body>
</html>
