#!/usr/bin/env python3
"""
智能选股系统启动脚本
自动检查依赖、配置环境并启动服务
"""

import os
import sys
import subprocess
import time
import requests
import json
from pathlib import Path

def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🤖 智能选股系统 🤖                        ║
    ║                                                              ║
    ║              基于OpenBB + 本地AI的智能股票分析系统            ║
    ║                                                              ║
    ║  功能特性:                                                   ║
    ║  • 实时股票数据分析                                          ║
    ║  • AI智能选股建议                                            ║
    ║  • 自动交易信号推送                                          ║
    ║  • Web端实时监控                                             ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_python_version():
    """检查Python版本"""
    print("🔍 检查Python版本...")
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本: {sys.version}")

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'openbb',
        'pandas',
        'numpy',
        'requests'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("正在安装依赖包...")
        
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ])
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动运行: pip install -r requirements.txt")
            sys.exit(1)

def check_ollama_service():
    """检查Ollama服务是否运行"""
    print("🔍 检查Ollama AI服务...")
    
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            model_names = [model['name'] for model in models]
            
            if 'qwen2.5:7b-instruct' in model_names:
                print("✅ Ollama服务运行正常，Qwen2.5模型已就绪")
                return True
            else:
                print("⚠️  Ollama服务运行，但缺少Qwen2.5模型")
                print("正在下载Qwen2.5模型...")
                
                # 下载模型
                subprocess.run([
                    "ollama", "pull", "qwen2.5:7b-instruct"
                ])
                return True
        else:
            print("❌ Ollama服务响应异常")
            return False
            
    except requests.exceptions.RequestException:
        print("❌ Ollama服务未运行")
        print("请先启动Ollama服务:")
        print("1. 安装Ollama: https://ollama.ai/")
        print("2. 运行: ollama serve")
        print("3. 下载模型: ollama pull qwen2.5:7b-instruct")
        return False

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    env_vars = {
        'PYTHONPATH': str(Path.cwd()),
        'OPENBB_LOG_LEVEL': 'INFO',
        'OPENBB_CACHE_DIRECTORY': str(Path.cwd() / 'cache'),
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"✅ {key}={value}")

def start_backend():
    """启动后端服务"""
    print("🚀 启动后端API服务...")
    
    try:
        # 启动FastAPI服务
        cmd = [
            sys.executable, "-m", "uvicorn",
            "intelligent_stock_web_system:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        process = subprocess.Popen(cmd)
        
        # 等待服务启动
        print("等待服务启动...")
        time.sleep(5)
        
        # 检查服务是否启动成功
        try:
            response = requests.get("http://localhost:8000/api/health", timeout=10)
            if response.status_code == 200:
                print("✅ 后端服务启动成功")
                print("📊 API文档: http://localhost:8000/docs")
                print("🌐 Web界面: http://localhost:8000")
                return process
            else:
                print("❌ 后端服务启动失败")
                return None
        except requests.exceptions.RequestException:
            print("❌ 无法连接到后端服务")
            return None
            
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None

def start_frontend():
    """启动前端服务"""
    frontend_dir = Path("frontend")
    
    if not frontend_dir.exists():
        print("⚠️  前端目录不存在，跳过前端启动")
        return None
    
    print("🚀 启动前端React服务...")
    
    try:
        # 检查是否安装了依赖
        if not (frontend_dir / "node_modules").exists():
            print("📦 安装前端依赖...")
            subprocess.run(["npm", "install"], cwd=frontend_dir, check=True)
        
        # 启动前端服务
        process = subprocess.Popen(
            ["npm", "start"],
            cwd=frontend_dir
        )
        
        print("✅ 前端服务启动中...")
        print("🌐 前端界面: http://localhost:3000")
        return process
        
    except subprocess.CalledProcessError:
        print("❌ 前端服务启动失败")
        return None
    except FileNotFoundError:
        print("⚠️  未找到npm，请安装Node.js")
        return None

def main():
    """主函数"""
    print_banner()
    
    # 检查环境
    check_python_version()
    check_dependencies()
    
    # 设置环境
    setup_environment()
    
    # 检查AI服务
    if not check_ollama_service():
        print("\n⚠️  AI服务未就绪，系统将以有限功能运行")
        print("建议先配置Ollama服务以获得完整的AI分析功能")
    
    # 启动服务
    backend_process = start_backend()
    
    if backend_process is None:
        print("❌ 后端服务启动失败，退出")
        sys.exit(1)
    
    frontend_process = start_frontend()
    
    print("\n" + "="*60)
    print("🎉 智能选股系统启动完成!")
    print("="*60)
    print("📊 后端API: http://localhost:8000")
    print("📚 API文档: http://localhost:8000/docs")
    if frontend_process:
        print("🌐 前端界面: http://localhost:3000")
    print("🤖 AI分析: 实时运行中...")
    print("="*60)
    print("\n按 Ctrl+C 停止服务")
    
    try:
        # 保持服务运行
        backend_process.wait()
    except KeyboardInterrupt:
        print("\n🛑 正在停止服务...")
        
        if backend_process:
            backend_process.terminate()
        
        if frontend_process:
            frontend_process.terminate()
        
        print("✅ 服务已停止")

if __name__ == "__main__":
    main()
