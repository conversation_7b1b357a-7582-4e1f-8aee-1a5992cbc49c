# 群晖NAS智能选股系统环境配置
# 端口配置: 1750-1799

# 基础配置
COMPOSE_PROJECT_NAME=intelligent_stock
COMPOSE_FILE=docker-compose.yml

# 端口配置
WEB_PORT=1750          # 主要Web访问端口
POSTGRES_PORT=1751     # PostgreSQL数据库端口
REDIS_PORT=1752        # Redis缓存端口
OLLAMA_PORT=1753       # Ollama AI服务端口
BACKEND_PORT=1754      # 后端API端口
FRONTEND_PORT=1755     # 前端React应用端口
HTTPS_PORT=1756        # HTTPS端口 (可选)

# 数据库配置
POSTGRES_DB=intelligent_stock
POSTGRES_USER=stock_user
POSTGRES_PASSWORD=StockAnalysis2024!
POSTGRES_HOST=postgres
POSTGRES_PORT_INTERNAL=5432

# Redis配置
REDIS_HOST=redis
REDIS_PORT_INTERNAL=6379
REDIS_PASSWORD=
REDIS_DB=0

# Ollama AI配置
OLLAMA_HOST=ollama
OLLAMA_PORT_INTERNAL=11434
OLLAMA_MODELS_PATH=/root/.ollama
DEFAULT_AI_MODEL=qwen2.5:7b-instruct

# 应用配置
APP_NAME=智能选股系统
APP_VERSION=1.0.0
APP_ENV=production
DEBUG=false
LOG_LEVEL=info

# 安全配置
SECRET_KEY=your-secret-key-change-this-in-production
JWT_SECRET=your-jwt-secret-change-this-in-production
CORS_ORIGINS=*

# OpenBB配置
OPENBB_LOG_LEVEL=INFO
OPENBB_CACHE_DIRECTORY=/app/cache
OPENBB_USER_DATA_DIRECTORY=/app/user_data

# 数据源API密钥 (可选，提高数据质量)
# 请在相应网站注册获取API密钥
OPENBB_FMP_API_KEY=
OPENBB_POLYGON_API_KEY=
OPENBB_ALPHA_VANTAGE_API_KEY=
OPENBB_BENZINGA_API_KEY=
OPENBB_INTRINIO_API_KEY=

# 交易配置
INITIAL_CAPITAL=100000          # 初始资金 (10万)
MAX_SINGLE_POSITION=0.15        # 单只股票最大仓位 15%
MAX_SECTOR_POSITION=0.30        # 单个行业最大仓位 30%
MIN_CONFIDENCE=60               # 最低信心度要求
MAX_DAILY_TRADES=5              # 每日最大交易次数
STOP_LOSS_RATIO=0.08           # 止损比例 8%

# 市场扫描配置
SCAN_INTERVAL=300               # 扫描间隔 (秒)
STOCK_POOL_SIZE=50             # 股票池大小
ENABLE_AUTO_TRADING=true        # 启用自动交易
ENABLE_NOTIFICATIONS=true       # 启用通知

# 性能配置
WORKER_PROCESSES=1              # 工作进程数
WORKER_CONNECTIONS=1000         # 每个进程的连接数
KEEPALIVE_TIMEOUT=65           # 保持连接超时
CLIENT_MAX_BODY_SIZE=100M      # 最大请求体大小

# 缓存配置
CACHE_TTL=300                  # 缓存过期时间 (秒)
ENABLE_REDIS_CACHE=true        # 启用Redis缓存
CACHE_PREFIX=stock_system      # 缓存前缀

# 日志配置
LOG_FORMAT=json                # 日志格式
LOG_FILE=/app/logs/app.log     # 日志文件路径
LOG_MAX_SIZE=100MB             # 日志文件最大大小
LOG_BACKUP_COUNT=5             # 日志备份数量

# 监控配置
ENABLE_METRICS=true            # 启用指标收集
METRICS_PORT=9090              # 指标端口
HEALTH_CHECK_INTERVAL=30       # 健康检查间隔

# 群晖特定配置
SYNOLOGY_DSM_VERSION=7.0       # DSM版本
DOCKER_MEMORY_LIMIT=8g         # Docker内存限制
DOCKER_CPU_LIMIT=4             # Docker CPU限制
ENABLE_GPU=false               # 启用GPU (如果可用)

# 时区配置
TZ=Asia/Shanghai               # 时区设置

# 备份配置
BACKUP_ENABLED=true            # 启用自动备份
BACKUP_INTERVAL=daily          # 备份间隔
BACKUP_RETENTION=7             # 备份保留天数
BACKUP_PATH=/volume1/backups/stock_system  # 备份路径

# 邮件通知配置 (可选)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASSWORD=
SMTP_FROM=
NOTIFICATION_EMAIL=

# 微信通知配置 (可选)
WECHAT_WEBHOOK_URL=

# 钉钉通知配置 (可选)
DINGTALK_WEBHOOK_URL=

# 开发配置 (仅开发环境)
DEV_MODE=false
HOT_RELOAD=false
DEBUG_SQL=false
