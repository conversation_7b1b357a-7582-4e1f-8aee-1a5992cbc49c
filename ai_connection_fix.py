#!/usr/bin/env python3
"""
AI连接问题诊断和修复工具
解决Ollama和AI模型连接问题
"""

import requests
import subprocess
import time
import json
import sys
import os
from typing import Dict, List

class AIConnectionFixer:
    """AI连接修复器"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.synology_ollama_url = "http://localhost:1753"  # 群晖端口
        self.current_url = None
        
    def detect_environment(self) -> str:
        """检测运行环境"""
        if os.path.exists('/.dockerenv'):
            return "docker"
        elif os.path.exists('/usr/syno'):
            return "synology"
        else:
            return "local"
    
    def check_ollama_service(self) -> Dict:
        """检查Ollama服务状态"""
        print("🔍 检查Ollama服务状态...")
        
        # 尝试不同的URL
        urls_to_try = [
            ("本地标准", "http://localhost:11434"),
            ("群晖Docker", "http://localhost:1753"),
            ("Docker内部", "http://ollama:11434")
        ]
        
        for name, url in urls_to_try:
            try:
                print(f"  尝试连接 {name}: {url}")
                response = requests.get(f"{url}/api/tags", timeout=5)
                
                if response.status_code == 200:
                    print(f"  ✅ {name} 连接成功!")
                    self.current_url = url
                    data = response.json()
                    models = data.get('models', [])
                    
                    return {
                        "status": "connected",
                        "url": url,
                        "models": [model.get('name', '') for model in models],
                        "model_count": len(models)
                    }
                else:
                    print(f"  ❌ {name} HTTP错误: {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                print(f"  ❌ {name} 连接被拒绝")
            except requests.exceptions.Timeout:
                print(f"  ❌ {name} 连接超时")
            except Exception as e:
                print(f"  ❌ {name} 其他错误: {e}")
        
        return {"status": "disconnected", "error": "所有连接尝试都失败"}
    
    def check_ollama_process(self) -> Dict:
        """检查Ollama进程"""
        print("\n🔍 检查Ollama进程...")
        
        try:
            # 检查进程
            result = subprocess.run(
                ["pgrep", "-f", "ollama"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                pids = result.stdout.strip().split('\n')
                print(f"  ✅ 找到Ollama进程: {len(pids)}个")
                return {"status": "running", "pids": pids}
            else:
                print("  ❌ 未找到Ollama进程")
                return {"status": "not_running"}
                
        except FileNotFoundError:
            print("  ⚠️  pgrep命令不可用，尝试其他方法...")
            
            # 尝试ps命令
            try:
                result = subprocess.run(
                    ["ps", "aux"],
                    capture_output=True,
                    text=True
                )
                
                if "ollama" in result.stdout:
                    print("  ✅ 通过ps找到Ollama进程")
                    return {"status": "running"}
                else:
                    print("  ❌ 通过ps未找到Ollama进程")
                    return {"status": "not_running"}
                    
            except Exception as e:
                print(f"  ❌ 进程检查失败: {e}")
                return {"status": "unknown", "error": str(e)}
    
    def check_docker_containers(self) -> Dict:
        """检查Docker容器状态"""
        print("\n🐳 检查Docker容器...")
        
        try:
            # 检查docker-compose服务
            result = subprocess.run(
                ["docker-compose", "ps", "ollama"],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                output = result.stdout
                if "Up" in output:
                    print("  ✅ Ollama Docker容器运行中")
                    return {"status": "running", "output": output}
                else:
                    print("  ❌ Ollama Docker容器未运行")
                    return {"status": "stopped", "output": output}
            else:
                print("  ⚠️  docker-compose命令失败")
                return {"status": "error", "error": result.stderr}
                
        except FileNotFoundError:
            print("  ⚠️  docker-compose命令不可用")
            
            # 尝试直接docker命令
            try:
                result = subprocess.run(
                    ["docker", "ps", "--filter", "name=ollama"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    if "ollama" in result.stdout:
                        print("  ✅ 通过docker找到Ollama容器")
                        return {"status": "running", "output": result.stdout}
                    else:
                        print("  ❌ 未找到Ollama容器")
                        return {"status": "not_found"}
                        
            except FileNotFoundError:
                print("  ❌ Docker命令不可用")
                return {"status": "docker_unavailable"}
    
    def start_ollama_service(self) -> bool:
        """启动Ollama服务"""
        print("\n🚀 尝试启动Ollama服务...")
        
        env = self.detect_environment()
        
        if env == "docker" or env == "synology":
            # Docker环境，尝试启动容器
            print("  检测到Docker环境，启动Ollama容器...")
            try:
                result = subprocess.run(
                    ["docker-compose", "up", "-d", "ollama"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    print("  ✅ Ollama容器启动成功")
                    time.sleep(10)  # 等待服务启动
                    return True
                else:
                    print(f"  ❌ 容器启动失败: {result.stderr}")
                    return False
                    
            except FileNotFoundError:
                print("  ❌ docker-compose命令不可用")
                return False
        else:
            # 本地环境，尝试启动Ollama
            print("  检测到本地环境，启动Ollama服务...")
            try:
                # 检查ollama命令是否可用
                result = subprocess.run(
                    ["which", "ollama"],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    # 启动ollama serve
                    print("  启动ollama serve...")
                    subprocess.Popen(
                        ["ollama", "serve"],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL
                    )
                    time.sleep(5)  # 等待服务启动
                    return True
                else:
                    print("  ❌ ollama命令不可用，请先安装Ollama")
                    print("  安装地址: https://ollama.ai/")
                    return False
                    
            except Exception as e:
                print(f"  ❌ 启动失败: {e}")
                return False
    
    def install_default_model(self) -> bool:
        """安装默认AI模型"""
        print("\n📥 安装默认AI模型...")
        
        if not self.current_url:
            print("  ❌ Ollama服务未连接，无法安装模型")
            return False
        
        # 推荐的轻量级模型
        models_to_try = [
            "qwen2.5:7b-instruct",
            "yi:6b-chat", 
            "phi3:3.8b"
        ]
        
        for model in models_to_try:
            print(f"  尝试安装模型: {model}")
            try:
                if self.detect_environment() in ["docker", "synology"]:
                    # Docker环境
                    result = subprocess.run(
                        ["docker-compose", "exec", "ollama", "ollama", "pull", model],
                        capture_output=True,
                        text=True,
                        timeout=600  # 10分钟超时
                    )
                else:
                    # 本地环境
                    result = subprocess.run(
                        ["ollama", "pull", model],
                        capture_output=True,
                        text=True,
                        timeout=600
                    )
                
                if result.returncode == 0:
                    print(f"  ✅ 模型 {model} 安装成功!")
                    return True
                else:
                    print(f"  ❌ 模型 {model} 安装失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print(f"  ⏰ 模型 {model} 安装超时，尝试下一个...")
                continue
            except Exception as e:
                print(f"  ❌ 安装过程出错: {e}")
                continue
        
        print("  ❌ 所有模型安装都失败")
        return False
    
    def update_ai_config(self) -> bool:
        """更新AI配置"""
        print("\n🔧 更新AI配置...")
        
        if not self.current_url:
            print("  ❌ 无法确定Ollama URL")
            return False
        
        # 更新local_llm.py中的配置
        config_file = "ai_engine/local_llm.py"
        
        if not os.path.exists(config_file):
            print(f"  ❌ 配置文件不存在: {config_file}")
            return False
        
        try:
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新URL配置
            if "localhost:11434" in content and self.current_url != "http://localhost:11434":
                new_port = self.current_url.split(':')[-1]
                content = content.replace("port: int = 11434", f"port: int = {new_port}")
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ 已更新配置文件，使用端口: {new_port}")
                return True
            else:
                print("  ✅ 配置文件无需更新")
                return True
                
        except Exception as e:
            print(f"  ❌ 更新配置失败: {e}")
            return False
    
    def test_ai_connection(self) -> bool:
        """测试AI连接"""
        print("\n🧪 测试AI连接...")
        
        try:
            from ai_engine.local_llm import LocalLLMClient
            
            # 尝试创建客户端
            client = LocalLLMClient()
            
            # 测试简单生成
            response = client.generate("你好，请简单介绍一下你自己。", temperature=0.3)
            
            if response and len(response) > 10:
                print("  ✅ AI连接测试成功!")
                print(f"  📝 AI响应: {response[:100]}...")
                return True
            else:
                print("  ❌ AI响应为空或过短")
                return False
                
        except Exception as e:
            print(f"  ❌ AI连接测试失败: {e}")
            return False
    
    def comprehensive_fix(self) -> bool:
        """综合修复流程"""
        print("🔧 AI连接问题综合修复")
        print("=" * 50)
        
        # 1. 检查Ollama服务
        service_status = self.check_ollama_service()
        
        if service_status["status"] == "connected":
            print(f"\n✅ Ollama服务正常，已安装 {service_status['model_count']} 个模型")
            
            if service_status['model_count'] == 0:
                print("⚠️  未安装任何模型，开始安装...")
                self.install_default_model()
        else:
            print("\n❌ Ollama服务连接失败，开始修复...")
            
            # 2. 检查进程和容器
            self.check_ollama_process()
            self.check_docker_containers()
            
            # 3. 尝试启动服务
            if self.start_ollama_service():
                # 4. 重新检查连接
                time.sleep(5)
                service_status = self.check_ollama_service()
                
                if service_status["status"] == "connected":
                    print("✅ Ollama服务启动成功!")
                    
                    # 5. 安装默认模型
                    if service_status.get('model_count', 0) == 0:
                        self.install_default_model()
                else:
                    print("❌ Ollama服务启动后仍无法连接")
                    return False
            else:
                print("❌ Ollama服务启动失败")
                return False
        
        # 6. 更新配置
        self.update_ai_config()
        
        # 7. 测试连接
        return self.test_ai_connection()

def main():
    """主函数"""
    fixer = AIConnectionFixer()
    
    print("🤖 AI连接问题诊断和修复工具")
    print("=" * 40)
    
    success = fixer.comprehensive_fix()
    
    if success:
        print("\n🎉 AI连接修复成功!")
        print("现在可以正常使用智能选股系统了")
        print("\n下一步:")
        print("1. 运行: python smart_model_installer.py")
        print("2. 或者: python start.py")
    else:
        print("\n❌ AI连接修复失败")
        print("\n手动修复建议:")
        print("1. 检查Ollama是否正确安装")
        print("2. 运行: ollama serve")
        print("3. 运行: ollama pull qwen2.5:7b-instruct")
        print("4. 检查防火墙设置")
        
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
