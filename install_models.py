#!/usr/bin/env python3
"""
本地AI模型安装和管理工具
支持一键安装多种开源大模型
"""

import subprocess
import sys
import time
import requests
from typing import List, Dict

class ModelInstaller:
    """模型安装器"""
    
    def __init__(self):
        self.models = {
            # 推荐模型 (按优先级排序)
            "recommended": [
                {
                    "name": "qwen2.5:7b-instruct",
                    "description": "阿里Qwen2.5-7B (推荐首选)",
                    "size": "4.7GB",
                    "memory": "8GB",
                    "features": ["中文优化", "金融知识", "推理能力强"]
                },
                {
                    "name": "llama3.1:8b-instruct", 
                    "description": "Meta Llama3.1-8B",
                    "size": "4.7GB",
                    "memory": "8GB", 
                    "features": ["Meta开源", "性能优秀", "英文为主"]
                },
                {
                    "name": "yi:6b-chat",
                    "description": "零一万物Yi-6B",
                    "size": "3.5GB",
                    "memory": "6GB",
                    "features": ["中文友好", "轻量级", "国产模型"]
                }
            ],
            
            # 高性能模型
            "high_performance": [
                {
                    "name": "qwen2.5:14b-instruct",
                    "description": "阿里Qwen2.5-14B",
                    "size": "8.2GB", 
                    "memory": "16GB",
                    "features": ["更强性能", "中文优化", "专业分析"]
                },
                {
                    "name": "llama3.1:70b-instruct",
                    "description": "Meta Llama3.1-70B",
                    "size": "40GB",
                    "memory": "64GB+",
                    "features": ["顶级性能", "需要大内存", "企业级"]
                }
            ],
            
            # 专业模型
            "specialized": [
                {
                    "name": "deepseek-coder:6.7b-instruct",
                    "description": "DeepSeek代码专家",
                    "size": "3.8GB",
                    "memory": "8GB",
                    "features": ["代码理解", "技术分析", "编程专业"]
                },
                {
                    "name": "deepseek-math:7b-instruct", 
                    "description": "DeepSeek数学专家",
                    "size": "4.1GB",
                    "memory": "8GB",
                    "features": ["数学计算", "量化分析", "逻辑推理"]
                }
            ],
            
            # 轻量级模型
            "lightweight": [
                {
                    "name": "phi3:3.8b",
                    "description": "微软Phi3-3.8B",
                    "size": "2.3GB",
                    "memory": "4GB",
                    "features": ["超轻量", "快速响应", "资源友好"]
                },
                {
                    "name": "gemma:7b-instruct",
                    "description": "Google Gemma-7B", 
                    "size": "4.8GB",
                    "memory": "8GB",
                    "features": ["Google开源", "平衡性能", "多语言"]
                }
            ]
        }
    
    def check_ollama_status(self) -> bool:
        """检查Ollama服务状态"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_installed_models(self) -> List[str]:
        """获取已安装的模型列表"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get('models', [])
                return [model['name'] for model in models]
        except:
            pass
        return []
    
    def install_model(self, model_name: str) -> bool:
        """安装指定模型"""
        print(f"📥 正在安装模型: {model_name}")
        print("⏳ 这可能需要几分钟时间，请耐心等待...")
        
        try:
            # 使用ollama pull命令安装模型
            result = subprocess.run(
                ["ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=1800  # 30分钟超时
            )
            
            if result.returncode == 0:
                print(f"✅ 模型 {model_name} 安装成功!")
                return True
            else:
                print(f"❌ 模型 {model_name} 安装失败:")
                print(result.stderr)
                return False
                
        except subprocess.TimeoutExpired:
            print(f"❌ 模型 {model_name} 安装超时")
            return False
        except FileNotFoundError:
            print("❌ 未找到ollama命令，请先安装Ollama")
            print("访问 https://ollama.ai/ 下载安装")
            return False
        except Exception as e:
            print(f"❌ 安装过程中出错: {e}")
            return False
    
    def print_model_info(self, category: str, models: List[Dict]):
        """打印模型信息"""
        print(f"\n📋 {category}")
        print("-" * 60)
        
        for i, model in enumerate(models, 1):
            print(f"{i}. {model['description']}")
            print(f"   模型名称: {model['name']}")
            print(f"   文件大小: {model['size']}")
            print(f"   内存需求: {model['memory']}")
            print(f"   特性: {', '.join(model['features'])}")
            print()
    
    def interactive_install(self):
        """交互式安装"""
        print("🤖 本地AI模型安装工具")
        print("=" * 50)
        
        # 检查Ollama服务
        if not self.check_ollama_status():
            print("❌ Ollama服务未运行")
            print("请先启动Ollama服务:")
            print("1. 安装Ollama: https://ollama.ai/")
            print("2. 运行: ollama serve")
            return
        
        print("✅ Ollama服务运行正常")
        
        # 显示已安装的模型
        installed = self.get_installed_models()
        if installed:
            print(f"\n📦 已安装模型: {', '.join(installed)}")
        else:
            print("\n📦 暂无已安装模型")
        
        # 显示可用模型
        print("\n🎯 推荐安装方案:")
        
        print("\n方案一: 快速开始 (推荐新手)")
        print("- qwen2.5:7b-instruct (中文优化，适合股票分析)")
        
        print("\n方案二: 性能优先")  
        print("- qwen2.5:14b-instruct (更强性能)")
        print("- llama3.1:8b-instruct (Meta开源)")
        
        print("\n方案三: 专业分析")
        print("- deepseek-coder:6.7b-instruct (技术分析专业)")
        print("- deepseek-math:7b-instruct (量化计算专业)")
        
        # 详细模型列表
        self.print_model_info("推荐模型", self.models["recommended"])
        self.print_model_info("高性能模型", self.models["high_performance"]) 
        self.print_model_info("专业模型", self.models["specialized"])
        self.print_model_info("轻量级模型", self.models["lightweight"])
        
        # 用户选择
        print("请选择安装方案:")
        print("1. 快速开始 (安装 qwen2.5:7b-instruct)")
        print("2. 性能优先 (安装 qwen2.5:14b-instruct + llama3.1:8b-instruct)")
        print("3. 专业分析 (安装 deepseek-coder + deepseek-math)")
        print("4. 自定义安装")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 退出安装")
                return
            elif choice == "1":
                self.install_model("qwen2.5:7b-instruct")
            elif choice == "2":
                self.install_model("qwen2.5:14b-instruct")
                self.install_model("llama3.1:8b-instruct")
            elif choice == "3":
                self.install_model("deepseek-coder:6.7b-instruct")
                self.install_model("deepseek-math:7b-instruct")
            elif choice == "4":
                self.custom_install()
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消安装")
    
    def custom_install(self):
        """自定义安装"""
        print("\n🔧 自定义安装模式")
        print("请输入要安装的模型名称 (多个模型用空格分隔):")
        print("例如: qwen2.5:7b-instruct llama3.1:8b-instruct")
        
        try:
            models_input = input("模型名称: ").strip()
            if not models_input:
                print("❌ 未输入模型名称")
                return
            
            model_names = models_input.split()
            
            for model_name in model_names:
                self.install_model(model_name)
                
        except KeyboardInterrupt:
            print("\n👋 用户取消安装")
    
    def quick_setup(self):
        """一键快速设置"""
        print("🚀 一键快速设置 - 安装推荐模型")
        
        if not self.check_ollama_status():
            print("❌ Ollama服务未运行，请先启动服务")
            return
        
        # 安装推荐模型
        success = self.install_model("qwen2.5:7b-instruct")
        
        if success:
            print("\n🎉 快速设置完成!")
            print("现在可以启动智能选股系统:")
            print("python start.py")
        else:
            print("\n❌ 快速设置失败")

def main():
    """主函数"""
    installer = ModelInstaller()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        installer.quick_setup()
    else:
        installer.interactive_install()

if __name__ == "__main__":
    main()
