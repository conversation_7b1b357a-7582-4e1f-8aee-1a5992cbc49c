#!/usr/bin/env python3
"""
OpenBB Platform 界面演示
展示OpenBB的主要功能和API接口，方便后续开发参考
"""

import sys
import json
from datetime import datetime, timedelta
from typing import Dict, List

try:
    from openbb import obb
    OPENBB_AVAILABLE = True
    print("✅ OpenBB Platform 已加载")
except ImportError:
    OPENBB_AVAILABLE = False
    print("❌ OpenBB Platform 未安装")

class OpenBBInterfaceDemo:
    """OpenBB界面演示器"""
    
    def __init__(self):
        self.demo_symbols = {
            # 美股
            'US': ['AAPL', 'MSFT', 'GOOGL', 'TSLA'],
            # 中国A股
            'CN': ['000001.SZ', '600519.SS', '000858.SZ', '002415.SZ']
        }
    
    def show_openbb_structure(self):
        """展示OpenBB的模块结构"""
        print("\n🏗️  OpenBB Platform 模块结构")
        print("=" * 60)
        
        if not OPENBB_AVAILABLE:
            print("❌ OpenBB未可用，显示理论结构")
            self._show_theoretical_structure()
            return
        
        # 获取主要模块
        main_modules = [
            'equity',      # 股票
            'crypto',      # 加密货币
            'economy',     # 经济数据
            'news',        # 新闻
            'currency',    # 外汇
            'commodity',   # 商品
            'fixedincome', # 固定收益
            'derivatives', # 衍生品
            'etf',         # ETF
            'index',       # 指数
        ]
        
        print("📊 主要数据模块:")
        for module in main_modules:
            if hasattr(obb, module):
                module_obj = getattr(obb, module)
                print(f"  ✅ obb.{module} - {self._get_module_description(module)}")
                
                # 显示子模块
                if hasattr(module_obj, '__dict__'):
                    sub_modules = [attr for attr in dir(module_obj) 
                                 if not attr.startswith('_') and 
                                 not callable(getattr(module_obj, attr, None))]
                    if sub_modules:
                        print(f"     子模块: {', '.join(sub_modules[:5])}")
            else:
                print(f"  ❌ obb.{module} - 未安装")
    
    def _get_module_description(self, module: str) -> str:
        """获取模块描述"""
        descriptions = {
            'equity': '股票数据 (价格、基本面、技术指标)',
            'crypto': '加密货币数据',
            'economy': '宏观经济数据',
            'news': '财经新闻',
            'currency': '外汇数据',
            'commodity': '商品数据',
            'fixedincome': '债券和固定收益',
            'derivatives': '期权期货等衍生品',
            'etf': 'ETF基金数据',
            'index': '股票指数数据'
        }
        return descriptions.get(module, '未知模块')
    
    def _show_theoretical_structure(self):
        """显示理论结构"""
        structure = {
            "obb.equity": {
                "price": ["historical", "quote", "performance"],
                "fundamental": ["overview", "ratios", "income", "balance", "cash"],
                "discovery": ["screener", "gainers", "losers"],
                "estimates": ["consensus", "revisions"]
            },
            "obb.news": {
                "world": "全球新闻",
                "company": "公司新闻"
            },
            "obb.economy": {
                "gdp": "GDP数据",
                "inflation": "通胀数据",
                "employment": "就业数据"
            }
        }
        
        for module, sub_modules in structure.items():
            print(f"📊 {module}")
            if isinstance(sub_modules, dict):
                for sub, desc in sub_modules.items():
                    if isinstance(desc, list):
                        print(f"   └── {sub}: {', '.join(desc)}")
                    else:
                        print(f"   └── {sub}: {desc}")
    
    def demo_equity_functions(self):
        """演示股票相关功能"""
        print("\n📈 股票模块功能演示")
        print("=" * 60)
        
        if not OPENBB_AVAILABLE:
            print("❌ OpenBB未可用，显示API示例")
            self._show_equity_api_examples()
            return
        
        # 演示股票价格获取
        print("1. 📊 股票报价获取")
        print("-" * 30)
        
        for region, symbols in self.demo_symbols.items():
            print(f"\n{region}股票示例:")
            for symbol in symbols[:2]:  # 只演示前2个
                try:
                    print(f"  获取 {symbol} 报价...", end=" ")
                    
                    # 获取报价
                    quote = obb.equity.price.quote(symbol=symbol, provider="yfinance")
                    
                    if quote.results:
                        result = quote.results[0]
                        price = getattr(result, 'last_price', 0) or getattr(result, 'price', 0)
                        print(f"✅ 价格: ${price:.2f}")
                    else:
                        print("❌ 无数据")
                        
                except Exception as e:
                    print(f"❌ 错误: {str(e)[:50]}")
        
        # 演示历史数据获取
        print(f"\n2. 📈 历史数据获取")
        print("-" * 30)
        
        try:
            symbol = "AAPL"
            print(f"获取 {symbol} 历史数据...", end=" ")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
            hist = obb.equity.price.historical(
                symbol=symbol,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d"),
                provider="yfinance"
            )
            
            if hist.results:
                df = hist.to_df()
                print(f"✅ 获取 {len(df)} 条记录")
                print(f"   最新价格: ${df['close'].iloc[-1]:.2f}")
                print(f"   价格区间: ${df['low'].min():.2f} - ${df['high'].max():.2f}")
            else:
                print("❌ 无历史数据")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    def _show_equity_api_examples(self):
        """显示股票API示例"""
        examples = {
            "获取股票报价": "obb.equity.price.quote(symbol='AAPL', provider='yfinance')",
            "获取历史数据": "obb.equity.price.historical(symbol='AAPL', start_date='2024-01-01')",
            "获取基本面数据": "obb.equity.fundamental.overview(symbol='AAPL', provider='fmp')",
            "获取财务比率": "obb.equity.fundamental.ratios(symbol='AAPL')",
            "股票筛选": "obb.equity.discovery.screener()",
            "获取分析师预期": "obb.equity.estimates.consensus(symbol='AAPL')"
        }
        
        for desc, code in examples.items():
            print(f"  {desc}:")
            print(f"    {code}")
    
    def demo_news_functions(self):
        """演示新闻功能"""
        print("\n📰 新闻模块功能演示")
        print("=" * 60)
        
        if not OPENBB_AVAILABLE:
            print("❌ OpenBB未可用，显示API示例")
            print("  获取全球新闻: obb.news.world(provider='benzinga', limit=10)")
            print("  获取公司新闻: obb.news.company(symbol='AAPL', provider='benzinga')")
            return
        
        try:
            print("获取全球财经新闻...", end=" ")
            
            # 获取新闻 (使用免费的provider)
            news = obb.news.world(provider="biztoc", limit=5)
            
            if news.results:
                print(f"✅ 获取 {len(news.results)} 条新闻")
                
                for i, article in enumerate(news.results[:3], 1):
                    title = getattr(article, 'title', 'No title')
                    date = getattr(article, 'date', 'No date')
                    print(f"  {i}. {title[:60]}... ({date})")
            else:
                print("❌ 无新闻数据")
                
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    def demo_technical_analysis(self):
        """演示技术分析功能"""
        print("\n📊 技术分析功能演示")
        print("=" * 60)
        
        if not OPENBB_AVAILABLE:
            print("❌ OpenBB未可用，显示技术分析API示例")
            self._show_technical_api_examples()
            return
        
        # 检查是否有技术分析模块
        if hasattr(obb, 'technical'):
            print("✅ 技术分析模块可用")
            try:
                # 这里可以添加技术分析演示
                print("  技术分析功能需要历史数据作为输入")
                print("  支持的指标: RSI, MACD, 布林带, 移动平均线等")
            except Exception as e:
                print(f"❌ 技术分析错误: {e}")
        else:
            print("⚠️  技术分析模块未安装")
            print("  可通过 pip install openbb[technical] 安装")
    
    def _show_technical_api_examples(self):
        """显示技术分析API示例"""
        examples = {
            "RSI指标": "obb.technical.rsi(data=historical_data, length=14)",
            "MACD指标": "obb.technical.macd(data=historical_data)",
            "布林带": "obb.technical.bbands(data=historical_data)",
            "移动平均": "obb.technical.sma(data=historical_data, length=20)",
            "随机指标": "obb.technical.stoch(data=historical_data)"
        }
        
        for desc, code in examples.items():
            print(f"  {desc}:")
            print(f"    {code}")
    
    def show_data_providers(self):
        """显示数据提供商"""
        print("\n🔌 数据提供商")
        print("=" * 60)
        
        providers = {
            "免费提供商": {
                "yfinance": "Yahoo Finance - 全球股票数据",
                "fred": "美联储经济数据",
                "biztoc": "商业新闻聚合",
                "wikipedia": "维基百科数据"
            },
            "付费提供商": {
                "alpha_vantage": "Alpha Vantage - 股票和外汇",
                "fmp": "Financial Modeling Prep - 财务数据",
                "polygon": "Polygon.io - 实时市场数据",
                "benzinga": "Benzinga - 新闻和分析",
                "intrinio": "Intrinio - 专业金融数据"
            }
        }
        
        for category, provider_list in providers.items():
            print(f"\n📊 {category}:")
            for provider, description in provider_list.items():
                print(f"  • {provider}: {description}")
    
    def show_usage_examples(self):
        """显示使用示例"""
        print("\n💡 OpenBB使用示例")
        print("=" * 60)
        
        examples = [
            {
                "title": "获取中国A股数据",
                "code": """
# 获取平安银行报价
quote = obb.equity.price.quote(symbol="000001.SZ", provider="yfinance")
print(f"价格: {quote.results[0].last_price}")

# 获取贵州茅台历史数据
hist = obb.equity.price.historical(
    symbol="600519.SS", 
    start_date="2024-01-01",
    provider="yfinance"
)
df = hist.to_df()
print(df.head())
                """
            },
            {
                "title": "技术指标计算",
                "code": """
# 获取历史数据
hist = obb.equity.price.historical(symbol="AAPL", provider="yfinance")
data = hist.to_df()

# 计算RSI
rsi = obb.technical.rsi(data=data.to_dict('records'), length=14)

# 计算MACD
macd = obb.technical.macd(data=data.to_dict('records'))

# 计算移动平均
ma20 = obb.technical.sma(data=data.to_dict('records'), length=20)
                """
            },
            {
                "title": "新闻情感分析",
                "code": """
# 获取公司新闻
news = obb.news.company(symbol="AAPL", provider="benzinga", limit=10)

# 获取全球新闻
world_news = obb.news.world(provider="biztoc", limit=20)

# 处理新闻数据
for article in news.results:
    print(f"标题: {article.title}")
    print(f"时间: {article.date}")
    print(f"链接: {article.url}")
                """
            }
        ]
        
        for example in examples:
            print(f"\n📝 {example['title']}:")
            print(example['code'])
    
    def run_demo(self):
        """运行完整演示"""
        print("🚀 OpenBB Platform 界面和功能演示")
        print("=" * 80)
        print(f"📅 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 1. 显示模块结构
        self.show_openbb_structure()
        
        # 2. 演示股票功能
        self.demo_equity_functions()
        
        # 3. 演示新闻功能
        self.demo_news_functions()
        
        # 4. 演示技术分析
        self.demo_technical_analysis()
        
        # 5. 显示数据提供商
        self.show_data_providers()
        
        # 6. 显示使用示例
        self.show_usage_examples()
        
        print("\n" + "=" * 80)
        print("🎯 总结")
        print("=" * 80)
        print("OpenBB Platform 提供了:")
        print("✅ 统一的API接口访问多个数据源")
        print("✅ 支持股票、新闻、经济数据等多种数据类型")
        print("✅ 内置技术分析指标计算")
        print("✅ 支持中国A股和全球市场")
        print("✅ 免费和付费数据源选择")
        print("\n💡 这为您的智能选股系统提供了强大的数据基础!")

def main():
    """主函数"""
    demo = OpenBBInterfaceDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
