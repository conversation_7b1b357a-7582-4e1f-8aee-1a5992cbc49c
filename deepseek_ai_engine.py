#!/usr/bin/env python3
"""
DeepSeek AI引擎
使用DeepSeek API进行股票分析，突破本地AI限制
"""

import requests
import json
import asyncio
import os
from typing import Dict, List, Optional
from datetime import datetime
import logging

class DeepSeekAIEngine:
    """DeepSeek AI分析引擎"""
    
    def __init__(self, api_key: str = None):
        """
        初始化DeepSeek AI引擎
        
        Args:
            api_key: DeepSeek API密钥，如果为None则从环境变量获取
        """
        self.api_key = api_key or os.getenv('DEEPSEEK_API_KEY')
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.model = "deepseek-chat"
        self.logger = logging.getLogger(__name__)
        
        if not self.api_key:
            self.logger.warning("DeepSeek API密钥未设置，将使用模拟分析")
    
    async def analyze_stock(self, stock_data: Dict, technical_data: Dict) -> Dict:
        """
        使用DeepSeek AI分析股票
        
        Args:
            stock_data: 股票基础数据
            technical_data: 技术指标数据
            
        Returns:
            AI分析结果
        """
        if not self.api_key:
            return await self._simulate_analysis(stock_data, technical_data)
        
        try:
            # 构建分析提示词
            prompt = self._build_analysis_prompt(stock_data, technical_data)
            
            # 调用DeepSeek API
            response = await self._call_deepseek_api(prompt)
            
            if response:
                # 解析AI响应
                analysis = self._parse_ai_response(response, stock_data['symbol'])
                return analysis
            else:
                return await self._simulate_analysis(stock_data, technical_data)
                
        except Exception as e:
            self.logger.error(f"DeepSeek分析失败: {e}")
            return await self._simulate_analysis(stock_data, technical_data)
    
    def _build_analysis_prompt(self, stock_data: Dict, technical_data: Dict) -> str:
        """构建分析提示词"""
        
        symbol = stock_data.get('symbol', 'Unknown')
        name = stock_data.get('name', symbol)
        price = stock_data.get('price', 0)
        change_percent = stock_data.get('change_percent', 0)
        volume = stock_data.get('volume', 0)
        pe_ratio = stock_data.get('pe_ratio', 0)
        market_cap = stock_data.get('market_cap', 0)
        
        rsi = technical_data.get('rsi', 50)
        ma5 = technical_data.get('ma5', 0)
        ma20 = technical_data.get('ma20', 0)
        ma60 = technical_data.get('ma60', 0)
        volume_ratio = technical_data.get('volume_ratio', 1)
        price_position = technical_data.get('price_position', 50)
        trend = technical_data.get('trend', 'sideways')
        
        prompt = f"""
你是一位资深的股票分析师和量化投资专家，请基于以下数据对股票进行专业分析：

股票信息：
- 代码：{symbol}
- 名称：{name}
- 当前价格：{price:.2f}
- 涨跌幅：{change_percent:.2f}%
- 成交量：{volume:,}
- PE比率：{pe_ratio:.2f}
- 市值：{market_cap:,}

技术指标：
- RSI：{rsi:.1f}
- MA5：{ma5:.2f}
- MA20：{ma20:.2f}
- MA60：{ma60:.2f}
- 成交量比率：{volume_ratio:.2f}
- 价格位置（年内）：{price_position:.1f}%
- 趋势：{trend}

请从以下维度进行分析：

1. 技术面分析：
   - RSI指标解读
   - 移动平均线分析
   - 成交量分析
   - 趋势判断

2. 基本面分析：
   - 估值水平评估
   - 市值规模分析

3. 投资建议：
   - 明确给出：强烈买入/买入/观望/谨慎观望/卖出
   - 信心度评分（0-100分）
   - 3-5个核心理由
   - 2-3个主要风险

4. 操作建议：
   - 建议仓位比例
   - 止损位设置
   - 目标价位

请用专业、客观的语言分析，避免过于乐观或悲观的表述。

输出格式要求：
投资建议：[具体建议]
信心度：[数字]分
核心理由：
1. [理由1]
2. [理由2]
3. [理由3]
主要风险：
1. [风险1]
2. [风险2]
操作建议：[具体操作建议]
"""
        
        return prompt
    
    async def _call_deepseek_api(self, prompt: str) -> Optional[str]:
        """调用DeepSeek API"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是一位专业的股票分析师，具有丰富的投资经验和深厚的金融知识。"
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 1500,
            "stream": False
        }
        
        try:
            # 使用异步请求
            import aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.base_url,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content']
                    else:
                        self.logger.error(f"DeepSeek API错误: {response.status}")
                        return None
                        
        except Exception as e:
            self.logger.error(f"调用DeepSeek API失败: {e}")
            return None
    
    def _parse_ai_response(self, response: str, symbol: str) -> Dict:
        """解析AI响应"""
        try:
            lines = response.strip().split('\n')
            
            # 初始化结果
            result = {
                'symbol': symbol,
                'action': '观望',
                'confidence': 50,
                'reasons': [],
                'risks': [],
                'operation_advice': '',
                'raw_response': response,
                'timestamp': datetime.now().isoformat(),
                'ai_engine': 'deepseek'
            }
            
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                # 解析投资建议
                if line.startswith('投资建议：'):
                    action_text = line.replace('投资建议：', '').strip()
                    result['action'] = action_text
                
                # 解析信心度
                elif line.startswith('信心度：'):
                    confidence_text = line.replace('信心度：', '').replace('分', '').strip()
                    try:
                        result['confidence'] = int(confidence_text)
                    except:
                        result['confidence'] = 50
                
                # 解析核心理由
                elif line.startswith('核心理由：'):
                    current_section = 'reasons'
                elif line.startswith('主要风险：'):
                    current_section = 'risks'
                elif line.startswith('操作建议：'):
                    current_section = 'operation'
                    result['operation_advice'] = line.replace('操作建议：', '').strip()
                
                # 解析列表项
                elif line.startswith(('1.', '2.', '3.', '4.', '5.')):
                    content = line[2:].strip()
                    if current_section == 'reasons':
                        result['reasons'].append(content)
                    elif current_section == 'risks':
                        result['risks'].append(content)
            
            # 确保至少有一些内容
            if not result['reasons']:
                result['reasons'] = ['AI分析完成，请查看详细报告']
            if not result['risks']:
                result['risks'] = ['投资有风险，请谨慎决策']
            
            return result
            
        except Exception as e:
            self.logger.error(f"解析AI响应失败: {e}")
            return {
                'symbol': symbol,
                'action': '观望',
                'confidence': 50,
                'reasons': ['AI分析出现异常'],
                'risks': ['分析结果不确定'],
                'operation_advice': '建议人工复核',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'ai_engine': 'deepseek'
            }
    
    async def _simulate_analysis(self, stock_data: Dict, technical_data: Dict) -> Dict:
        """模拟分析（当API不可用时）"""
        symbol = stock_data.get('symbol', 'Unknown')
        
        # 简化的分析逻辑
        rsi = technical_data.get('rsi', 50)
        trend = technical_data.get('trend', 'sideways')
        volume_ratio = technical_data.get('volume_ratio', 1)
        
        score = 50
        reasons = []
        risks = []
        
        # RSI分析
        if rsi < 30:
            score += 20
            reasons.append(f"RSI({rsi:.1f})显示超卖，可能存在反弹机会")
        elif rsi > 70:
            score -= 15
            risks.append(f"RSI({rsi:.1f})显示超买，注意回调风险")
        else:
            score += 10
            reasons.append(f"RSI({rsi:.1f})处于合理区间")
        
        # 趋势分析
        if trend == 'up':
            score += 15
            reasons.append("均线呈多头排列，趋势向上")
        elif trend == 'down':
            score -= 15
            risks.append("均线呈空头排列，趋势向下")
        
        # 成交量分析
        if volume_ratio > 1.5:
            score += 10
            reasons.append(f"成交量放大{volume_ratio:.1f}倍，市场关注度高")
        
        # 生成建议
        if score >= 70:
            action = "买入"
            confidence = min(85, score)
        elif score >= 55:
            action = "观望"
            confidence = min(75, score)
        else:
            action = "谨慎观望"
            confidence = max(40, score)
        
        return {
            'symbol': symbol,
            'action': action,
            'confidence': confidence,
            'reasons': reasons or ['基于技术指标的基础分析'],
            'risks': risks or ['市场波动风险'],
            'operation_advice': '建议分批建仓，设置止损',
            'timestamp': datetime.now().isoformat(),
            'ai_engine': 'simulated'
        }
    
    async def batch_analyze(self, stocks_data: List[Dict]) -> List[Dict]:
        """批量分析股票"""
        results = []
        
        for stock_data in stocks_data:
            # 这里需要技术指标数据，实际使用时需要传入
            technical_data = {
                'rsi': 50,
                'ma5': stock_data.get('price', 0),
                'ma20': stock_data.get('price', 0),
                'ma60': stock_data.get('price', 0),
                'volume_ratio': 1.0,
                'price_position': 50,
                'trend': 'sideways'
            }
            
            analysis = await self.analyze_stock(stock_data, technical_data)
            results.append(analysis)
            
            # 避免API频率限制
            await asyncio.sleep(1)
        
        return results

# 使用示例
async def main():
    """测试DeepSeek AI引擎"""
    
    # 初始化AI引擎
    ai_engine = DeepSeekAIEngine()
    
    # 模拟股票数据
    stock_data = {
        'symbol': '000001.SZ',
        'name': '平安银行',
        'price': 12.50,
        'change_percent': 2.5,
        'volume': 50000000,
        'pe_ratio': 8.5,
        'market_cap': 240000000000
    }
    
    technical_data = {
        'rsi': 45.5,
        'ma5': 12.60,
        'ma20': 12.20,
        'ma60': 11.80,
        'volume_ratio': 1.8,
        'price_position': 65.0,
        'trend': 'up'
    }
    
    print("🤖 DeepSeek AI股票分析测试")
    print("=" * 50)
    
    # 进行分析
    analysis = await ai_engine.analyze_stock(stock_data, technical_data)
    
    print(f"股票: {analysis['symbol']}")
    print(f"建议: {analysis['action']}")
    print(f"信心度: {analysis['confidence']}%")
    print(f"理由: {', '.join(analysis['reasons'])}")
    print(f"风险: {', '.join(analysis['risks'])}")
    print(f"AI引擎: {analysis['ai_engine']}")

if __name__ == "__main__":
    asyncio.run(main())
