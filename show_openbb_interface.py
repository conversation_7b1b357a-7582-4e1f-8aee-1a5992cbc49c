#!/usr/bin/env python3
"""
OpenBB界面展示启动器
一键启动OpenBB功能演示和Web界面
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    📊 OpenBB 界面展示                        ║
    ║                                                              ║
    ║              了解OpenBB Platform的功能和界面                 ║
    ║                                                              ║
    ║  包含内容:                                                   ║
    ║  • OpenBB模块结构和API                                       ║
    ║  • 股票数据获取演示                                          ║
    ║  • 新闻和技术分析功能                                        ║
    ║  • Web界面原型                                               ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖包...")
    
    required_packages = ['fastapi', 'uvicorn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n📦 安装缺失的依赖包...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", 
                "fastapi", "uvicorn[standard]"
            ])
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败")
            return False
    
    return True

def run_openbb_demo():
    """运行OpenBB功能演示"""
    print("\n🚀 启动OpenBB功能演示...")
    
    try:
        # 运行OpenBB界面演示
        result = subprocess.run([
            sys.executable, "openbb_interface_demo.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ OpenBB功能演示完成")
            print("\n" + "="*60)
            print("📋 演示输出:")
            print("="*60)
            print(result.stdout)
        else:
            print("❌ OpenBB功能演示失败")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("⏰ OpenBB演示超时")
    except Exception as e:
        print(f"❌ 运行演示时出错: {e}")

def start_web_interface():
    """启动Web界面"""
    print("\n🌐 启动OpenBB Web界面...")
    
    try:
        # 启动Web服务器
        process = subprocess.Popen([
            sys.executable, "openbb_web_interface.py"
        ])
        
        # 等待服务器启动
        print("⏳ 等待Web服务器启动...")
        time.sleep(3)
        
        # 打开浏览器
        web_url = "http://localhost:8080"
        print(f"🌐 打开浏览器: {web_url}")
        webbrowser.open(web_url)
        
        print("\n" + "="*60)
        print("🎉 OpenBB Web界面已启动!")
        print("="*60)
        print(f"📱 访问地址: {web_url}")
        print("🔧 功能包括:")
        print("  • 股票数据查询")
        print("  • 实时报价显示")
        print("  • 财经新闻获取")
        print("  • 技术分析演示")
        print("  • API使用示例")
        print("\n按 Ctrl+C 停止服务器")
        
        # 保持服务器运行
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 正在停止Web服务器...")
            process.terminate()
            process.wait()
            print("✅ Web服务器已停止")
            
    except Exception as e:
        print(f"❌ 启动Web界面失败: {e}")

def show_openbb_structure():
    """显示OpenBB项目结构"""
    print("\n📁 OpenBB项目结构概览")
    print("="*60)
    
    structure = """
    openbb_platform/
    ├── openbb/                    # 主要API包
    │   ├── package/              # 功能模块
    │   │   ├── equity.py         # 股票数据
    │   │   ├── news.py           # 新闻数据
    │   │   ├── economy.py        # 经济数据
    │   │   ├── crypto.py         # 加密货币
    │   │   └── ...
    │   └── __init__.py
    ├── extensions/               # 扩展模块
    │   ├── equity/              # 股票扩展
    │   ├── technical/           # 技术分析
    │   ├── news/                # 新闻扩展
    │   └── ...
    ├── core/                    # 核心框架
    └── providers/               # 数据提供商
        ├── yfinance/           # Yahoo Finance
        ├── alpha_vantage/      # Alpha Vantage
        ├── fmp/                # Financial Modeling Prep
        └── ...
    
    examples/                    # 使用示例
    ├── loadHistoricalPriceData.ipynb
    ├── portfolioOptimization.ipynb
    ├── streamlit/              # Streamlit应用示例
    └── ...
    """
    
    print(structure)

def show_api_examples():
    """显示API使用示例"""
    print("\n💡 OpenBB API 使用示例")
    print("="*60)
    
    examples = {
        "基础股票数据": """
# 导入OpenBB
from openbb import obb

# 获取股票报价
quote = obb.equity.price.quote(symbol="AAPL", provider="yfinance")
print(f"价格: ${quote.results[0].last_price}")

# 获取历史数据
hist = obb.equity.price.historical(
    symbol="AAPL", 
    start_date="2024-01-01",
    provider="yfinance"
)
df = hist.to_df()
print(df.head())
        """,
        
        "中国A股数据": """
# 获取中国股票数据
# 平安银行 (深交所)
quote_sz = obb.equity.price.quote(symbol="000001.SZ", provider="yfinance")

# 贵州茅台 (上交所)  
quote_ss = obb.equity.price.quote(symbol="600519.SS", provider="yfinance")

# 获取A股历史数据
hist_china = obb.equity.price.historical(
    symbol="000001.SZ",
    start_date="2024-01-01", 
    provider="yfinance"
)
        """,
        
        "新闻和情感分析": """
# 获取全球财经新闻
world_news = obb.news.world(provider="biztoc", limit=10)

# 获取特定公司新闻
company_news = obb.news.company(
    symbol="AAPL", 
    provider="benzinga", 
    limit=5
)

# 处理新闻数据
for article in world_news.results:
    print(f"标题: {article.title}")
    print(f"时间: {article.date}")
        """,
        
        "技术分析": """
# 获取历史数据用于技术分析
hist = obb.equity.price.historical(symbol="AAPL", provider="yfinance")
data = hist.to_df()

# 计算技术指标 (需要安装technical扩展)
# pip install openbb[technical]

# RSI指标
rsi = obb.technical.rsi(data=data.to_dict('records'), length=14)

# MACD指标
macd = obb.technical.macd(data=data.to_dict('records'))

# 移动平均线
ma20 = obb.technical.sma(data=data.to_dict('records'), length=20)
        """
    }
    
    for title, code in examples.items():
        print(f"\n📝 {title}:")
        print(code)

def main():
    """主函数"""
    print_banner()
    
    print("选择要查看的内容:")
    print("1. OpenBB功能演示 (命令行)")
    print("2. OpenBB Web界面 (浏览器)")
    print("3. 项目结构概览")
    print("4. API使用示例")
    print("5. 全部展示")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-5): ").strip()
            
            if choice == "0":
                print("👋 退出")
                break
            elif choice == "1":
                if check_dependencies():
                    run_openbb_demo()
            elif choice == "2":
                if check_dependencies():
                    start_web_interface()
            elif choice == "3":
                show_openbb_structure()
            elif choice == "4":
                show_api_examples()
            elif choice == "5":
                show_openbb_structure()
                show_api_examples()
                if check_dependencies():
                    run_openbb_demo()
                    input("\n按回车键继续启动Web界面...")
                    start_web_interface()
            else:
                print("❌ 无效选择，请输入0-5")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    main()
