import React from 'react';
import { Card, List, Tag, Typography, Space, Avatar, Badge, Empty, Tooltip } from 'antd';
import { 
  RobotOutlined, 
  TrophyOutlined, 
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Text, Title } = Typography;

interface TradingSignal {
  symbol: string;
  price: number;
  recommendation: string;
  confidence: number;
  reasoning: string[];
  risks: string[];
  timestamp: string;
  auto_executed: boolean;
}

interface Props {
  notifications: TradingSignal[];
}

const TradingNotifications: React.FC<Props> = ({ notifications }) => {
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return '#52c41a';
    if (confidence >= 60) return '#faad14';
    return '#ff4d4f';
  };

  const getConfidenceTag = (confidence: number) => {
    if (confidence >= 80) return { color: 'success', text: '高信心' };
    if (confidence >= 60) return { color: 'warning', text: '中信心' };
    return { color: 'error', text: '低信心' };
  };

  const formatTimestamp = (timestamp: string) => {
    return dayjs(timestamp).format('MM-DD HH:mm:ss');
  };

  if (notifications.length === 0) {
    return (
      <Card>
        <Empty
          image={<RobotOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />}
          description={
            <span>
              暂无AI交易建议
              <br />
              <Text type="secondary">AI正在分析市场，请稍候...</Text>
            </span>
          }
        />
      </Card>
    );
  }

  return (
    <div>
      <Card 
        title={
          <Space>
            <RobotOutlined style={{ color: '#1890ff' }} />
            <span>AI交易通知</span>
            <Badge count={notifications.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        extra={
          <Text type="secondary">
            实时AI分析建议
          </Text>
        }
      >
        <List
          itemLayout="vertical"
          size="large"
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showQuickJumper: true,
          }}
          dataSource={notifications}
          renderItem={(item, index) => {
            const confidenceTag = getConfidenceTag(item.confidence);
            
            return (
              <List.Item
                key={index}
                style={{
                  background: index === 0 ? '#f6ffed' : 'white',
                  border: index === 0 ? '1px solid #b7eb8f' : '1px solid #f0f0f0',
                  borderRadius: 8,
                  marginBottom: 16,
                  padding: 16
                }}
                actions={[
                  <Space key="confidence">
                    <TrophyOutlined style={{ color: getConfidenceColor(item.confidence) }} />
                    <Text>信心度: {item.confidence}%</Text>
                  </Space>,
                  <Space key="time">
                    <ClockCircleOutlined />
                    <Text type="secondary">{formatTimestamp(item.timestamp)}</Text>
                  </Space>,
                  item.auto_executed && (
                    <Tag color="green" key="executed">
                      <DollarOutlined /> 已自动执行
                    </Tag>
                  )
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <Avatar 
                      style={{ 
                        backgroundColor: getConfidenceColor(item.confidence),
                        fontSize: 16
                      }}
                      icon={<RobotOutlined />}
                    />
                  }
                  title={
                    <Space>
                      <Title level={4} style={{ margin: 0 }}>
                        {item.symbol}
                      </Title>
                      <Tag color={confidenceTag.color}>
                        {confidenceTag.text}
                      </Tag>
                      {index === 0 && <Tag color="red">最新</Tag>}
                    </Space>
                  }
                  description={
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <Space>
                        <Text strong>建议: </Text>
                        <Tag color={item.recommendation === '买入' ? 'green' : 'blue'}>
                          {item.recommendation}
                        </Tag>
                        <Text strong>价格: </Text>
                        <Text code>¥{item.price.toFixed(2)}</Text>
                      </Space>
                    </Space>
                  }
                />
                
                <div style={{ marginTop: 12 }}>
                  <Title level={5}>
                    <TrophyOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                    AI分析理由:
                  </Title>
                  <ul style={{ marginLeft: 16, marginBottom: 16 }}>
                    {item.reasoning.map((reason, idx) => (
                      <li key={idx}>
                        <Text>{reason}</Text>
                      </li>
                    ))}
                  </ul>
                  
                  {item.risks && item.risks.length > 0 && (
                    <>
                      <Title level={5}>
                        <ExclamationCircleOutlined style={{ marginRight: 8, color: '#ff4d4f' }} />
                        风险提示:
                      </Title>
                      <ul style={{ marginLeft: 16 }}>
                        {item.risks.map((risk, idx) => (
                          <li key={idx}>
                            <Text type="warning">{risk}</Text>
                          </li>
                        ))}
                      </ul>
                    </>
                  )}
                </div>
              </List.Item>
            );
          }}
        />
      </Card>
    </div>
  );
};

export default TradingNotifications;
