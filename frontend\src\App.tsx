import React, { useState, useEffect } from 'react';
import { Layout, Menu, Card, Row, Col, Typography, Badge, Button, Input, Space, Spin, Alert } from 'antd';
import { 
  DashboardOutlined, 
  StockOutlined, 
  BellOutlined, 
  Bar<PERSON><PERSON>Outlined,
  RobotOutlined,
  SearchOutlined
} from '@ant-design/icons';
import './App.css';
import TradingNotifications from './components/TradingNotifications';
import StockAnalysis from './components/StockAnalysis';
import Portfolio from './components/Portfolio';
import Dashboard from './components/Dashboard';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;
const { Search } = Input;

interface WebSocketMessage {
  type: string;
  data: any;
}

const App: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const [notifications, setNotifications] = useState<any[]>([]);
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [searchSymbol, setSearchSymbol] = useState('');

  // WebSocket连接
  useEffect(() => {
    const connectWebSocket = () => {
      // 自动获取当前主机地址，适配群晖部署
      const wsUrl = `ws://${window.location.host}/ws`;
      const websocket = new WebSocket(wsUrl);
      
      websocket.onopen = () => {
        console.log('WebSocket连接已建立');
        setConnectionStatus('connected');
        setWs(websocket);
      };

      websocket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          console.log('收到WebSocket消息:', message);
          
          if (message.type === 'buy_signal') {
            setNotifications(prev => [message.data, ...prev.slice(0, 19)]); // 保留最近20条
            
            // 显示浏览器通知
            if (Notification.permission === 'granted') {
              new Notification('🤖 AI买入建议', {
                body: `${message.data.symbol} - 信心度: ${message.data.confidence}%`,
                icon: '/favicon.ico'
              });
            }
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      websocket.onclose = () => {
        console.log('WebSocket连接已关闭');
        setConnectionStatus('disconnected');
        setWs(null);
        
        // 5秒后重连
        setTimeout(connectWebSocket, 5000);
      };

      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        setConnectionStatus('disconnected');
      };
    };

    connectWebSocket();

    // 请求通知权限
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }

    return () => {
      if (ws) {
        ws.close();
      }
    };
  }, []);

  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'analysis',
      icon: <BarChartOutlined />,
      label: '股票分析',
    },
    {
      key: 'portfolio',
      icon: <StockOutlined />,
      label: '投资组合',
    },
    {
      key: 'notifications',
      icon: <Badge count={notifications.length} size="small"><BellOutlined /></Badge>,
      label: '交易通知',
    },
  ];

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <Dashboard notifications={notifications} />;
      case 'analysis':
        return <StockAnalysis searchSymbol={searchSymbol} />;
      case 'portfolio':
        return <Portfolio />;
      case 'notifications':
        return <TradingNotifications notifications={notifications} />;
      default:
        return <Dashboard notifications={notifications} />;
    }
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return '#52c41a';
      case 'connecting': return '#faad14';
      case 'disconnected': return '#ff4d4f';
      default: return '#d9d9d9';
    }
  };

  const getConnectionStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'AI已连接';
      case 'connecting': return '连接中...';
      case 'disconnected': return 'AI已断开';
      default: return '未知状态';
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        collapsible 
        collapsed={collapsed} 
        onCollapse={setCollapsed}
        theme="dark"
        width={250}
      >
        <div style={{ 
          height: 64, 
          margin: 16, 
          display: 'flex', 
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start'
        }}>
          <RobotOutlined style={{ fontSize: 24, color: '#1890ff', marginRight: collapsed ? 0 : 8 }} />
          {!collapsed && (
            <Title level={4} style={{ color: 'white', margin: 0 }}>
              智能选股
            </Title>
          )}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
        />
      </Sider>
      
      <Layout>
        <Header style={{ 
          padding: '0 24px', 
          background: '#fff', 
          display: 'flex', 
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}>
          <Space>
            <Title level={3} style={{ margin: 0 }}>
              {menuItems.find(item => item.key === selectedKey)?.label}
            </Title>
          </Space>
          
          <Space>
            <Search
              placeholder="搜索股票代码"
              allowClear
              enterButton={<SearchOutlined />}
              size="middle"
              style={{ width: 300 }}
              value={searchSymbol}
              onChange={(e) => setSearchSymbol(e.target.value)}
              onSearch={(value) => {
                setSearchSymbol(value);
                setSelectedKey('analysis');
              }}
            />
            
            <Badge 
              color={getConnectionStatusColor()} 
              text={getConnectionStatusText()}
            />
          </Space>
        </Header>
        
        <Content style={{ 
          margin: '24px', 
          padding: 24, 
          background: '#f0f2f5',
          minHeight: 'calc(100vh - 112px)'
        }}>
          {connectionStatus === 'disconnected' && (
            <Alert
              message="AI服务连接中断"
              description="正在尝试重新连接AI分析服务，某些功能可能暂时不可用。"
              type="warning"
              showIcon
              style={{ marginBottom: 16 }}
            />
          )}
          
          {renderContent()}
        </Content>
      </Layout>
    </Layout>
  );
};

export default App;
