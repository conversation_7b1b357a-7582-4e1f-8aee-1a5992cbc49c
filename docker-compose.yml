version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:15-alpine
    container_name: stock_postgres
    environment:
      POSTGRES_DB: intelligent_stock
      POSTGRES_USER: stock_user
      POSTGRES_PASSWORD: stock_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - stock_network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: stock_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - stock_network

  # Ollama本地AI模型服务
  ollama:
    image: ollama/ollama:latest
    container_name: stock_ollama
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - stock_network
    environment:
      - OLLAMA_HOST=0.0.0.0
    # 启动后自动拉取模型
    command: >
      sh -c "ollama serve & 
             sleep 10 && 
             ollama pull qwen2.5:7b-instruct && 
             wait"

  # 后端API服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: stock_backend
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************************/intelligent_stock
      - REDIS_URL=redis://redis:6379/0
      - OLLAMA_URL=http://ollama:11434
    depends_on:
      - postgres
      - redis
      - ollama
    volumes:
      - ./:/app
    networks:
      - stock_network
    restart: unless-stopped

  # Celery异步任务处理
  celery:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: stock_celery
    command: celery -A celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/intelligent_stock
      - REDIS_URL=redis://redis:6379/0
      - OLLAMA_URL=http://ollama:11434
    depends_on:
      - postgres
      - redis
      - ollama
    volumes:
      - ./:/app
    networks:
      - stock_network
    restart: unless-stopped

  # Celery Beat调度器
  celery-beat:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: stock_celery_beat
    command: celery -A celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=****************************************************/intelligent_stock
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - postgres
      - redis
    volumes:
      - ./:/app
    networks:
      - stock_network
    restart: unless-stopped

  # 前端React应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: stock_frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000
    depends_on:
      - backend
    networks:
      - stock_network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: stock_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - stock_network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  ollama_data:

networks:
  stock_network:
    driver: bridge
