[tool.poetry]
name = "openbb-index"
version = "1.4.2"
description = "Index extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_index" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.8"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_core_extension"]
index = "openbb_index.index_router:router"

[tool.poetry.plugins."openbb_charting_extension"]
index = "openbb_index.index_views:IndexViews"
