# 🤖 智能选股系统

基于OpenBB数据平台和本地AI的Web端智能股票分析系统

## 🌟 系统特性

### 核心功能
- **实时股票分析**: 整合OpenBB多数据源，提供全面的股票数据
- **AI智能选股**: 本地AI模型分析，生成个性化投资建议
- **自动交易信号**: 实时推送买入/卖出信号，支持模拟交易
- **Web端监控**: 现代化Web界面，实时数据展示
- **风险控制**: 多层风险评估，智能仓位管理

### 技术架构
- **后端**: FastAPI + OpenBB Platform + 本地AI (Qwen2.5)
- **前端**: React + TypeScript + Ant Design
- **数据库**: PostgreSQL + Redis
- **AI引擎**: 本地Ollama + Qwen2.5-7B模型
- **部署**: Docker容器化部署

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+ (可选，用于前端)
- Docker & Docker Compose (推荐)
- 8GB+ 内存 (用于AI模型)

### 方式一：一键启动 (推荐)

```bash
# 1. 克隆项目
git clone <项目地址>
cd intelligent-stock-system

# 2. 运行启动脚本
python start.py
```

启动脚本会自动：
- 检查Python环境和依赖
- 安装缺失的包
- 配置Ollama AI服务
- 启动后端和前端服务

### 方式二：Docker部署

```bash
# 1. 构建并启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f backend
```

### 方式三：手动安装

```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 安装并启动Ollama
# 访问 https://ollama.ai/ 下载安装
ollama serve
ollama pull qwen2.5:7b-instruct

# 3. 启动后端
uvicorn intelligent_stock_web_system:app --reload

# 4. 启动前端 (可选)
cd frontend
npm install
npm start
```

## 📊 系统使用

### 访问地址
- **Web界面**: http://localhost:3000 (前端)
- **API服务**: http://localhost:8000 (后端)
- **API文档**: http://localhost:8000/docs

### 主要功能

#### 1. 仪表板
- 实时市场概况
- AI分析统计
- 最新交易信号
- 系统状态监控

#### 2. 股票分析
- 输入股票代码进行分析
- 技术指标计算 (RSI, MACD, 布林带等)
- 基本面数据展示
- AI综合评分和建议

#### 3. 交易通知
- 实时AI买入/卖出建议
- 详细分析理由和风险提示
- 自动模拟交易执行
- 历史信号记录

#### 4. 投资组合
- 模拟持仓管理
- 收益率统计
- 风险分析

## 🔧 配置说明

### OpenBB数据源配置

系统支持多个数据提供商，在使用前需要配置API密钥：

```python
# 在环境变量中设置
export OPENBB_FMP_API_KEY="your_fmp_key"
export OPENBB_POLYGON_API_KEY="your_polygon_key"
export OPENBB_ALPHA_VANTAGE_API_KEY="your_av_key"
```

### AI模型配置

默认使用Qwen2.5-7B模型，可以根据需要调整：

```python
# 在 ai_engine/local_llm.py 中修改
model_name = "qwen2.5:7b-instruct"  # 可改为其他模型
```

### 股票池配置

在 `intelligent_stock_web_system.py` 中修改监控的股票列表：

```python
stock_pool = [
    "000001.SZ",  # 平安银行
    "600519.SS",  # 贵州茅台
    # 添加更多股票代码
]
```

## 📈 系统架构

### 数据流程
```
市场数据 → OpenBB Platform → 技术/基本面分析 → AI模型分析 → 交易信号 → Web推送
```

### 核心模块

1. **数据采集层** (`OpenBBDataProvider`)
   - 实时行情数据
   - 历史价格数据
   - 技术指标计算
   - 基本面数据
   - 新闻情感分析

2. **AI分析层** (`LocalLLMClient`)
   - 本地大模型推理
   - 股票综合分析
   - 投资建议生成
   - 风险评估

3. **决策引擎** (`IntelligentDecisionEngine`)
   - 多因子评分模型
   - 风险控制机制
   - 仓位管理
   - 交易信号生成

4. **Web服务层** (`FastAPI`)
   - RESTful API
   - WebSocket实时推送
   - 用户界面服务

## 🛠️ 开发指南

### 添加新的技术指标

```python
# 在 OpenBBDataProvider 中添加
async def get_custom_indicator(self, symbol: str):
    df = await self.get_historical_data(symbol)
    # 使用OpenBB技术分析功能
    result = obb.technical.your_indicator(data=df.to_dict('records'))
    return result
```

### 自定义AI分析提示词

```python
# 在 ai_engine/local_llm.py 中修改
def _build_stock_analysis_prompt(self, stock_data: Dict) -> str:
    return f"""
    你是专业的投资分析师，请分析：
    {stock_data}
    
    请提供：
    1. 投资建议
    2. 详细理由
    3. 风险评估
    """
```

### 扩展数据源

```python
# 添加新的数据提供商
class CustomDataProvider:
    async def get_data(self, symbol: str):
        # 实现自定义数据获取逻辑
        pass
```

## 🔒 风险控制

### 内置风控机制
- **仓位限制**: 单只股票最大15%仓位
- **行业分散**: 单个行业最大30%仓位
- **信心度过滤**: 只执行高信心度(>70%)的信号
- **止损机制**: 自动8%止损
- **流动性检查**: 过滤低流动性股票

### 免责声明
⚠️ **重要提示**: 
- 本系统仅供学习和研究使用
- 所有投资建议仅为模拟分析，不构成实际投资建议
- 投资有风险，决策需谨慎
- 使用者需自行承担投资风险

## 📞 技术支持

### 常见问题

**Q: AI服务连接失败？**
A: 检查Ollama服务是否运行，确保模型已下载

**Q: 数据获取失败？**
A: 检查网络连接和API密钥配置

**Q: 前端无法连接后端？**
A: 确认后端服务在8000端口正常运行

### 日志查看

```bash
# Docker环境
docker-compose logs -f backend

# 本地环境
tail -f logs/app.log
```

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统！

### 开发环境设置
```bash
# 1. Fork项目
# 2. 创建开发分支
git checkout -b feature/your-feature

# 3. 安装开发依赖
pip install -r requirements-dev.txt

# 4. 运行测试
pytest tests/

# 5. 提交代码
git commit -m "Add your feature"
git push origin feature/your-feature
```

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

---

**Happy Trading! 🚀📈**
