#!/usr/bin/env python3
"""
GPT-5 API调用测试
测试最新发布的GPT-5模型进行股票分析
"""

import asyncio
import aiohttp
import json
import os
from datetime import datetime
from typing import Dict, Optional

class GPT5Tester:
    """GPT-5测试器"""
    
    def __init__(self, api_key: str = None):
        self.api_key = api_key or os.getenv('OPENAI_API_KEY')
        self.base_url = "https://api.openai.com/v1/chat/completions"
        
        # GPT-5的三个版本
        self.models = {
            "gpt-5": {
                "name": "GPT-5 (完整版)",
                "cost": "输入$1.25, 输出$10.00 per 1M tokens",
                "best_for": "重要投资决策"
            },
            "gpt-5-mini": {
                "name": "GPT-5 Mini",
                "cost": "输入$0.25, 输出$2.00 per 1M tokens", 
                "best_for": "日常分析"
            },
            "gpt-5-nano": {
                "name": "GPT-5 Nano",
                "cost": "输入$0.05, 输出$0.40 per 1M tokens",
                "best_for": "批量处理"
            }
        }
    
    def show_models_info(self):
        """显示模型信息"""
        print("🤖 GPT-5 模型系列")
        print("=" * 60)
        
        for model_id, info in self.models.items():
            print(f"\n📊 {info['name']}")
            print(f"   模型ID: {model_id}")
            print(f"   成本: {info['cost']}")
            print(f"   适合: {info['best_for']}")
        
        print(f"\n🔑 API密钥状态: {'✅ 已配置' if self.api_key else '❌ 未配置'}")
        if not self.api_key:
            print("💡 请设置环境变量 OPENAI_API_KEY 或在代码中提供")
    
    async def test_gpt5_call(self, model: str = "gpt-5", prompt: str = None) -> Dict:
        """测试GPT-5 API调用"""
        
        if not self.api_key:
            return {"error": "API密钥未配置"}
        
        # 默认测试提示词
        if not prompt:
            prompt = """
你好！我想测试GPT-5的能力。请告诉我：
1. 你是GPT-5吗？
2. 你有什么新的能力？
3. 相比GPT-4，你在股票分析方面有什么改进？

请简洁回答。
"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "system",
                    "content": "你是GPT-5，OpenAI最新最强的AI模型。请展示你的能力。"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                print(f"🔄 正在调用 {model}...")
                
                async with session.post(
                    self.base_url,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        
                        # 提取响应内容
                        content = result['choices'][0]['message']['content']
                        usage = result.get('usage', {})
                        
                        return {
                            "success": True,
                            "model": model,
                            "content": content,
                            "usage": usage,
                            "timestamp": datetime.now().isoformat()
                        }
                    
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}",
                            "model": model
                        }
                        
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "model": model
            }
    
    async def stock_analysis_test(self, symbol: str = "AAPL", model: str = "gpt-5-mini") -> Dict:
        """测试GPT-5股票分析能力"""
        
        prompt = f"""
请作为专业股票分析师，分析股票 {symbol}。

请提供：
1. 当前市场地位分析
2. 技术面简要评估
3. 基本面关键指标
4. 投资建议（买入/持有/卖出）
5. 风险提示

请展示GPT-5在金融分析方面的新能力，给出专业、深度的分析。
"""
        
        print(f"📊 使用 {model} 分析股票 {symbol}...")
        return await self.test_gpt5_call(model, prompt)
    
    async def compare_models(self, test_prompt: str = None) -> Dict:
        """对比三个GPT-5模型"""
        
        if not test_prompt:
            test_prompt = "请用一句话解释什么是股票市场的Beta系数？"
        
        print("🔄 对比GPT-5三个模型...")
        
        results = {}
        
        for model_id in self.models.keys():
            print(f"   测试 {model_id}...")
            result = await self.test_gpt5_call(model_id, test_prompt)
            results[model_id] = result
            
            # 避免API频率限制
            await asyncio.sleep(1)
        
        return results
    
    def display_result(self, result: Dict):
        """显示测试结果"""
        
        if not result.get("success"):
            print(f"❌ 调用失败: {result.get('error')}")
            return
        
        print(f"✅ {result['model']} 调用成功!")
        print(f"📝 响应内容:")
        print("-" * 40)
        print(result['content'])
        print("-" * 40)
        
        # 显示用量信息
        usage = result.get('usage', {})
        if usage:
            print(f"📊 Token使用量:")
            print(f"   输入: {usage.get('prompt_tokens', 0)} tokens")
            print(f"   输出: {usage.get('completion_tokens', 0)} tokens")
            print(f"   总计: {usage.get('total_tokens', 0)} tokens")
            
            # 估算成本
            self.estimate_cost(result['model'], usage)
    
    def estimate_cost(self, model: str, usage: Dict):
        """估算API调用成本"""
        
        prompt_tokens = usage.get('prompt_tokens', 0)
        completion_tokens = usage.get('completion_tokens', 0)
        
        # GPT-5价格表 (per 1M tokens)
        prices = {
            "gpt-5": {"input": 1.25, "output": 10.00},
            "gpt-5-mini": {"input": 0.25, "output": 2.00},
            "gpt-5-nano": {"input": 0.05, "output": 0.40}
        }
        
        if model in prices:
            input_cost = (prompt_tokens / 1000000) * prices[model]["input"]
            output_cost = (completion_tokens / 1000000) * prices[model]["output"]
            total_cost = input_cost + output_cost
            
            print(f"💰 估算成本: ${total_cost:.6f}")
            print(f"   输入成本: ${input_cost:.6f}")
            print(f"   输出成本: ${output_cost:.6f}")

async def main():
    """主函数"""
    
    print("🚀 GPT-5 API调用测试")
    print("=" * 60)
    
    # 检查API密钥
    api_key = os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ 未找到OpenAI API密钥")
        print("💡 请设置环境变量:")
        print("   export OPENAI_API_KEY='your-api-key-here'")
        print("   或在Windows中: set OPENAI_API_KEY=your-api-key-here")
        
        # 提供手动输入选项
        manual_key = input("\n🔑 或者现在输入API密钥 (回车跳过): ").strip()
        if manual_key:
            api_key = manual_key
        else:
            print("⏭️  跳过API测试")
            return
    
    # 创建测试器
    tester = GPT5Tester(api_key)
    
    # 显示模型信息
    tester.show_models_info()
    
    print("\n" + "=" * 60)
    print("选择测试项目:")
    print("1. 基础GPT-5测试")
    print("2. 股票分析测试")
    print("3. 模型对比测试")
    print("4. 全部测试")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 退出测试")
                break
                
            elif choice == "1":
                print("\n🧪 基础GPT-5测试")
                result = await tester.test_gpt5_call("gpt-5")
                tester.display_result(result)
                
            elif choice == "2":
                symbol = input("输入股票代码 (默认AAPL): ").strip() or "AAPL"
                print(f"\n📊 股票分析测试: {symbol}")
                result = await tester.stock_analysis_test(symbol, "gpt-5-mini")
                tester.display_result(result)
                
            elif choice == "3":
                print("\n🔄 模型对比测试")
                results = await tester.compare_models()
                
                for model_id, result in results.items():
                    print(f"\n📊 {tester.models[model_id]['name']} 结果:")
                    tester.display_result(result)
                    
            elif choice == "4":
                print("\n🚀 全部测试")
                
                # 基础测试
                print("\n1️⃣ 基础测试...")
                result1 = await tester.test_gpt5_call("gpt-5")
                tester.display_result(result1)
                
                # 股票分析测试
                print("\n2️⃣ 股票分析测试...")
                result2 = await tester.stock_analysis_test("AAPL", "gpt-5-mini")
                tester.display_result(result2)
                
                # 模型对比
                print("\n3️⃣ 模型对比测试...")
                results3 = await tester.compare_models()
                for model_id, result in results3.items():
                    print(f"\n📊 {model_id}:")
                    if result.get("success"):
                        print(f"✅ 成功 - {result['content'][:100]}...")
                    else:
                        print(f"❌ 失败 - {result.get('error')}")
                
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
