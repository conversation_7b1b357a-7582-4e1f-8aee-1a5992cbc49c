"""
本地大模型接口
使用Ollama部署Qwen2.5-7B模型进行股票分析
"""

import requests
import json
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass


@dataclass
class ModelResponse:
    """模型响应数据结构"""
    content: str
    confidence: float
    reasoning: List[str]
    recommendations: Dict


class LocalLLMClient:
    """本地大模型客户端"""

    def __init__(self, model_name: str = "qwen2.5:7b-instruct", host: str = "localhost", port: int = 11434):
        """
        初始化本地大模型客户端

        支持的模型:
        - qwen2.5:7b-instruct (推荐，中文友好)
        - qwen2.5:14b-instruct (更强性能)
        - llama3.1:8b-instruct (Meta开源)
        - deepseek-coder:6.7b-instruct (代码专业)
        - yi:6b-chat (零一万物)
        - baichuan2:7b-chat (百川智能)
        """
        self.model_name = model_name
        self.base_url = f"http://{host}:{port}"
        self.session = requests.Session()
        self.logger = logging.getLogger(__name__)
        
        # 检查模型是否可用
        self._check_model_availability()
    
    def _check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                models = response.json().get('models', [])
                available_models = [model['name'] for model in models]
                
                if self.model_name in available_models:
                    self.logger.info(f"模型 {self.model_name} 可用")
                    return True
                else:
                    self.logger.warning(f"模型 {self.model_name} 不可用，可用模型: {available_models}")
                    return False
            else:
                self.logger.error("无法连接到Ollama服务")
                return False
        except Exception as e:
            self.logger.error(f"检查模型可用性失败: {e}")
            return False
    
    def generate(self, prompt: str, temperature: float = 0.7, max_tokens: int = 1000) -> str:
        """生成文本响应"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result.get('response', '')
            else:
                self.logger.error(f"生成失败: {response.status_code}")
                return ""
                
        except Exception as e:
            self.logger.error(f"生成文本失败: {e}")
            return ""
    
    def analyze_stock_decision(self, stock_data: Dict) -> ModelResponse:
        """分析股票投资决策"""
        prompt = self._build_stock_analysis_prompt(stock_data)
        response_text = self.generate(prompt, temperature=0.3)
        
        # 解析响应
        return self._parse_stock_analysis_response(response_text)
    
    def _build_stock_analysis_prompt(self, stock_data: Dict) -> str:
        """构建股票分析提示词"""
        return f"""
你是一位专业的量化投资分析师，请分析以下股票数据并给出投资建议。

股票信息：
- 代码：{stock_data.get('code', 'N/A')}
- 名称：{stock_data.get('name', 'N/A')}
- 当前价格：{stock_data.get('price', 0):.2f}元
- 涨跌幅：{stock_data.get('pct_change', 0):.2f}%

技术指标：
- RSI：{stock_data.get('rsi', 0):.2f}
- MACD：{stock_data.get('macd', 0):.4f}
- 成交量比率：{stock_data.get('volume_ratio', 0):.2f}
- 20日均线：{stock_data.get('ma20', 0):.2f}
- 布林带位置：{stock_data.get('bb_position', 0):.2f}

基本面数据：
- PE比率：{stock_data.get('pe', 0):.2f}
- PB比率：{stock_data.get('pb', 0):.2f}
- ROE：{stock_data.get('roe', 0):.2f}%
- 营收增长率：{stock_data.get('revenue_growth', 0):.2f}%

资金流向：
- 主力净流入：{stock_data.get('net_inflow', 0):.2f}万元
- 换手率：{stock_data.get('turnover_rate', 0):.2f}%

请按以下格式分析：

1. 投资建议：[买入/观望/卖出]
2. 信心等级：[1-10分]
3. 主要理由：
   - 理由1：[具体说明]
   - 理由2：[具体说明]
   - 理由3：[具体说明]
4. 风险提示：
   - 风险1：[具体说明]
   - 风险2：[具体说明]
5. 目标价位：[具体价格]
6. 止损价位：[具体价格]
7. 预期持有时间：[短线/中线/长线]
8. 建议仓位：[轻仓/中仓/重仓]

请基于数据进行客观分析，避免过度乐观或悲观。
"""
    
    def _parse_stock_analysis_response(self, response_text: str) -> ModelResponse:
        """解析股票分析响应"""
        try:
            # 简单的文本解析，提取关键信息
            lines = response_text.strip().split('\n')
            
            # 提取投资建议
            recommendation = "观望"
            confidence = 5.0
            reasons = []
            risks = []
            
            for line in lines:
                line = line.strip()
                if "投资建议" in line:
                    if "买入" in line:
                        recommendation = "买入"
                    elif "卖出" in line:
                        recommendation = "卖出"
                elif "信心等级" in line:
                    try:
                        confidence = float([x for x in line if x.isdigit()][0])
                    except:
                        confidence = 5.0
                elif "理由" in line and "：" in line:
                    reason = line.split("：", 1)[1]
                    if reason:
                        reasons.append(reason)
                elif "风险" in line and "：" in line:
                    risk = line.split("：", 1)[1]
                    if risk:
                        risks.append(risk)
            
            return ModelResponse(
                content=response_text,
                confidence=confidence * 10,  # 转换为百分比
                reasoning=reasons,
                recommendations={
                    "action": recommendation,
                    "risks": risks
                }
            )
            
        except Exception as e:
            self.logger.error(f"解析响应失败: {e}")
            return ModelResponse(
                content=response_text,
                confidence=50.0,
                reasoning=["解析失败"],
                recommendations={"action": "观望", "risks": ["数据解析错误"]}
            )
    
    def generate_buy_explanation(self, stock_data: Dict, decision_data: Dict) -> str:
        """生成买入决策解释"""
        prompt = f"""
我的量化系统建议买入 {stock_data.get('name')}({stock_data.get('code')})，
请生成一个简洁明了的买入理由说明。

当前情况：
- 股价：{stock_data.get('price', 0):.2f}元
- 建议买入：{decision_data.get('quantity', 0)}股
- 投资金额：{decision_data.get('amount', 0):.2f}元
- AI信心度：{decision_data.get('confidence', 0):.1f}%

技术面信号：{decision_data.get('technical_signals', '无')}
基本面因素：{decision_data.get('fundamental_factors', '无')}

请生成一个投资者容易理解的买入通知，包括：
1. 核心买入理由（2-3个要点）
2. 主要风险提示（1-2个要点）
3. 预期时间框架

语言要专业但通俗，适合快速阅读。字数控制在200字以内。
"""
        
        return self.generate(prompt, temperature=0.5, max_tokens=300)


# 使用示例
if __name__ == "__main__":
    # 初始化本地LLM客户端
    llm_client = LocalLLMClient()
    
    # 测试股票数据
    test_stock_data = {
        'code': '000001',
        'name': '平安银行',
        'price': 12.50,
        'pct_change': 2.5,
        'rsi': 65.5,
        'macd': 0.15,
        'volume_ratio': 1.2,
        'pe': 8.5,
        'pb': 0.8,
        'roe': 12.5,
        'net_inflow': 5000
    }
    
    # 分析股票
    result = llm_client.analyze_stock_decision(test_stock_data)
    print("AI分析结果：")
    print(f"建议：{result.recommendations['action']}")
    print(f"信心度：{result.confidence}%")
    print(f"理由：{result.reasoning}")
