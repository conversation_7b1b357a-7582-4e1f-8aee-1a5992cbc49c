# 🤖 AI连接问题解决指南

## 🔍 问题诊断

### 快速测试
```bash
# 1. 快速AI连接测试
python test_ai_connection.py

# 2. 全面诊断和修复
python ai_connection_fix.py
```

## 🚨 常见问题及解决方案

### 问题1: "连接被拒绝" / "Connection refused"

**原因**: Ollama服务未启动

**解决方案**:

#### 本地环境:
```bash
# 检查Ollama是否安装
ollama --version

# 启动Ollama服务
ollama serve

# 在新终端测试
curl http://localhost:11434/api/tags
```

#### 群晖Docker环境:
```bash
# 检查容器状态
docker-compose ps ollama

# 启动Ollama容器
docker-compose up -d ollama

# 查看容器日志
docker-compose logs ollama
```

### 问题2: "未安装任何模型"

**解决方案**:

#### 安装推荐模型:
```bash
# 本地环境
ollama pull qwen2.5:7b-instruct

# Docker环境
docker-compose exec ollama ollama pull qwen2.5:7b-instruct
```

#### 轻量级替代方案:
```bash
# 如果内存不足，使用轻量模型
ollama pull yi:6b-chat
ollama pull phi3:3.8b
```

### 问题3: "端口冲突"

**群晖环境端口映射**:
- 容器内端口: 11434
- 群晖外部端口: 1753

**检查端口**:
```bash
# 检查端口占用
netstat -tuln | grep 1753
netstat -tuln | grep 11434

# 测试连接
curl http://localhost:1753/api/tags  # 群晖
curl http://localhost:11434/api/tags # 本地
```

### 问题4: "模型下载失败"

**原因**: 网络问题或磁盘空间不足

**解决方案**:
```bash
# 检查磁盘空间
df -h

# 检查网络连接
ping ollama.ai

# 手动下载模型
ollama pull qwen2.5:7b-instruct --verbose

# 如果下载失败，尝试轻量模型
ollama pull phi3:3.8b
```

### 问题5: "AI响应超时"

**原因**: 模型太大或系统资源不足

**解决方案**:
```bash
# 检查系统资源
free -h
top

# 使用更轻量的模型
ollama pull yi:6b-chat

# 调整超时设置 (在代码中)
client.generate(prompt, timeout=60)
```

## 🔧 环境特定解决方案

### 群晖NAS环境

#### 1. 检查Docker资源分配
```bash
# 在群晖DSM中:
# 控制面板 → Docker → 设置 → 资源
# 建议分配: 8GB内存, 4CPU核心
```

#### 2. 防火墙配置
```bash
# 群晖控制面板 → 安全性 → 防火墙
# 添加规则: 允许端口1750-1756
```

#### 3. 存储空间检查
```bash
# 确保有足够空间存储AI模型
# 每个7B模型约需要4-5GB空间
```

### Docker环境

#### 1. 容器网络问题
```bash
# 检查容器网络
docker network ls
docker network inspect stock_network

# 重建网络
docker-compose down
docker-compose up -d
```

#### 2. 容器资源限制
```yaml
# 在docker-compose.yml中调整
ollama:
  deploy:
    resources:
      limits:
        memory: 8G
      reservations:
        memory: 4G
```

### 本地开发环境

#### 1. Ollama安装
```bash
# macOS
brew install ollama

# Linux
curl -fsSL https://ollama.ai/install.sh | sh

# Windows
# 下载安装包: https://ollama.ai/download
```

#### 2. 服务管理
```bash
# 启动服务
ollama serve

# 后台运行
nohup ollama serve > ollama.log 2>&1 &

# 检查进程
ps aux | grep ollama
```

## 🛠️ 高级故障排除

### 1. 完全重置Ollama

```bash
# 停止服务
pkill ollama

# 清理数据 (谨慎操作!)
rm -rf ~/.ollama

# 重新启动
ollama serve

# 重新安装模型
ollama pull qwen2.5:7b-instruct
```

### 2. 网络代理问题

```bash
# 如果使用代理
export HTTP_PROXY=http://proxy:port
export HTTPS_PROXY=http://proxy:port

# 或在Docker中设置
docker-compose exec ollama env HTTP_PROXY=http://proxy:port ollama pull model
```

### 3. 权限问题

```bash
# 检查文件权限
ls -la ~/.ollama

# 修复权限
sudo chown -R $USER:$USER ~/.ollama
```

## 📊 性能优化

### 1. 内存优化
```bash
# 限制并发模型数量
export OLLAMA_MAX_LOADED_MODELS=1

# 减少模型上下文长度
export OLLAMA_NUM_PARALLEL=1
```

### 2. 模型选择建议

| 内存大小 | 推荐模型 | 性能 |
|----------|----------|------|
| 4-6GB | phi3:3.8b | 快速响应 |
| 6-8GB | yi:6b-chat | 平衡性能 |
| 8-12GB | qwen2.5:7b-instruct | 推荐配置 |
| 16GB+ | qwen2.5:14b-instruct | 高性能 |

### 3. 群晖优化设置

```bash
# 在.env文件中设置
OLLAMA_NUM_PARALLEL=1
OLLAMA_MAX_LOADED_MODELS=1
DOCKER_MEMORY_LIMIT=8g
```

## 🔍 日志分析

### 查看详细日志
```bash
# Ollama服务日志
journalctl -u ollama -f

# Docker容器日志
docker-compose logs -f ollama

# 应用日志
tail -f logs/app.log
```

### 常见错误信息

| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| "connection refused" | 服务未启动 | 启动Ollama服务 |
| "model not found" | 模型未安装 | 安装对应模型 |
| "out of memory" | 内存不足 | 使用更小模型 |
| "timeout" | 响应超时 | 增加超时时间 |

## 📞 获取帮助

### 自动诊断工具
```bash
# 运行诊断工具
python ai_connection_fix.py

# 生成诊断报告
python synology_check.py
```

### 手动检查清单

- [ ] Ollama服务是否运行
- [ ] 端口是否正确 (11434/1753)
- [ ] 模型是否已安装
- [ ] 网络连接是否正常
- [ ] 系统资源是否充足
- [ ] 防火墙是否允许
- [ ] Docker容器是否健康

### 联系支持

如果问题仍然存在:
1. 运行 `python ai_connection_fix.py` 生成诊断报告
2. 收集相关日志文件
3. 描述具体的错误信息和操作步骤

---

**记住**: 大多数AI连接问题都可以通过重启服务和重新安装模型来解决！ 🚀
