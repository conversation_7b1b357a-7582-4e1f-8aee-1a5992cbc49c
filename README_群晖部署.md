# 🏠 群晖NAS智能选股系统部署指南

## 🌟 系统概述

在群晖NAS上部署基于OpenBB + 本地AI的智能股票分析系统，享受7x24小时不间断的AI选股服务。

### 🎯 端口配置 (1750-1799)

| 端口 | 服务 | 说明 |
|------|------|------|
| **1750** | **Web主入口** | **主要访问端口 (Nginx)** |
| 1751 | PostgreSQL | 数据库服务 |
| 1752 | Redis | 缓存服务 |
| 1753 | Ollama | AI模型服务 |
| 1754 | Backend API | 后端API服务 |
| 1755 | Frontend | 前端React应用 |
| 1756 | HTTPS | 安全访问 (可选) |

## 🚀 一键部署

### 前置要求

1. **群晖DSM 7.0+**
2. **Docker套件** (在套件中心安装)
3. **Container Manager** (新版Docker管理)
4. **8GB+内存** (推荐16GB)
5. **20GB+可用空间**

### 快速部署

```bash
# 1. 下载项目到群晖
git clone <项目地址> /volume1/docker/intelligent-stock

# 2. 进入项目目录
cd /volume1/docker/intelligent-stock

# 3. 执行一键部署脚本
chmod +x synology_deploy.sh
./synology_deploy.sh
```

### 手动部署

```bash
# 1. 检查Docker环境
docker --version
docker-compose --version

# 2. 配置环境变量
cp .env.synology .env

# 3. 启动服务
docker-compose up -d

# 4. 查看服务状态
docker-compose ps
```

## 📱 访问系统

部署完成后，通过以下地址访问：

### 🌐 主要入口
```
http://你的群晖IP:1750
```

### 📊 其他服务
- **API文档**: http://你的群晖IP:1750/docs
- **后端API**: http://你的群晖IP:1754
- **前端应用**: http://你的群晖IP:1755

## 🔧 群晖配置

### 1. 防火墙设置

在群晖控制面板 → 安全性 → 防火墙中：

```
规则名称: 智能选股系统
端口: 1750-1756
协议: TCP
动作: 允许
```

### 2. Docker资源配置

在Container Manager中设置：

```
内存限制: 8GB (推荐)
CPU限制: 4核心
存储: 20GB+
```

### 3. 计划任务 (可选)

设置自动重启任务：

```bash
# 每天凌晨2点重启服务
0 2 * * * cd /volume1/docker/intelligent-stock && docker-compose restart
```

## 🤖 AI模型管理

### 查看已安装模型
```bash
docker-compose exec ollama ollama list
```

### 安装推荐模型
```bash
# 中文优化模型 (推荐)
docker-compose exec ollama ollama pull qwen2.5:7b-instruct

# 轻量级模型
docker-compose exec ollama ollama pull yi:6b-chat

# 专业技术分析
docker-compose exec ollama ollama pull deepseek-coder:6.7b-instruct
```

### 智能模型选择
```bash
# 使用DeepSeek评估最佳模型
docker-compose exec backend python smart_model_installer.py
```

## 📊 系统监控

### 查看服务状态
```bash
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f ollama
```

### 资源监控
```bash
# 查看资源使用
docker stats

# 查看磁盘使用
docker system df
```

### 健康检查
```bash
# 检查API健康状态
curl http://localhost:1754/api/health

# 检查AI服务
curl http://localhost:1753/api/tags
```

## 🔄 维护操作

### 更新系统
```bash
# 拉取最新镜像
docker-compose pull

# 重新构建并启动
docker-compose up -d --build
```

### 备份数据
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U stock_user intelligent_stock > backup.sql

# 备份整个数据目录
tar -czf stock_system_backup.tar.gz data/
```

### 恢复数据
```bash
# 恢复数据库
docker-compose exec -T postgres psql -U stock_user intelligent_stock < backup.sql
```

### 清理系统
```bash
# 清理未使用的镜像
docker system prune -f

# 清理未使用的卷
docker volume prune -f
```

## ⚙️ 高级配置

### 1. 性能优化

编辑 `.env` 文件：

```bash
# 针对群晖优化
WORKER_PROCESSES=2              # 根据CPU核心数调整
DOCKER_MEMORY_LIMIT=8g          # 根据可用内存调整
SCAN_INTERVAL=600               # 增加扫描间隔减少负载
```

### 2. 数据源配置

添加API密钥提高数据质量：

```bash
# 在 .env 文件中配置
OPENBB_FMP_API_KEY=your_fmp_key
OPENBB_POLYGON_API_KEY=your_polygon_key
```

### 3. 通知配置

配置微信/邮件通知：

```bash
# 微信通知
WECHAT_WEBHOOK_URL=your_wechat_webhook

# 邮件通知
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
NOTIFICATION_EMAIL=<EMAIL>
```

## 🔍 故障排除

### 常见问题

**Q: 端口被占用？**
```bash
# 检查端口占用
netstat -tuln | grep 1750

# 修改端口配置
vim docker-compose.yml
```

**Q: 内存不足？**
```bash
# 检查内存使用
free -h

# 调整Docker内存限制
vim .env
```

**Q: AI模型下载失败？**
```bash
# 手动下载模型
docker-compose exec ollama ollama pull qwen2.5:7b-instruct

# 检查网络连接
docker-compose exec ollama curl -I https://ollama.ai
```

**Q: 服务启动失败？**
```bash
# 查看详细日志
docker-compose logs backend

# 重新构建镜像
docker-compose build --no-cache backend
```

### 日志分析

```bash
# 查看系统日志
docker-compose logs --tail=100 -f

# 查看特定服务日志
docker-compose logs backend | grep ERROR

# 导出日志
docker-compose logs > system.log
```

## 📈 性能基准

### 推荐配置

| 配置项 | 最低要求 | 推荐配置 | 高性能配置 |
|--------|----------|----------|------------|
| CPU | 2核心 | 4核心 | 8核心+ |
| 内存 | 4GB | 8GB | 16GB+ |
| 存储 | 10GB | 20GB | 50GB+ |
| 网络 | 10Mbps | 100Mbps | 1Gbps |

### 性能测试

```bash
# API响应时间测试
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:1754/api/health

# 并发测试
ab -n 100 -c 10 http://localhost:1754/api/health
```

## 🔐 安全建议

### 1. 网络安全
- 仅开放必要端口
- 配置防火墙规则
- 使用VPN访问

### 2. 数据安全
- 定期备份数据
- 加密敏感配置
- 监控异常访问

### 3. 系统安全
- 及时更新DSM
- 定期更新Docker镜像
- 监控系统资源

## 📞 技术支持

### 获取帮助

1. **查看日志**: `docker-compose logs`
2. **检查状态**: `docker-compose ps`
3. **重启服务**: `docker-compose restart`
4. **完全重置**: `docker-compose down && docker-compose up -d`

### 联系方式

- 📧 技术支持: <EMAIL>
- 📱 QQ群: 123456789
- 🌐 官网: https://example.com

---

## 🎉 部署完成

恭喜！您已成功在群晖NAS上部署智能选股系统。

**访问地址**: http://你的群晖IP:1750

享受AI驱动的智能选股服务吧！ 🚀📈
