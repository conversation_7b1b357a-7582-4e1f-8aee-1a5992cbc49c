#!/usr/bin/env python3
"""
DeepSeek API配置工具
帮助用户配置DeepSeek API密钥和测试连接
"""

import os
import asyncio
import json
from pathlib import Path
from deepseek_ai_engine import DeepSeekAIEngine

class DeepSeekSetup:
    """DeepSeek配置工具"""
    
    def __init__(self):
        self.config_file = ".env"
        self.api_key = None
    
    def print_banner(self):
        """打印横幅"""
        banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🧠 DeepSeek AI 配置                       ║
    ║                                                              ║
    ║              配置DeepSeek API，获得强大AI分析能力             ║
    ║                                                              ║
    ║  DeepSeek优势:                                               ║
    ║  • 强大的推理能力                                            ║
    ║  • 专业的金融分析                                            ║
    ║  • 实时API调用                                               ║
    ║  • 无本地资源限制                                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
    
    def show_api_guide(self):
        """显示API获取指南"""
        print("\n📋 DeepSeek API获取指南")
        print("=" * 60)
        print("1. 访问DeepSeek官网: https://platform.deepseek.com/")
        print("2. 注册账号并登录")
        print("3. 进入API管理页面")
        print("4. 创建新的API密钥")
        print("5. 复制API密钥")
        print("\n💡 注意事项:")
        print("• 新用户通常有免费额度")
        print("• API密钥请妥善保管")
        print("• 建议设置使用限额")
    
    def get_current_config(self):
        """获取当前配置"""
        try:
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    for line in content.split('\n'):
                        if line.startswith('DEEPSEEK_API_KEY='):
                            return line.split('=', 1)[1].strip()
            
            # 检查环境变量
            return os.getenv('DEEPSEEK_API_KEY')
        except:
            return None
    
    def save_api_key(self, api_key: str):
        """保存API密钥"""
        try:
            # 读取现有配置
            config_lines = []
            if Path(self.config_file).exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config_lines = f.readlines()
            
            # 更新或添加API密钥
            found = False
            for i, line in enumerate(config_lines):
                if line.startswith('DEEPSEEK_API_KEY='):
                    config_lines[i] = f'DEEPSEEK_API_KEY={api_key}\n'
                    found = True
                    break
            
            if not found:
                config_lines.append(f'DEEPSEEK_API_KEY={api_key}\n')
            
            # 写入配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                f.writelines(config_lines)
            
            # 设置环境变量
            os.environ['DEEPSEEK_API_KEY'] = api_key
            self.api_key = api_key
            
            print(f"✅ API密钥已保存到 {self.config_file}")
            return True
            
        except Exception as e:
            print(f"❌ 保存API密钥失败: {e}")
            return False
    
    async def test_api_connection(self, api_key: str = None):
        """测试API连接"""
        print("\n🧪 测试DeepSeek API连接...")
        
        test_api_key = api_key or self.api_key or self.get_current_config()
        
        if not test_api_key:
            print("❌ 未找到API密钥")
            return False
        
        try:
            # 创建AI引擎实例
            ai_engine = DeepSeekAIEngine(api_key=test_api_key)
            
            # 模拟股票数据进行测试
            test_stock_data = {
                'symbol': 'AAPL',
                'name': '苹果公司',
                'price': 150.00,
                'change_percent': 1.5,
                'volume': 50000000,
                'pe_ratio': 25.0,
                'market_cap': 2500000000000
            }
            
            test_technical_data = {
                'rsi': 55.0,
                'ma5': 152.0,
                'ma20': 148.0,
                'ma60': 145.0,
                'volume_ratio': 1.2,
                'price_position': 60.0,
                'trend': 'up'
            }
            
            print("  发送测试请求...", end=" ")
            
            # 进行分析
            result = await ai_engine.analyze_stock(test_stock_data, test_technical_data)
            
            if result and not result.get('error'):
                print("✅ 成功!")
                print(f"  AI引擎: {result.get('ai_engine', 'unknown')}")
                print(f"  分析建议: {result.get('action', 'N/A')}")
                print(f"  信心度: {result.get('confidence', 0)}%")
                
                if result.get('ai_engine') == 'deepseek':
                    print("🎉 DeepSeek API连接正常，AI分析功能已就绪!")
                    return True
                else:
                    print("⚠️  使用了备用分析，请检查API密钥")
                    return False
            else:
                print("❌ 失败")
                print(f"  错误: {result.get('error', '未知错误')}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    def interactive_setup(self):
        """交互式设置"""
        self.print_banner()
        
        # 检查当前配置
        current_key = self.get_current_config()
        if current_key:
            print(f"✅ 发现已配置的API密钥: {current_key[:8]}...{current_key[-4:]}")
            
            choice = input("是否要测试当前配置? (y/N): ").strip().lower()
            if choice == 'y':
                if asyncio.run(self.test_api_connection(current_key)):
                    print("\n🎉 当前配置正常，无需重新设置!")
                    return True
                else:
                    print("\n⚠️  当前配置有问题，请重新设置")
        
        # 显示获取指南
        self.show_api_guide()
        
        # 获取API密钥
        print("\n🔑 请输入您的DeepSeek API密钥:")
        api_key = input("API密钥: ").strip()
        
        if not api_key:
            print("❌ API密钥不能为空")
            return False
        
        # 保存API密钥
        if not self.save_api_key(api_key):
            return False
        
        # 测试连接
        if asyncio.run(self.test_api_connection(api_key)):
            print("\n🎉 DeepSeek AI配置完成!")
            print("现在可以享受强大的AI股票分析功能了!")
            return True
        else:
            print("\n❌ API测试失败，请检查密钥是否正确")
            return False
    
    def quick_test(self):
        """快速测试"""
        print("🧪 快速测试DeepSeek API...")
        
        current_key = self.get_current_config()
        if not current_key:
            print("❌ 未找到API密钥，请先运行配置")
            return False
        
        return asyncio.run(self.test_api_connection(current_key))
    
    def show_usage_examples(self):
        """显示使用示例"""
        print("\n💡 DeepSeek AI使用示例")
        print("=" * 60)
        
        examples = """
# 1. 在Python代码中使用
from deepseek_ai_engine import DeepSeekAIEngine

# 初始化AI引擎
ai_engine = DeepSeekAIEngine()

# 分析股票
analysis = await ai_engine.analyze_stock(stock_data, technical_data)
print(f"投资建议: {analysis['action']}")
print(f"信心度: {analysis['confidence']}%")

# 2. 在智能选股系统中
# DeepSeek AI已集成到主系统中，会自动进行股票分析

# 3. 批量分析
results = await ai_engine.batch_analyze(stocks_list)
        """
        
        print(examples)
    
    def show_pricing_info(self):
        """显示价格信息"""
        print("\n💰 DeepSeek API价格信息")
        print("=" * 60)
        print("• 新用户通常有免费额度")
        print("• 按Token使用量计费")
        print("• 价格相对较低")
        print("• 建议设置使用限额")
        print("\n详细价格请访问: https://platform.deepseek.com/pricing")

def main():
    """主函数"""
    setup = DeepSeekSetup()
    
    print("🧠 DeepSeek AI配置工具")
    print("=" * 40)
    print("1. 交互式配置")
    print("2. 快速测试")
    print("3. 使用示例")
    print("4. 价格信息")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-4): ").strip()
            
            if choice == "0":
                print("👋 退出")
                break
            elif choice == "1":
                setup.interactive_setup()
            elif choice == "2":
                setup.quick_test()
            elif choice == "3":
                setup.show_usage_examples()
            elif choice == "4":
                setup.show_pricing_info()
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    main()
