"""
本地大模型性能对比和切换工具
支持多种开源模型的性能测试和选择
"""

import asyncio
import time
import json
from typing import Dict, List
from ai_engine.local_llm import LocalLLMClient

class ModelComparison:
    """模型对比工具"""
    
    def __init__(self):
        self.models = {
            # 中文优化模型
            "qwen2.5:7b-instruct": "阿里Qwen2.5-7B (推荐)",
            "qwen2.5:14b-instruct": "阿里Qwen2.5-14B (高性能)",
            "qwen2.5:32b-instruct": "阿里Qwen2.5-32B (最强性能)",
            
            # Meta开源模型
            "llama3.1:8b-instruct": "Meta Llama3.1-8B",
            "llama3.1:70b-instruct": "Meta Llama3.1-70B (需大内存)",
            
            # 专业模型
            "deepseek-coder:6.7b-instruct": "DeepSeek代码专家",
            "deepseek-math:7b-instruct": "DeepSeek数学专家",
            
            # 国产模型
            "yi:6b-chat": "零一万物Yi-6B",
            "yi:34b-chat": "零一万物Yi-34B",
            "baichuan2:7b-chat": "百川智能Baichuan2-7B",
            "chatglm3:6b": "清华ChatGLM3-6B",
            
            # 轻量级模型
            "phi3:3.8b": "微软Phi3-3.8B (轻量)",
            "gemma:7b-instruct": "Google Gemma-7B",
        }
        
        self.test_prompt = """
        请分析以下股票数据并给出投资建议：
        
        股票：平安银行(000001)
        当前价格：12.50元
        技术指标：RSI=45, MACD=0.15, 成交量放大1.8倍
        基本面：PE=8.5, ROE=12.5%, 营收增长15%
        
        请提供：
        1. 投资建议（买入/观望/卖出）
        2. 信心等级（1-10分）
        3. 主要理由（3个要点）
        4. 风险提示
        
        请用中文回答，要专业且简洁。
        """
    
    async def test_model_performance(self, model_name: str) -> Dict:
        """测试单个模型性能"""
        print(f"\n🧪 测试模型: {self.models.get(model_name, model_name)}")
        
        try:
            # 初始化客户端
            client = LocalLLMClient(model_name=model_name)
            
            # 测试响应时间
            start_time = time.time()
            response = client.generate(self.test_prompt, temperature=0.3)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            # 评估响应质量
            quality_score = self._evaluate_response_quality(response)
            
            result = {
                "model": model_name,
                "description": self.models.get(model_name, model_name),
                "response_time": round(response_time, 2),
                "response_length": len(response),
                "quality_score": quality_score,
                "response": response[:200] + "..." if len(response) > 200 else response,
                "status": "success"
            }
            
            print(f"✅ 响应时间: {response_time:.2f}秒")
            print(f"✅ 质量评分: {quality_score}/10")
            
            return result
            
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return {
                "model": model_name,
                "description": self.models.get(model_name, model_name),
                "status": "failed",
                "error": str(e)
            }
    
    def _evaluate_response_quality(self, response: str) -> float:
        """评估响应质量"""
        score = 5.0  # 基础分
        
        # 检查是否包含关键要素
        if "买入" in response or "卖出" in response or "观望" in response:
            score += 1.0
        
        if any(str(i) in response for i in range(1, 11)):  # 包含信心等级
            score += 1.0
        
        if "理由" in response or "原因" in response:
            score += 1.0
        
        if "风险" in response:
            score += 1.0
        
        if len(response) > 100:  # 回答详细程度
            score += 1.0
        
        return min(10.0, score)
    
    async def compare_all_models(self) -> List[Dict]:
        """对比所有可用模型"""
        print("🚀 开始模型性能对比测试...")
        print("=" * 60)
        
        results = []
        
        for model_name in self.models.keys():
            result = await self.test_model_performance(model_name)
            results.append(result)
            
            # 避免请求过于频繁
            await asyncio.sleep(1)
        
        # 按性能排序
        successful_results = [r for r in results if r.get("status") == "success"]
        successful_results.sort(key=lambda x: (x["quality_score"], -x["response_time"]), reverse=True)
        
        return results
    
    def print_comparison_report(self, results: List[Dict]):
        """打印对比报告"""
        print("\n" + "=" * 80)
        print("📊 模型性能对比报告")
        print("=" * 80)
        
        successful_results = [r for r in results if r.get("status") == "success"]
        failed_results = [r for r in results if r.get("status") == "failed"]
        
        if successful_results:
            print("\n🏆 可用模型排名 (按质量评分排序):")
            print("-" * 80)
            
            for i, result in enumerate(successful_results, 1):
                print(f"{i}. {result['description']}")
                print(f"   模型: {result['model']}")
                print(f"   质量评分: {result['quality_score']}/10")
                print(f"   响应时间: {result['response_time']}秒")
                print(f"   响应长度: {result['response_length']}字符")
                print()
        
        if failed_results:
            print("❌ 不可用模型:")
            print("-" * 40)
            for result in failed_results:
                print(f"• {result['description']}: {result.get('error', '未知错误')}")
        
        # 推荐建议
        print("\n💡 推荐建议:")
        print("-" * 40)
        
        if successful_results:
            best_model = successful_results[0]
            print(f"🥇 最佳模型: {best_model['description']}")
            print(f"   配置: model_name='{best_model['model']}'")
            
            if len(successful_results) > 1:
                fast_model = min(successful_results, key=lambda x: x['response_time'])
                print(f"⚡ 最快模型: {fast_model['description']} ({fast_model['response_time']}秒)")
        
        print("\n📝 使用方法:")
        print("在 ai_engine/local_llm.py 中修改:")
        print("LocalLLMClient(model_name='your_chosen_model')")
    
    def save_results(self, results: List[Dict], filename: str = "model_comparison_results.json"):
        """保存测试结果"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {filename}")

async def main():
    """主函数"""
    comparator = ModelComparison()
    
    print("🤖 本地大模型性能对比工具")
    print("=" * 50)
    print("将测试多个开源模型在股票分析任务上的表现")
    print("请确保Ollama服务正在运行...")
    
    # 运行对比测试
    results = await comparator.compare_all_models()
    
    # 打印报告
    comparator.print_comparison_report(results)
    
    # 保存结果
    comparator.save_results(results)

if __name__ == "__main__":
    asyncio.run(main())
