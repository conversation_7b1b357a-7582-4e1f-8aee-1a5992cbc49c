#!/bin/bash

# 群晖NAS智能选股系统部署脚本
# 端口配置: 1750-1799

echo "🏠 群晖NAS智能选股系统部署脚本"
echo "=================================="
echo "📡 端口配置:"
echo "  - 1750: Web访问主端口 (Nginx)"
echo "  - 1751: PostgreSQL数据库"
echo "  - 1752: Redis缓存"
echo "  - 1753: Ollama AI服务"
echo "  - 1754: 后端API服务"
echo "  - 1755: 前端React应用"
echo "  - 1756: HTTPS (可选)"
echo ""

# 检查Docker和Docker Compose
check_docker() {
    echo "🔍 检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        echo "❌ Docker未安装，请先在群晖套件中心安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        echo "❌ Docker Compose未安装"
        echo "请在群晖中安装Container Manager套件"
        exit 1
    fi
    
    echo "✅ Docker环境检查通过"
}

# 检查端口占用
check_ports() {
    echo "🔍 检查端口占用情况..."
    
    ports=(1750 1751 1752 1753 1754 1755 1756)
    occupied_ports=()
    
    for port in "${ports[@]}"; do
        if netstat -tuln | grep ":$port " > /dev/null 2>&1; then
            occupied_ports+=($port)
        fi
    done
    
    if [ ${#occupied_ports[@]} -gt 0 ]; then
        echo "⚠️  以下端口已被占用: ${occupied_ports[*]}"
        echo "请在群晖控制面板中检查端口使用情况"
        read -p "是否继续部署? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    else
        echo "✅ 端口检查通过，1750-1756端口可用"
    fi
}

# 获取群晖IP地址
get_synology_ip() {
    echo "🌐 获取群晖IP地址..."
    
    # 尝试多种方法获取IP
    SYNOLOGY_IP=$(hostname -I | awk '{print $1}')
    
    if [ -z "$SYNOLOGY_IP" ]; then
        SYNOLOGY_IP=$(ip route get 1 | awk '{print $NF;exit}')
    fi
    
    if [ -z "$SYNOLOGY_IP" ]; then
        echo "⚠️  无法自动获取IP地址"
        read -p "请手动输入群晖IP地址: " SYNOLOGY_IP
    fi
    
    echo "📍 群晖IP地址: $SYNOLOGY_IP"
    
    # 更新前端配置
    if [ -f "frontend/package.json" ]; then
        sed -i "s/your-synology-ip/$SYNOLOGY_IP/g" docker-compose.yml
        echo "✅ 已更新前端配置"
    fi
}

# 创建必要的目录和文件
setup_directories() {
    echo "📁 创建必要的目录..."
    
    # 创建数据目录
    mkdir -p data/postgres
    mkdir -p data/redis
    mkdir -p data/ollama
    mkdir -p logs
    mkdir -p ssl
    
    # 创建初始化SQL文件
    cat > init.sql << 'EOF'
-- 智能选股系统数据库初始化
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 股票表
CREATE TABLE IF NOT EXISTS stocks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    symbol VARCHAR(20) NOT NULL,
    name VARCHAR(100) NOT NULL,
    market VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 交易信号表
CREATE TABLE IF NOT EXISTS trading_signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stock_symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL, -- BUY, SELL, HOLD
    confidence DECIMAL(5,2) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    reasoning TEXT,
    ai_model VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 模拟交易表
CREATE TABLE IF NOT EXISTS simulated_trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stock_symbol VARCHAR(20) NOT NULL,
    action VARCHAR(10) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(12,2) NOT NULL,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_trading_signals_symbol ON trading_signals(stock_symbol);
CREATE INDEX IF NOT EXISTS idx_trading_signals_created_at ON trading_signals(created_at);
CREATE INDEX IF NOT EXISTS idx_simulated_trades_symbol ON simulated_trades(stock_symbol);
CREATE INDEX IF NOT EXISTS idx_simulated_trades_executed_at ON simulated_trades(executed_at);

-- 插入示例数据
INSERT INTO stocks (symbol, name, market) VALUES 
('000001.SZ', '平安银行', 'SZSE'),
('600519.SS', '贵州茅台', 'SSE'),
('000858.SZ', '五粮液', 'SZSE'),
('002415.SZ', '海康威视', 'SZSE')
ON CONFLICT (symbol) DO NOTHING;

EOF
    
    echo "✅ 目录和文件创建完成"
}

# 构建和启动服务
deploy_services() {
    echo "🚀 开始部署服务..."
    
    # 停止可能存在的旧容器
    echo "🛑 停止旧容器..."
    docker-compose down 2>/dev/null || true
    
    # 构建镜像
    echo "🔨 构建Docker镜像..."
    docker-compose build --no-cache
    
    if [ $? -ne 0 ]; then
        echo "❌ 镜像构建失败"
        exit 1
    fi
    
    # 启动服务
    echo "🚀 启动服务..."
    docker-compose up -d
    
    if [ $? -ne 0 ]; then
        echo "❌ 服务启动失败"
        exit 1
    fi
    
    echo "✅ 服务启动成功"
}

# 等待服务就绪
wait_for_services() {
    echo "⏳ 等待服务启动..."
    
    # 等待数据库
    echo "📊 等待PostgreSQL..."
    for i in {1..30}; do
        if docker-compose exec -T postgres pg_isready -U stock_user > /dev/null 2>&1; then
            echo "✅ PostgreSQL已就绪"
            break
        fi
        sleep 2
        echo -n "."
    done
    
    # 等待Redis
    echo "🔄 等待Redis..."
    for i in {1..30}; do
        if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
            echo "✅ Redis已就绪"
            break
        fi
        sleep 2
        echo -n "."
    done
    
    # 等待后端API
    echo "🔗 等待后端API..."
    for i in {1..60}; do
        if curl -s http://localhost:1754/api/health > /dev/null 2>&1; then
            echo "✅ 后端API已就绪"
            break
        fi
        sleep 3
        echo -n "."
    done
}

# 安装AI模型
install_ai_models() {
    echo "🤖 安装AI模型..."
    
    # 等待Ollama服务
    echo "⏳ 等待Ollama服务..."
    for i in {1..60}; do
        if curl -s http://localhost:1753/api/tags > /dev/null 2>&1; then
            echo "✅ Ollama服务已就绪"
            break
        fi
        sleep 3
        echo -n "."
    done
    
    # 安装推荐模型
    echo "📥 安装Qwen2.5-7B模型 (推荐)..."
    docker-compose exec ollama ollama pull qwen2.5:7b-instruct
    
    if [ $? -eq 0 ]; then
        echo "✅ AI模型安装成功"
    else
        echo "⚠️  AI模型安装失败，可以稍后手动安装"
    fi
}

# 显示部署结果
show_deployment_info() {
    echo ""
    echo "🎉 群晖智能选股系统部署完成!"
    echo "=================================="
    echo ""
    echo "📱 访问地址:"
    echo "  🌐 主要Web界面: http://$SYNOLOGY_IP:1750"
    echo "  📊 API文档: http://$SYNOLOGY_IP:1750/docs"
    echo "  🔗 后端API: http://$SYNOLOGY_IP:1754"
    echo "  ⚛️  前端应用: http://$SYNOLOGY_IP:1755"
    echo ""
    echo "🔧 管理端口:"
    echo "  📊 PostgreSQL: $SYNOLOGY_IP:1751"
    echo "  🔄 Redis: $SYNOLOGY_IP:1752"
    echo "  🤖 Ollama AI: $SYNOLOGY_IP:1753"
    echo ""
    echo "📋 管理命令:"
    echo "  查看日志: docker-compose logs -f"
    echo "  重启服务: docker-compose restart"
    echo "  停止服务: docker-compose down"
    echo "  更新服务: docker-compose pull && docker-compose up -d"
    echo ""
    echo "🤖 AI模型管理:"
    echo "  查看模型: docker-compose exec ollama ollama list"
    echo "  安装模型: docker-compose exec ollama ollama pull <model-name>"
    echo ""
    echo "🔍 故障排除:"
    echo "  检查服务状态: docker-compose ps"
    echo "  查看容器日志: docker-compose logs <service-name>"
    echo ""
    echo "⚠️  注意事项:"
    echo "  - 确保群晖防火墙允许1750-1756端口访问"
    echo "  - 首次启动可能需要几分钟下载AI模型"
    echo "  - 建议配置至少8GB内存给Docker"
    echo ""
}

# 主函数
main() {
    echo "开始群晖NAS部署流程..."
    
    check_docker
    check_ports
    get_synology_ip
    setup_directories
    deploy_services
    wait_for_services
    install_ai_models
    show_deployment_info
    
    echo "🎊 部署完成! 请访问 http://$SYNOLOGY_IP:1750 开始使用"
}

# 执行主函数
main "$@"
