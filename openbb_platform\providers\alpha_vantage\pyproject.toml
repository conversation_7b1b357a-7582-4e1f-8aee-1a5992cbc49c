[tool.poetry]
name = "openbb-alpha-vantage"
version = "1.4.2"
description = "Alpha Vantage extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_alpha_vantage" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.8"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
alpha_vantage = "openbb_alpha_vantage:alpha_vantage_provider"
