interactions:
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
    method: GET
    uri: https://api.polygon.io/vX/reference/financials?apiKey=MOCK_API_KEY&include_sources=True&limit=5&ticker=AAPL&timeframe=annual
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAA/+y9a3PayrY2+ldc/rLOOSvN7PslVbvOAQR+nTMlYgebKb21i2q1WgaZSzaQGLNr
        /ve3xMUIA+ISJyZzs76sGdsafXt69BjdYzzjvy8HdvitMxpefvzf/33Zji4/XkIIIcEQKfIRQ0w+
        Vv3LD5fDkR6MmpEe2cuPlxhiDKACmF1+uLS9KPNzkv6cwMsPl3G70+49rPwKIQDJ5YdLbYz9OtI9
        Y6e/HrW7K3+C6xh/hPIjFsHlh8v0t/FAT/9E93rfdGcqfGh0p/nVDtr9tM/TPs5/+Gz1YC7u8sOl
        aT+ujCkdS9tcfrwkTKCpePNoB+nwL4vFz39e/ueHS9PvftW952Zv1mjx69eOvbjumUL6bf/bwNjm
        fHDfBp3Lj5et0ejr8OMff+iv7cLXfuf5od8rtPt/fEd/DGxsB7Zn7B9Da/6YfTT8Y9kZkM4XhAjy
        NdFxu2Mz8n9QfPpLO/xD668dkE4MVAQ2W6NuYdydTWdP90xbd4aXH//7st0z/a5tDkd6ZLu2N0p/
        ZvrDUbMfNwf2u+19s+mPvutO+h+4gCgiwv4boQ+X33rt0eXHy7svzuWHy44Obdr/cn84uqjFF7fz
        bz9c9geRHVx+JBAuhn358TJqD6wZNQf2a38wuvxwOf6qR63Lj5d//PH//O9O3+gOSBfk//q//+Nf
        qcBafNXvR8NiL/piB9/bxg6/9DvRvy50L7r4/9rRf/wrBkL+6z8v//4w7fuwqXtR046/2t7QDle6
        zxWjakf3hxfFXnRRWXz+MgK+MoJ2bzTQ8wE0292v30bpYOP+oPutoy8/Xl5/AdfTnwIEcdqzgR1a
        PTCtaeci+912+l8XM/7SP6UQs/9GcEv3bucypiN3MjJeOokgOW6eF6KLvSgjeD4LK1Mt6Wyqo3bn
        28hGTf3dDvSDbQ5berAy3ajAJMKMipURzf9sOShnJueiOJNz8WXxB/MhUXYkdBq2/dAa2Wgu2PvW
        De2gFs/bmzVT+zYajnQvavceVgaJkJiNsmdHzfkm6fSHw6b+rtsdHXZsc9Rvmn632+81h6O+eWz1
        O5EdDJuhnuqcl1lQBa5U3qJ6dpRqnH7X/vFnfzi8KC5auKj3L8rTFi6+ZFr4cFGaNrHcWuJIZEKS
        jvDrVLUMbDQbRzNqf29HthfNtlF/1LKDpo6Sb8NRiojsAm8b0eeFxFm/L5yFxOnOqqUSL4oZicuR
        qGP32HQk/a92oEepRt2w+VmByldQXO11bfH1hr2P4JEYfBG6kLmCMgVnIHsYpND6OujH7dHKBuIK
        UZmjr67SDy8+zz586a08srNTaTNhqzsezbq5nN/MjljpL6IEopz+Luc4g/jMNKMfneaZ2FTq6kST
        2QiyOzke9LtN0++N2r1v6Zjmo+v3hs3Qxv2BbY70eHV0RBCeM7rsLq4O+t2L8ov0i9qL9IvSVPpF
        XY8zIz9WyS0HnLa4bHDZ3qy52d/V9dgOK+PRQPcHUbunB8/XI9sdev1eOhGDfqczncORHdjhKgaU
        ms3gVL01rR70UgMktcpmij8zUbyAeHaGLv64WFP6UxV2UZmLufhsBzO9n1H7+MgZWQj9bAdTkdOm
        VpU7nFsLX/Vg1DbtrzNQD635NmiP2nbYjNrD0aAdzs63XtT81sv+5GX0Uxy9VvhbtWK2sYsvL419
        uHCWsqca8i7b2sskTXG1rvzlkSpTiNmGmK31QmM2X/Z4ZkSkoAix/4ZqK+5nQhaK88PFy35cdhSL
        oxE+k77JFhGIrBoju4BJdgFzYY3kQvNYY/Y1NOeNvbI80IquGunxy+KEtmfXjghBUc6pNtv3qbJZ
        rM4fpbmU5dIcu9VelMpc9Fzyq82Gs5ojz1oUlGKC8q3FmeLYaivSt7UV14zEDcoEze3hubs0XNk4
        kmDJco6M28VHWVPjSAN+KmlxCgy0GTXao1b523DU79pBZWw639IRFIdDOxzaqK7HK+PgcwXf6/d2
        nfOAFTiz/4Zym0WbEbH5oFdHDjIreSZ4k1ZQfIvtPpop1YX5/lUPVh2ww231jMTUXP88k7jUzsce
        654dbTFlEGQH2TI6HtnBK1Nm5zj3tGSKqexXhgw91hmhUyUx0yDD5syRP8qJn2uh4cXMm9cbvXl8
        ZDfxzJvfBa3eikXVXByze5gJuyC2aqtdLE7HDObIkUOTKh3Z0M66/GB7dqA7U/NHR912LzVJ9Kj9
        3W5eFaoIzkHUl5nYDxdXM7kfputSXBG80fFCx22geXvz1oq9aLWpjRcbm13+H9APmUU5Fm9QXP79
        9+yecmBbtjdMF2DWu9ld3frPN/Sas7y1KWeFbPHMDup++Xp1V2/t5Rvum62DOHwHHbZWmcGqwwa7
        6fw5cqV2n0RH379mWpw16NlRLX5tPiA63z2zC6MfmARACpTkWBez+6PjZ4Ife8eQtrthOtJGF1Py
        uT9IT8ZsF+r9WQdezRbda7YOwcaOeckYycfCe66K9LDVjDv9p9VXg1RrLn81NUjmTw29h6Y2o/b3
        mWu9tFKya44KUFKZd9WVqtayHrYuqp3+08woqS7kXxRf5H/ImCrbbsR2Drm6qr5WhrZypSh4jlu8
        0uOtl1z7dwaptc7M5rnd+26Ho9V5XvFCBGT79nM2s9cLiZmZ3Qagg4zatJ3Pg/73dmSj0vPd0EbX
        vZfGlm2tbBaCMvb8HkPfDLE3moXN+GLH4otsGdemrfNT9styDMfez2xZ1ZfGtq0qhZtWdePaHbLR
        Ni8QOvBMX246tmWFlk7yxhVCBQRZeo4dskDL6/FNC3TszcCWBXppbMsCYSW3brtNg9+8dG82D5vX
        9dhlTb246Z1UZxqaMGxZOz3F5ufxt0F6ZDc7bR22O6+XVhYwV7kvSbOTeCbk4s+MkOVmo4f0u/Ti
        DkxFg7lokBX994dL+1/f2qPnlQtPjCjP6Wll9sWx3ntpxU35cNnp9x6aIzvoNiMbrl5TQoZgHgr+
        7PceQPrphZN+ujy5j/T/Unl1O+im0lYvh+SK6ZU6HLmrTQsQ4rw5nK221+8Bk7fiEv/Iinsv3Xy9
        6Jt7jQsK0txIjY2dPNY2zghbtXPV/PJ3Bs3dxv+eiN1p5KNjR5J9aZ+1taoU0fzUWoBGD4d2tKr4
        KWE875lwoRiKs0+36LL9uzyTM5e6Ov08f/qP97a3LMNO1xodZikttwESr8A+C/J5rfFIgWEmcxXN
        UsQs0Oe1Cjz2bjojuNiLduIIv1y3b4USLkAFUd4mXqqFNTQd6/DP5CwFrwJKzOG/1tfdE/+6g8ea
        MzM5r7o1n824PbbR+kTSAhG5gVXV9Lv1KcTH9fDzILWPRs+fO7o3KvaidP2n4VSefb0/+RoKNutz
        VKAM4bxb7wwUNp8/Pw7rbaBYhLGkzlhv1B+smiCEoNy348U3Sz14ZAjbi6i1aWZyy5G/QXtzRiDO
        C2FZO+7XYMPe6KSfC86cNtvBQWCeM5hvi/44MjYePquGVs6UE4FJ3iPpqj29dmz+0HSX1+ZaG9P/
        1hsNm1/1c3qurVonHOW9thfn3158nn+7nOQj7diFxLnAjRMtphP9999/f9gY1o03h3WjaVg3Xwvr
        noV7001h3RggCLD8sUhtfPqR2ngWSi13RWr/qPxXodoYKky3hmpvewL68Qt3VEBY/MDV8m9w5d6O
        yP1z6PS//4lvv/vE/RYlle/BlUpq3fuu17jv+knU8ibXNHBunmvOfdvrXkO/4Y+D+gN1ncqT2/iU
        /Em8jj/52gmS+07QvSZ+3TzXrm6gmzwgPwna3lW16zbuaM0pjl2n2vaRpG5bdU232qslFeQ594+1
        hteuNW7GrnPzFHQ/tdy6P3aTG1JrBJ3AuZsE03ZuW3533KklFewnncSdfGrVnGrXrd9Mak6Fujjo
        +MkDDRIz9iZFEtQf2eIb061+C/Add+tBu9ZwJ263Qt2GP/Ycl7r1h0nQvW/XnFbXm1SI60SdWr0T
        u3V/5H6B4z/rlZGXGOQ6d01OFSSKMUBCxQENIQHSxhQoyYgULA41p4uQ+92PF7IgGclTnOcnyBN5
        gjx+pd75CfK8w3N3uFnb4USI0AgDgVaCA0oRA4pwCGJDCEIIEhGpQx8pd6LnVz1Sbspr2iNhhRPM
        JFK/XcLKe4Gf1L4swO8Tr+5CL3nEbuNm4k9uxl43aNccrxvUS+2gXiRuNwWwTzPgn3hX9x23foPd
        SefRm9w8eU6p7de9x6B+R4Irr+te3dCac43Xwe/CoHE39hx/UmvcEM8pQvfqU8vFtx2vHj362H8K
        nAfqJkHsTopr4KeYRsSgCFCsKKA4YkAjS4ESEYxNioFFAtCeYbiKnG4Q7hkdeehIKmvoiBCNSAwR
        0BEkgKKYgNBYCSzWmkLEsWVmmW+4LVcSE0Zz74HfJVfyDIY8MNTXVYVAGtMwtgCGIQTUQg0kRhpY
        qwURBluF2GFJCOjEkxDOEMnVF+vOUsSpijgKgQ2NARRzAkIlFTAxxwYbhpSZJ3X8c5Itd6ZzcIyY
        4uS3Suc4Iz8X+Y9ryDcahZZRBrghBtCYIqAYJ0DHEsUWa0ZDsnfyqMp/sX6f5NEzJHLPy4c1SOhQ
        wlBSBARUHFAsJZARhsDGxiCJLDckfg2JjfnaiNC8N7tfnq99RkIuEtaPxZBwjGh6DsKYAko0Akop
        AxSKGcKMKqhVTu6cogTnPWqdSu7cGRc5uPDmmHC/QOxN7p68myanIbUMKyB0GAHKrAChUVHqXRGh
        o9gQrM/52bP87H3y4QTDOO/59BTy4TakuigJ8+5M3j3V8rytc23B6zV1jy2xVAoJEGMU0BCGQEFp
        gKARs9BygWz8BqmrO3Dzbqmrb8AUdOiW+MVMQT/InqLyI4D/uewpZ02Sq0nWr9wUVCGLJAGxpCGg
        ghAgKdVAKE6iUISxMHC/FP/fNw97L2YWXFA7ouveh5nlvRA/dp3H70HvUyuY9KE7eaT+5ObZqxvi
        T66h16i2/STouFfXxOtWxn73DnuN63FQVtBvfBoG9f7Eda6fgnqFule3Xbd+AwPHpzXnU8ttVFu1
        erXlN9L+u9j9Mv/mr0+tsBt1aon75Caljlt/eAqu/LHn3D56yQN103YaFZZ+HzR85E/88V+T9bNT
        WcGgiAWIGbOAGhkDTQkDnGkpQk0o02bXe1Qzmt8ErjzIqjymju0PUx8unIW0JVCOvY5biDrdpyrm
        LlRl1x8H3WsYNG7btXqQ+JPbjjfxWm79U6dWv28H9UfqOtdjN7l9fFGV3Wvk1otPXj1Iao0b6Ce3
        7dqV13a7XstLrp+D7jWt1e87gVNtvVaVfnI3dp0i8rouC5zbtnd1TX18/ezjCnaTT10/eWBe4/7R
        bVTjWnnNlxLEMsUhBpIjDaiOKZBGMSAFhEghJczipWov/kmOWd7r/QnwT57P0txLmJs1zSKl0XEs
        McAIcUCp4EBRyAAnMYylldIIdgi1GztharczOHINrbs1cMRYQIMhBLEmEFAiFNDaaCCIwIZKLGQ0
        j/I7lhaGQUV/Z1qYM6Ry9c06pELGpY1jCnhkJaASaaCY5CAWoaCR1CGxbDfPzmGe8Bvx7OxHfkYK
        hJ4s+dkZrrlwXX/AtMRoi0MOuLQcUBwjoCWzQMSCKo64Foun+x3U4rtuYX8itfhW+mABhczLZfoV
        9MFnROYicj34DAkKI4wJYJRIQIVEIAyZApDGYSQiyhi203yfQ6h4DiFswBixPBLndyNsOI6H442G
        8048HGeHOMch3hR+EKkwMiLkgCgDAY20AVJbDLgQmKM4ZBqZg+iEsmf/LKhzh2FyulxKZzDlgSlZ
        B5NFBhmT2gSEKEBFLIAOWQxoyERkNCJUoL1ZjAAqQJVL4faLiYyO79vPZDPbTX2FoMglgD1t6qvz
        JszbhJP1TSiNiRWOMMDUWEBNLIA0oQGMcgytETwS4VaNfhAH4RsB66dzEB7Kffdm59bbs99tIuHa
        xXbAiMp1a34928G7JR96SyeH+qnTMak+ekknCeo3T273vlVzHp/8xqeO2715ch2vVXOun5dOTirT
        J25SffS7/rM7uUG1xjUL6g9P/uQBepMb4nfvnoN6NXm9p9P+B4l5dhu37SC5nnjOHfOTTqfmlJKa
        kzo9UTuof+q4DW/jxSOMeWSEZsBKhQGlygLNQgJioiFFMYRGyyx10UpgKOS52epvTGq2D2mIYJjk
        xTa8K2nIJsYNRmHuZcUvoqg675ucfVNbD52kMdEkYiGIQwQBjS0GEkkLpAqhiESMRSj+WYxf+xEy
        KslPkZAxn9wLiVzWm1/L7XXeiXkn2IZ3jhiGWlscAUogBhQRCCRFCERYWhVrjUM8t0r3Iq0TLA8L
        p0Bad0ZIDkI8Zz2KjZMQSh5RYGXqtyAtQBiaGAgpBTYQEhzOX8JyKacoyqWuewfKqTMScq3dDUig
        0HBuNIgR54CqFAkWIqC0sjIkyEBkdxAYYoTyTrh3ITA84yD3zHDXvR5GYgylBSZkAlASYxByakAU
        x1LTUEVYmf0JISXKrbX5XoSQZ1TkoWIDFwnXsRUojkHErAQUoxhIbQkQmEdcklAym0OwSQuK5pHj
        /3yCzfOC5xkG7TUnLmKaaCNDQCARgDKCgQ4lBJbFiAtsOackh2U331L8aSy751XOVfbr2a+QMGi5
        YMBYaQCVmoPQyAhoyyODoWWImsPI0fe8+XoncvQzQHL1/nroUSRYHGmkACUkApSGGGimOYDYYGal
        MjEK92XQhZTlPVz8WgbdzRYLKUAM96Vm/1klCc4gzQXpejQSZJFSYRQDGEsEKCISaCgJEAxzgljM
        rbK57MNoM/swnLIMizX24RkrMdvEPoym7MPqx9iH0emzD6MZOzD7WezDC/mv2IcRVJhtZR8+llBR
        cqr+WYSKXjfo3idu9wb5kzvkTm6YmxRpbbqnH6Dr3MCg7iX+5G4y1xNJ0A06/uTTo9dwkVd3Ya1+
        /VRzOh23bqjnFKF3dTfxsYvcrJ7opvs76vj16+egHrXdyW0rqD+wmtPpBvWoVWu4z7WrCnQTHy71
        xANxG7dJre4SN9Uv9ZunwLmhXuO2EySdllePkqAePPp19+m1nqg5ZhxcucydmInbuEGeU3r0nAcU
        XN13a40K8rDX8bq3LXdSWnFi3GITKkWiiGuATAQBZZEGOkapitBK8TDU0aIE+GG5a4AWhKC5rs3v
        k732U1HDvOfF6fJAPOe+605unvz0pEluYK1RbXuJz/z6fdebtDqu8/DsNVySQc2TP/G6tcbNU1C/
        T2r1G1qrP9Dpitc/tX1cYX79U+Li6w2oKSUu9lnglDpew2WekyL0gfj4DrndT4lf77Tcrjvxk1I2
        e+2pqVUcmdjGgEquASWxAVJCAbA1VCsdRtHC592HAYNxnncTcgoEGL9vInIexSXCSubN/MlRXJ6V
        d5bi0i02uYqtFQICSOMI0NgaoLjkQFEVqSjGMcRrBG4b9iAtEClzn2BPiK3rDILsfYVbbFrBELGC
        Aah46o5GCIRcCkBEbK0OqY75nnwLG3h+KJe/Kc3PGSdZmh+32CQ0ZjjGFjAeakAFiYCEFAIdCqlj
        FLM42mnpvapbhPOUxunyZp+hkeXNdotNbEOmYm6BpDgEVFGWHiEGWC6J1EQTacK908xZgYvfM838
        jItstGd6tEilISIxiGyUWvuIAB0xC7jmoaaEcrt0DvcivOE0r4r3CRLe/FRIjN363Zzw5ms7aPjQ
        u/LagXP95Nd9WKuXOrV6p+t2r8ducsf8pDIOrirPGcKbZ9f51K45UTu4qnZq9SJ063dPtfodDa58
        6DrVxG1U27V60PafXxPeXD+53Wvq4U+PfuOOuc7N2KsXmVdvPQbJzZPbcMd+45p53U9JlvDGLTZj
        pKKYYgYiYiighHOgGYLAREpTxTQUi7KluzmmBVwrXfUbc0yftUeWYzrVHtSETEoLCDMEUAkJUNoy
        wDUSEtkQKTyPd92aA82wJHlVGN45B/q85NkcaLfYZMQoyG0ElA4FoEoTEBLKALYhIaGCFsbkgHIL
        rMB/43ILZ3Rkyy24xaZmWiLBYqCIkoCGGoIQWwY0tTGMLOQayi2e6uF0nAd6rr+YjTOPPuWgnr8Z
        e8qZrnl3kQMoVW6K4ckVOTiroCzLQGq+mml5gxCgiClADRdAGy2BCGNtaGQF4ebHOLOQEnnkR78z
        Z9YZTdlckvRAIyzWIRGAcwUBjeMISAtTNFkrYcyJRjKnRgJnEuXVTPkNaiScIZGpkfDUlFiFPCIG
        RDqCgOI4BNqEBGiMIyStJZLGBzB6IoXy8r5Pm9HzDI0so2d69sQUchRLIImNAWXKgDDGBsQcxkZx
        bCKp/mmFxnbwru14+/+JtGs/RocPFYZ5mvt/JB3+ecNn08fcYpNThA2kBMRxKACFwgBNtAHYhpGm
        SELMxBvU1Mj3F9+vpMZuOk5cYPJ3ZOM8Iz3LxukWm0LzKKIIghAxDWikIJCExyAWVMaxjWMm8cG0
        hwdQ5KACZbn15t6NIudHeY1UgRC2i033dGiNdhI6QgpJXl2435bQ8Rwj+uqqhXBloxDFgECuAA1D
        AiQmFMAYQRFjJkIhtzJ/HUJ5+kaQ+kWUp7sY8t5qw58eQd55h2QJ8lL7MKYxQjgCEjILKMEcaC00
        ECRSNKLGRvB4ttO3OhJPj+30jKMs26lbbBoaWixDDGgoIKCRtEBLjUHEqEWMSCoQ24CjVV58mZeH
        /iv4QzfbP4d07CeQrv49yx4b2JbtDdvf7dyRSXs4z+vc8Nv1Jz2GqdpNcZUVtdnfOcwjK1+vPvFt
        7esbJjxsHcThqQ8HrttysOqwwW4Ie96xXvsPci0C+ujUiUyLswY9O6rFJ3JRXysvXNa7p9rV3bOX
        uNhLrkng3LZqTtByu/4kcK5hzblv+ZNq169XMkoz6gb1+7aPg8Sd3Hdqzg32cIV52GVB3XsMGrdd
        r3FN3PrSzX1JkMVBx2t8SoKuO/Ead9RzfOLXH4k/6XRcfNsJrnzkJ2bsJbexWzdZpSlhqKEKGeAk
        igFlRIDQYAmQthJpGYU6Xk3gPh5PrMBVzjXHjt2/E1PHJllP290ArLTRBbg+9wejdr+X7UK9P+vA
        GXf74m4lCEapSFABMYgJh4CGkgMVxhIYGuI4opSGSi9qlrzB0bLHoXLgIX6dvQQ4hgEYM5qfd3ZK
        DMA/19h8YfVO7h+9+h32nFbbnZQeXedm7Ce3rSBxked4HRffPdWca+onlcxVn2Fu477jXvmwVn+A
        fnKDAqfT8urFpxTDtYb/7DZ86HUrkzX8Th7HQePuKUhuWM25bXvO/WOtUUGuUyT+5JEFyfU4SG6w
        X/dex4RHIdXGagg0JSrVmyFQUaSARaGNQmYJkdEcvzmUFzQ/rvMEKGzPC1/LvmwjFUJGQw0MwghQ
        GYZAcmlBHBmDMGScQbWJ72SzEhAFKFDeq8UJkL5uU18cU/KbUbedsfyK9UBhibVmMaCUcUAJEkAT
        A4EwDHJqLQkF2ZO9B0vM8lTZr2Xv2UJBSQqK5kWSnBoD5RmwWQbKFLDMYG6iGFgTUkCxFkCHYQgs
        ZNE00swwsoVynxcIzLMQ35hxfwdtNkc8b7ecDG/2GYGvYh05MlTYEAEukAI01BooZSIQa2KIkhrb
        mG0nuuSzl/6T5Lk8L3WG5/KpiaQ1koYIiBhSQCEyQAoaAhpCizmxmBqxxo2+ssmlUCivxMv7UsWd
        lztLFecWm1RGYchjAYQyEtBYQSBlbEBkEUbE8AhL+s+qS7EPqT+CcE8MnyKp/xnkWVJ/t9gUSsFQ
        Sggw1BRQxjTQAjFgrOYQKypirHfy+bMCFTzvbf60+PzPIHgVkCkQZ0phASSMGaAwlkBKjgBF0ijJ
        QkS12E7evEMjvAd583mFX92uoxAKZiwGTEYYUMYtkJZTgGMVRgwKqSKadex3VAbjAu/27X9xZbB9
        Caf38/tOj2/6jOks33TqeWkEJadTukUOKGISSKUsoKGNoTVxDDnKZfGFG1l8kZqy9ao1Ft8Zuy/f
        xOILAYKAwB9j8YWnz+ILQfovxX8Wi+9C/isWXwgV5ltZfI8MRNmtKWgB239D8T/xefq+600exn7j
        GtWu7mCQ3HZ9XG3VHPMcXN22A8d9Cq4q0L26Gy95ww11G9W2V291vCsfBl2v5dcfiNu9ffS7NxP3
        6rblOaWOe595nk6Clte4gUHDffInra43ucF+w6de49OjO3GZV688B0kr8evVbpY3PKiX2kH9AbpX
        1UfPuWE+vmFuN0gCxyVuo5q43Tvs1f11ZdP91HGvPnVqzs2T37iBntNJAufxOeh6j+7EjP2G+xzU
        feQlaweoJRhaaSKgOSaAGhmBMAoZEEghGQsmJaV7hUWsmM2C5oa0nlz804YQjh1D+A1Dgs7YfxUS
        FENitVAxINoaQI1VQBqBAI4kNUJCoRYVns6RdIdu8Z8fh/JTEotoAUu1b/zpO+YVrUZ+Q0r2jvz+
        SdG8B2ZxyALk4sAa5++YxLEjzemtRnN6WU4/9cwYu05xzofYp+7k8dmvm7F3VaF+eg5Mbsa1RtBx
        u6nzFCRe/Y6k54LbfuFDxG7SagUN/6nmGOLVr2HQ8Mc1x2u5V3cscO6YXw86LvbH3hofov9cu6p2
        gqRIvYlBbnLbCZIKdXHlKah3Hr16p+V17yZBUiF/TSr0zxfWs/TQoCJisQVkWg0IMQhCITFAEYUY
        RpSr2G5N4tiVDCQLXObVjPptc4HOOPpr4q7gCNsI6siGQMVRCChFIdBYRQBZqqCNFIaaH4SjLSfa
        G0Hq5+eTbsv0PeRw+wkZIUclY72RGXF6uVjnffzX5HplH5vYUsg1ASyWGtCIY6A1iYFFRkMphMUz
        B3pT5PT+V7uMkN/xbvenwiVzt3v9PCUvaNxN3KvKk5vcjAPnhrrO7aOHbzt+t9oJroJHP3lcZgOk
        MEoq0K/fdwPnLv3bxMP3bRffPdfqZuw2ql3PuXt26z5+7XIGV9fYrd+2vEbw6NUfnrwrlwSN60lQ
        v2bepDL2Gz706tdjLwle3+2SUHGJNU99TA0ojTkIrYAA45DwGCPJbbz/ewUlFO6uJPiL3yty3lNp
        AWPFf5v31DN6X72nasMFMoIBrZUBlBIDQgEtgBZSgxXWEYNboxL3UmFvFpa4ufoy5OhUo9LOYFuJ
        ShMMKkqiCESMMkA5iYCOtAZMcySVZlAavBaVti24BxMp8xgjTjy45wyNV8E9DCJDrQiBYcQCygUE
        MrQUUAm15FayUPKdOUmUCJRX4PYEcpLOC7+Sk8SjMFQRooAaQgGNsQAyUhoYSgU3MTFc09Vl35bT
        AxlRebE9J5WSeEbB65REDokyNgKRQSGgIiZAMwOBYihEEKfnhtg/N20aN5NnlL5XbtqGmLRdB9l7
        xKSd4fnqSV2gWEUhs0ByKQGNKAIy1gTAmCMicGiiRXx1vncnIRLsd8idOSPgVe4MkYJYrjBANsSA
        YoaBjEIDuAm5NJZKKvQaAradVYwglsc9e4L5p2dAvMo/jRGnAgsOwijGgCJogNJMAoKIUgQyZOE/
        LOVia2IqFzzvsD21zNQzlF9npuIQUxgbgNRUt1kOZEwQIFSaCFscIhPvmUpNFGd5VvivTaXelt3G
        JMstAvO+2W1nfL7KbiMaxsIqCaxEBNBU30qqDEAwoigKFRXIzN9j5qFNq+FDu1msZQGS35HF+ufG
        /72wWCedVtC9gYHjQ9dxn7zkNnG7FeY5xbHbuKZBUk3/jYPEoEz8X+LWS0mtcYOD5GGSLr17dd/y
        kxvqTYpjL7nBtfrtY61RWYOK13XH7pXLAifo1hx37F1VnoK0z84DcZ0K9hyfeVc3zK8Hr1msDYMG
        cWVBzIQAFIUEaAEpiE1sBYMshDY8oEKyKvD8nOdTrY98Bsar+sjQSBUyoQBGIQXUxgJIyyUINWWY
        IW4Imb9znKuo7VXYkRSw/H0LO573x6vCjjRSEjONQWy5ADRkCGiGFdAWkzgiMbLh/mWPUEEKlheh
        dNplj87geFX2iEaGWZb6uhIrQI2KgNZIAEwF1pwRqLXeWPXzVXg5Qu9QFXOhzXLKWAuGJX5Vp2ur
        MttayPrYqj9bClnP21urZ33G8J4YXr2vYTKEVGoK5LSWATMC6DgWAIYCKRwqFqN4kRkyHDX7cXNe
        8G+VOU4xluc5lvvD0UUtvphX63uLFKHhqBZf9fvR9LnZDr63jR1+6XfOB93eumyVQIBoCrW1QApB
        AY1QBBSyEESxsJZyFJJFwMus+v0OI4jsrG49K3+fawId6yK8NoEOKHV/xsWrF0gSMQFDGoKIEwmo
        iihQWISACxpGjCsuFuwix5WVRQWlUN416e9cVvaMplfPRRGWMGaRBTpCCFAOQyClksBGiGgTxiHS
        cEud9H3yWA8xpHZnrx5ruHh2dGg16zNQ3OR65V7CEiVEbIE2cQRorDWQhGEAQ6uR0ARDSfYDyvHv
        SbsAs/NhiZAjLXSp9qgwCgXF9H1KjK7N+Xfd7iwm3PS73X5vVt91HgK4du9z+GZdtJBOfHnawkU2
        yHD9rkcc6x2R/GvQZjSvN5vNgsEFxHIuyrdfiH64cBbillejx6bALESd7hXpb5L3slKDKJScQmtC
        oMW08jVHQGMmQcigFXEkLMLmh2ud7twQ71bs9EfKCfOCgOoNRvXPqiZ8PuxfJVvQKNYxjgnQFjFA
        KWJAwVgArAiObYxCHs4P+4dBCsOvg/7qexQqQKpyucqv0g8vPs8+/OGAoam0mbDzku/rCKy8O2kF
        FSKIAzklreRIgTBWFiCMIwytxjCOFkp1trdezuGXh+FXzLwk90VyJmRx/H5YkhJkzt1jw50X0k/H
        Nxy7yc38nP2a+MmnR29SgTWnk/hJBbr1247fvXtyk+upPPfKJ+6k1Qq+vJyzxJ14j0H9Yewmj0+1
        +jX0rryWn9yNXewid+J1POcOeVfXY3/tnDUTF992ak7luVYvMje5gT5O+1yZ+JO7iYvvoFe/g35S
        Tf6arJj8OIoNNywCVsUSUBxFQIURBJahkBvDWBi/PLVMbxFXzWFBGcqLIb1dfPTDIcNzSYvTY6DN
        qNEetcrfhqN+1w4qY9P5FrV7D8Xh0A6HNjoR2qLfQUt42SQIbWMYhUSC2EoLqI0hUJwggEMZQ4Nh
        RPE8+P3rYG75zryOZtT+3o5sL5p5T7PoLB0l34ajru2thGZtfYBeSJx5GRfOQuLUoZqFaxUzEpd+
        hzrWtZr6HXMnY9icOX9HOX5zc394MfMA9UYP8NjHIzzzAJeRQxu6SAqS51buXzLCbLq4O5ZVbyF0
        IfO86fY9mleD+oWgJo4jgAnEILWrQMgFBgZybg3DIRQLnrwd4WO8wLHcDwebabOO1dC11dix8z3c
        IWBYcX5VGMbYagx4BCGgNgyB5lEIopiKUFpmLImyz0J5r8mEYYRU/mvy7FVo61vysTcjW96S1x6R
        zy9FB3lxKzGGOiKYWIuBwZwCimMDNItjwJFFRJDYaMLzyHyR2kzmKwFUM2beFTLfOcmv3EDmi9SU
        zBf9EJkvUidP5ptOAYRo2tOfQeb7In9B5osgeEx/pbBM/28rn++mcOMdFgOlPC8y6pdbDBESDENO
        mLKIUQKeHkFVmxGgTlFQiXFZOoxLRQgSRVXGSFU4Ebxa2h3vw2ZV1X51vM9e8WkcI3Ha8WnZdaGY
        vayLqJQ5U2VWVVVJJCk5ZYan6+JUpao4pb1jFxAqKHHCwQuZCcBQLoHplCGtsHIVOSXqVFhZQYyq
        FUoUFQhXK8V9TTaicvmM38Viyy465/hlzExUsaw6VSoEkhQhBUuV2WZktFRV80XfK24XoYJUJx64
        m116RJfTUMakWnQ4VkVRkaIEeYmq2dIzyFGR7Irs3/ikRQr0d3rRms4NooIwiyBHy23BhKOKnFYq
        rIyJEBIX4Vwv0KoQxZfk1F07AxUkFHlEd++aDJPdIQIvh18psaJTURwSJ/1fRTLG58cVo6yMjg62
        OOwU+/XBFtkJUXC5V2ixymSFciyqVVxUgkpJ5hMiSohVcm4YOUQi797lVG4YM0OXmQNClHkFFpUo
        liilRYerqsDVCqlWEWPQqbJVZbnVkaMFTqVCq7ftv0NUcFZ7EqFe5gWXCMJVplSZoXIRM6dC5xYd
        RIwrvkf0KxIyl2PlXaJfszsASfoyXlQql1mV8opiqkgrgiJOpqcFKclyVVbf4C17h3J4t7fsf849
        cW5sECooSkkek8JpxwYderacTGzQqydpuTv04d2SJLPaQRKx1A4VWHGIErhUphRTwZySWPi3Fbio
        hH9srK/ENI/h+ORjfVe8z4xOdaq8isuwDEuVKqNVXsJUzc8QiUvV8kE6dUsoDRMkzzf/54bSrNi2
        Er5MeqlcEVRURFmWaYkoLlkVzSe9AiVzDogfIAUm+AnGD0yHjoVg0CJMl2Y9lqyqUIULIp0SpbxU
        LLHZ0AlksuL800Njz8nIWyKhVEHO+P62DeZXBEJlNyymSy1ZJpwVq0iqoixhVCrzUpUtzpZqEeO9
        HpLo1NwmNN//OLF3pBXfAy9VWJEXKxQKKQkrVyhWsASr8wusKkfVuTe6Oyhgt8X384MCNtG+78FY
        SyRDeZF7p8BYO1s+xQS1GAmUcaWcquOwSqmCq+USL5Eix/MTqCQJfjn2NxE0I5h/4PxsgubMmCRa
        3qNDjCplzFnZqRIChRKU8sV9GUa0vA+dn2Bc7cnj9gvo/DIDRdnF46JEKiWqSEkoqpwSqfLyXBtR
        zHlpa7nkXYj9adSU2ZHgzJohBznU4dChgnMlJRRocYNRLhbLZF+mWIHyGDd/PVNsdryMy+VNFpOq
        SFWZFFmRCYpgldP5ypWpLJa2ELOr1CXLC0d5Y2L2nB3CsUS5+dK/iJE5M8OKLC9JHUwErYhKyUGy
        XHQIKjsvt8bFEpsf1FuJ7wQRedP8LsR3WSxBtbwVLTNMBCmKouAlhxAuKpXy4vZPKClyuQrX7sX3
        hNg7lS9ZOcawyLxps6oSiDmQl6vFYkkIRuc6H0JaZnsTeFJM0Anyd2bXXnCR8dpRkVZZpVihVQqr
        UJbE3GvnkimI92dWZgVFc2tPvRuzcl6JEo7JiZUoya4UEcuVkqhKEC4SxMsOL9NKEakFQpEUcL0+
        wsrDDZUQ71kU4WfxGmYHJiFfGiElyli5VKzCKoXUwZAUxSJsAVUr5f0r5BBJco+Ud6mQs5MYk8n8
        0gS/lhjzn0FJe1DF2B31PnlBkdwMvtOu95mNCuBieeqTagmVYFEozlB63BVLsjTfdqpKiNpQjnBV
        o5Cd2cu/pAztAdURVQEqsetZ5XTLIx425+9ZHXFz/2mBydzaC6dSb3mvso9vNZp3KvuYUQsYZ14U
        VEmUCOZCiFIJFsu0ykpzRxpBLCE8utrtW22+dyp3m50vKTOOeJUiJSpSVEsOFhQiihdqlOGKYFvn
        a//y2W91AL19+ewt9ePTvu9fRH9DZJWEue98+xfPX4uxOjraJNPirEHPjmrxpqgjwTG1iKNleA2i
        lRJxuAOlcHC1VIHF6vyKG9Gy5M4ivGbfGTveGNt/5nbaZwfiZlnQX2Ws4uNBggtC5pX3m1vNRyPl
        WN9n2u4GuKSNLiDzuT8Ytfu9bBfq/VkHtqCJscxVJ6VSVIqMVSqyhMrQKVYWGjrVOJUdaHqTjbbN
        lj0ABnQPGBzS1x0Lvu2YPaDHYpq485/T9JzRt2Ha5v9/+eFyYP/rmx2OmtN0HgWFZlRbZnUIwyhS
        xnKpQ2QNpiGO6WV6FoxHOxJT/sokpizTSv5f820w7A/+w/+rqFyn+OQ67rPrVJj7fDe5qV8/ufXH
        jgurV40vQctMYFc3WBL+r/vH4F5NQnL/7OP7yee/bp+jxl03vOqM9F83yvsSPAV/ffoa4ts4xEFs
        uvdPIfkE/8K3rejq7lv4v247n2eyR67zOHKTh67B6jlyUN533eiqk2h8//z5rlq6qzyl/x4FjeDZ
        b6DO50b1W9i7b4XFy7//TwAAAP//UECe1fpqAQA=
    headers:
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Sun, 10 Dec 2023 00:00:13 GMT
      Link:
      - <https://api.polygon.io/vX/reference/financials?cursor=**********************************************************************************************************************************************************************************************************>;
        rel="next"
      Server:
      - nginx/1.19.2
      Strict-Transport-Security:
      - max-age=15724800; includeSubDomains
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding
      X-Item-Count:
      - '5'
      X-Request-Id:
      - 907a54ae5eab0bdd9ce68ab1ec24b2f4
    status:
      code: 200
      message: OK
version: 1
