[tool.poetry]
name = "openbb-technical"
version = "1.4.4"
description = "Technical Analysis extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_technical" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.8"
pandas-ta-openbb = "^0.4.20"
scikit-learn = "^1.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_core_extension"]
technical = "openbb_technical.technical_router:router"

[tool.poetry.plugins."openbb_charting_extension"]
technical = "openbb_technical.technical_views:TechnicalViews"
