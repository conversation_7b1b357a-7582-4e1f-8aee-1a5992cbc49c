[tool.poetry]
name = "openbb-biztoc"
version = "1.4.3"
description = "Biztoc Provider for OpenBB Platform"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_biztoc" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.8"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_provider_extension"]
biztoc = "openbb_biztoc:biztoc_provider"
