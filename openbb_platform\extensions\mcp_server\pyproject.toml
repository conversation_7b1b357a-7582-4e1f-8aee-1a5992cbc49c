[tool.poetry]
name = "openbb-mcp-server"
version = "1.0.0"
description = "OpenBB Platform MCP Server"
authors = ["OpenBB <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
homepage = "https://openbb.co"
repository = "https://github.com/openbb-finance/openbb"
documentation = "https://docs.openbb.co"
packages = [{ include = "openbb_mcp_server" }]

[tool.poetry.scripts]
openbb-mcp = "openbb_mcp_server.main:main"

[tool.poetry.dependencies]
python = ">=3.10,<3.13"
openbb-core = "^1.4.8"
fastmcp = "^2.10.5"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
