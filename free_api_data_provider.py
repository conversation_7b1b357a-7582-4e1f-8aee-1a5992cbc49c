"""
免费API数据提供者
整合多个免费股票数据API，无需本地OpenBB
"""

import requests
import pandas as pd
import yfinance as yf
import time
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

class FreeAPIDataProvider:
    """免费API数据提供者"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # API配置 (从环境变量获取，如果没有则为空)
        import os
        self.api_keys = {
            'alpha_vantage': os.getenv('ALPHA_VANTAGE_API_KEY', ''),
            'twelve_data': os.getenv('TWELVE_DATA_API_KEY', ''),
            'polygon': os.getenv('POLYGON_API_KEY', ''),
            'iex_cloud': os.getenv('IEX_CLOUD_API_KEY', '')
        }
        
        # API基础URL
        self.api_urls = {
            'alpha_vantage': 'https://www.alphavantage.co/query',
            'twelve_data': 'https://api.twelvedata.com',
            'polygon': 'https://api.polygon.io',
            'iex_cloud': 'https://cloud.iexapis.com/stable'
        }
        
        # 请求限制 (每分钟)
        self.rate_limits = {
            'alpha_vantage': 5,    # 每分钟5次
            'twelve_data': 8,      # 每分钟8次
            'polygon': 5,          # 每分钟5次
            'iex_cloud': 100       # 每分钟100次
        }
        
        self.last_request_time = {}
    
    def _wait_for_rate_limit(self, api_name: str):
        """等待API速率限制"""
        if api_name in self.last_request_time:
            time_since_last = time.time() - self.last_request_time[api_name]
            min_interval = 60 / self.rate_limits[api_name]
            
            if time_since_last < min_interval:
                wait_time = min_interval - time_since_last
                time.sleep(wait_time)
        
        self.last_request_time[api_name] = time.time()
    
    def get_stock_quote_yfinance(self, symbol: str) -> Dict:
        """使用yfinance获取股票报价 (免费，无API密钥)"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d")
            
            if hist.empty:
                return {}
            
            latest = hist.iloc[-1]
            
            return {
                'symbol': symbol,
                'price': float(latest['Close']),
                'open': float(latest['Open']),
                'high': float(latest['High']),
                'low': float(latest['Low']),
                'volume': int(latest['Volume']),
                'change': float(latest['Close'] - latest['Open']),
                'change_percent': float((latest['Close'] - latest['Open']) / latest['Open'] * 100),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'source': 'yfinance'
            }
            
        except Exception as e:
            self.logger.error(f"yfinance获取数据失败 {symbol}: {e}")
            return {}
    
    def get_stock_quote_alpha_vantage(self, symbol: str) -> Dict:
        """使用Alpha Vantage获取股票报价"""
        if not self.api_keys['alpha_vantage']:
            return {}
        
        try:
            self._wait_for_rate_limit('alpha_vantage')
            
            params = {
                'function': 'GLOBAL_QUOTE',
                'symbol': symbol,
                'apikey': self.api_keys['alpha_vantage']
            }
            
            response = requests.get(self.api_urls['alpha_vantage'], params=params, timeout=10)
            data = response.json()
            
            if 'Global Quote' in data:
                quote = data['Global Quote']
                return {
                    'symbol': symbol,
                    'price': float(quote.get('05. price', 0)),
                    'open': float(quote.get('02. open', 0)),
                    'high': float(quote.get('03. high', 0)),
                    'low': float(quote.get('04. low', 0)),
                    'volume': int(quote.get('06. volume', 0)),
                    'change': float(quote.get('09. change', 0)),
                    'change_percent': float(quote.get('10. change percent', '0%').replace('%', '')),
                    'source': 'alpha_vantage'
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Alpha Vantage获取数据失败 {symbol}: {e}")
            return {}
    
    def get_stock_quote_twelve_data(self, symbol: str) -> Dict:
        """使用Twelve Data获取股票报价"""
        if not self.api_keys['twelve_data']:
            return {}
        
        try:
            self._wait_for_rate_limit('twelve_data')
            
            url = f"{self.api_urls['twelve_data']}/quote"
            params = {
                'symbol': symbol,
                'apikey': self.api_keys['twelve_data']
            }
            
            response = requests.get(url, params=params, timeout=10)
            data = response.json()
            
            if 'symbol' in data:
                return {
                    'symbol': symbol,
                    'price': float(data.get('close', 0)),
                    'open': float(data.get('open', 0)),
                    'high': float(data.get('high', 0)),
                    'low': float(data.get('low', 0)),
                    'volume': int(data.get('volume', 0)),
                    'change': float(data.get('change', 0)),
                    'change_percent': float(data.get('percent_change', 0)),
                    'source': 'twelve_data'
                }
            
            return {}
            
        except Exception as e:
            self.logger.error(f"Twelve Data获取数据失败 {symbol}: {e}")
            return {}
    
    def get_historical_data_yfinance(self, symbol: str, period: str = "60d") -> pd.DataFrame:
        """使用yfinance获取历史数据"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            
            if not hist.empty:
                # 重命名列以匹配标准格式
                hist = hist.rename(columns={
                    'Open': 'open',
                    'High': 'high', 
                    'Low': 'low',
                    'Close': 'close',
                    'Volume': 'volume'
                })
                
                hist['symbol'] = symbol
                hist['date'] = hist.index
                
            return hist
            
        except Exception as e:
            self.logger.error(f"yfinance获取历史数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> Dict:
        """计算技术指标"""
        if df.empty:
            return {}
        
        try:
            # 确保有足够的数据
            if len(df) < 20:
                return {}
            
            # RSI计算
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.iloc[-1]
            
            # MACD计算
            def calculate_macd(prices):
                exp1 = prices.ewm(span=12).mean()
                exp2 = prices.ewm(span=26).mean()
                macd = exp1 - exp2
                signal = macd.ewm(span=9).mean()
                return macd.iloc[-1], signal.iloc[-1]
            
            # 移动平均线
            ma20 = df['close'].rolling(window=20).mean().iloc[-1]
            ma60 = df['close'].rolling(window=60).mean() if len(df) >= 60 else None
            ma60_value = ma60.iloc[-1] if ma60 is not None and not ma60.empty else 0
            
            # 布林带
            bb_period = 20
            bb_std = 2
            bb_middle = df['close'].rolling(window=bb_period).mean().iloc[-1]
            bb_std_val = df['close'].rolling(window=bb_period).std().iloc[-1]
            bb_upper = bb_middle + (bb_std_val * bb_std)
            bb_lower = bb_middle - (bb_std_val * bb_std)
            
            current_price = df['close'].iloc[-1]
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower)
            
            # 成交量比率
            avg_volume = df['volume'].rolling(window=20).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # 计算指标
            rsi = calculate_rsi(df['close'])
            macd, macd_signal = calculate_macd(df['close'])
            
            return {
                'rsi': float(rsi) if not pd.isna(rsi) else 50,
                'macd': float(macd) if not pd.isna(macd) else 0,
                'macd_signal': float(macd_signal) if not pd.isna(macd_signal) else 0,
                'ma20': float(ma20) if not pd.isna(ma20) else 0,
                'ma60': float(ma60_value) if not pd.isna(ma60_value) else 0,
                'bb_upper': float(bb_upper) if not pd.isna(bb_upper) else 0,
                'bb_lower': float(bb_lower) if not pd.isna(bb_lower) else 0,
                'bb_middle': float(bb_middle) if not pd.isna(bb_middle) else 0,
                'bb_position': float(bb_position) if not pd.isna(bb_position) else 0.5,
                'volume_ratio': float(volume_ratio) if not pd.isna(volume_ratio) else 1
            }
            
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return {}
    
    def get_comprehensive_stock_data(self, symbol: str) -> Dict:
        """获取综合股票数据"""
        # 尝试多个数据源，优先使用免费的yfinance
        quote_data = self.get_stock_quote_yfinance(symbol)
        
        # 如果yfinance失败，尝试其他API
        if not quote_data:
            quote_data = self.get_stock_quote_alpha_vantage(symbol)
        
        if not quote_data:
            quote_data = self.get_stock_quote_twelve_data(symbol)
        
        # 获取历史数据计算技术指标
        historical_data = self.get_historical_data_yfinance(symbol)
        technical_indicators = self.calculate_technical_indicators(historical_data)
        
        # 合并数据
        comprehensive_data = {
            **quote_data,
            **technical_indicators,
            'timestamp': datetime.now().isoformat()
        }
        
        return comprehensive_data
    
    def get_news_sentiment(self, symbol: str) -> Dict:
        """获取新闻情感分析 (简化版)"""
        # 这里可以集成免费的新闻API
        # 暂时返回模拟数据
        return {
            'sentiment_score': 0,
            'news_count': 0,
            'latest_news': []
        }

# 使用示例
if __name__ == "__main__":
    provider = FreeAPIDataProvider()
    
    # 测试获取股票数据
    symbols = ['AAPL', '000001.SZ', 'TSLA']
    
    for symbol in symbols:
        print(f"\n获取 {symbol} 数据:")
        data = provider.get_comprehensive_stock_data(symbol)
        
        if data:
            print(f"价格: ${data.get('price', 0):.2f}")
            print(f"涨跌幅: {data.get('change_percent', 0):.2f}%")
            print(f"RSI: {data.get('rsi', 0):.1f}")
            print(f"数据源: {data.get('source', 'unknown')}")
        else:
            print("获取数据失败")
