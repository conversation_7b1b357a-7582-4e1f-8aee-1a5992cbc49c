#!/usr/bin/env python3
"""
基于OpenBB的Web界面原型
展示如何将OpenBB功能集成到Web应用中
"""

from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional

try:
    from openbb import obb
    OPENBB_AVAILABLE = True
except ImportError:
    OPENBB_AVAILABLE = False

app = FastAPI(title="OpenBB Web Interface Demo")

class OpenBBWebInterface:
    """OpenBB Web界面"""
    
    def __init__(self):
        self.popular_stocks = {
            "美股": ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"],
            "中国A股": ["000001.SZ", "600519.SS", "000858.SZ", "002415.SZ", "300059.SZ"]
        }
    
    async def get_stock_quote(self, symbol: str) -> Dict:
        """获取股票报价"""
        if not OPENBB_AVAILABLE:
            return {"error": "OpenBB not available"}
        
        try:
            quote = obb.equity.price.quote(symbol=symbol, provider="yfinance")
            if quote.results:
                result = quote.results[0]
                return {
                    "symbol": symbol,
                    "price": getattr(result, 'last_price', 0) or getattr(result, 'price', 0),
                    "change": getattr(result, 'change', 0),
                    "change_percent": getattr(result, 'change_percent', 0),
                    "volume": getattr(result, 'volume', 0),
                    "timestamp": datetime.now().isoformat()
                }
            return {"error": "No data available"}
        except Exception as e:
            return {"error": str(e)}
    
    async def get_historical_data(self, symbol: str, days: int = 30) -> Dict:
        """获取历史数据"""
        if not OPENBB_AVAILABLE:
            return {"error": "OpenBB not available"}
        
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            hist = obb.equity.price.historical(
                symbol=symbol,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d"),
                provider="yfinance"
            )
            
            if hist.results:
                df = hist.to_df()
                return {
                    "symbol": symbol,
                    "data": df.to_dict('records'),
                    "count": len(df)
                }
            return {"error": "No historical data"}
        except Exception as e:
            return {"error": str(e)}
    
    async def get_news(self, symbol: Optional[str] = None, limit: int = 10) -> Dict:
        """获取新闻"""
        if not OPENBB_AVAILABLE:
            return {"error": "OpenBB not available"}
        
        try:
            if symbol:
                news = obb.news.company(symbol=symbol, provider="biztoc", limit=limit)
            else:
                news = obb.news.world(provider="biztoc", limit=limit)
            
            if news.results:
                articles = []
                for article in news.results:
                    articles.append({
                        "title": getattr(article, 'title', ''),
                        "date": str(getattr(article, 'date', '')),
                        "url": getattr(article, 'url', ''),
                        "text": getattr(article, 'text', '')[:200] + "..." if getattr(article, 'text', '') else ""
                    })
                return {"articles": articles, "count": len(articles)}
            return {"error": "No news available"}
        except Exception as e:
            return {"error": str(e)}

# 创建接口实例
interface = OpenBBWebInterface()

@app.get("/", response_class=HTMLResponse)
async def home():
    """主页"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>OpenBB Web Interface Demo</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
            .section { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .stock-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
            .stock-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; background: #f9f9f9; }
            .price { font-size: 24px; font-weight: bold; color: #27ae60; }
            .change { font-size: 14px; }
            .positive { color: #27ae60; }
            .negative { color: #e74c3c; }
            .button { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
            .button:hover { background: #2980b9; }
            .news-item { border-bottom: 1px solid #eee; padding: 10px 0; }
            .loading { color: #7f8c8d; font-style: italic; }
            .error { color: #e74c3c; background: #fdf2f2; padding: 10px; border-radius: 4px; }
            .tabs { display: flex; margin-bottom: 20px; }
            .tab { padding: 10px 20px; background: #ecf0f1; border: none; cursor: pointer; margin-right: 5px; }
            .tab.active { background: #3498db; color: white; }
            .tab-content { display: none; }
            .tab-content.active { display: block; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📊 OpenBB Web Interface Demo</h1>
                <p>基于OpenBB Platform的Web金融数据界面演示</p>
            </div>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('stocks')">股票数据</button>
                <button class="tab" onclick="showTab('news')">财经新闻</button>
                <button class="tab" onclick="showTab('analysis')">技术分析</button>
                <button class="tab" onclick="showTab('api')">API文档</button>
            </div>
            
            <!-- 股票数据标签页 -->
            <div id="stocks" class="tab-content active">
                <div class="section">
                    <h2>🔍 股票查询</h2>
                    <input type="text" id="stockSymbol" placeholder="输入股票代码 (如: AAPL, 000001.SZ)" style="padding: 10px; width: 300px;">
                    <button class="button" onclick="searchStock()">查询</button>
                    <div id="stockResult" style="margin-top: 20px;"></div>
                </div>
                
                <div class="section">
                    <h2>📈 热门股票</h2>
                    <div id="popularStocks" class="stock-grid">
                        <div class="loading">正在加载热门股票数据...</div>
                    </div>
                </div>
            </div>
            
            <!-- 新闻标签页 -->
            <div id="news" class="tab-content">
                <div class="section">
                    <h2>📰 财经新闻</h2>
                    <button class="button" onclick="loadNews()">刷新新闻</button>
                    <div id="newsContent" style="margin-top: 20px;">
                        <div class="loading">点击刷新按钮加载新闻...</div>
                    </div>
                </div>
            </div>
            
            <!-- 技术分析标签页 -->
            <div id="analysis" class="tab-content">
                <div class="section">
                    <h2>📊 技术分析</h2>
                    <p>技术分析功能演示 (需要历史数据)</p>
                    <input type="text" id="analysisSymbol" placeholder="输入股票代码">
                    <button class="button" onclick="performAnalysis()">分析</button>
                    <div id="analysisResult" style="margin-top: 20px;"></div>
                </div>
            </div>
            
            <!-- API文档标签页 -->
            <div id="api" class="tab-content">
                <div class="section">
                    <h2>📚 OpenBB API 使用示例</h2>
                    <h3>股票数据获取</h3>
                    <pre style="background: #f4f4f4; padding: 15px; border-radius: 4px;">
# 获取股票报价
quote = obb.equity.price.quote(symbol="AAPL", provider="yfinance")

# 获取历史数据
hist = obb.equity.price.historical(
    symbol="AAPL", 
    start_date="2024-01-01",
    provider="yfinance"
)

# 获取基本面数据
fundamental = obb.equity.fundamental.overview(symbol="AAPL")
                    </pre>
                    
                    <h3>新闻数据获取</h3>
                    <pre style="background: #f4f4f4; padding: 15px; border-radius: 4px;">
# 获取全球新闻
news = obb.news.world(provider="biztoc", limit=10)

# 获取公司新闻
company_news = obb.news.company(symbol="AAPL", provider="benzinga")
                    </pre>
                </div>
            </div>
        </div>
        
        <script>
            // 标签页切换
            function showTab(tabName) {
                // 隐藏所有标签页内容
                const contents = document.querySelectorAll('.tab-content');
                contents.forEach(content => content.classList.remove('active'));
                
                // 移除所有标签的active类
                const tabs = document.querySelectorAll('.tab');
                tabs.forEach(tab => tab.classList.remove('active'));
                
                // 显示选中的标签页
                document.getElementById(tabName).classList.add('active');
                event.target.classList.add('active');
            }
            
            // 搜索股票
            async function searchStock() {
                const symbol = document.getElementById('stockSymbol').value;
                if (!symbol) return;
                
                const resultDiv = document.getElementById('stockResult');
                resultDiv.innerHTML = '<div class="loading">正在查询...</div>';
                
                try {
                    const response = await fetch(`/api/stock/${symbol}`);
                    const data = await response.json();
                    
                    if (data.error) {
                        resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="stock-card">
                                <h3>${data.symbol}</h3>
                                <div class="price">$${data.price.toFixed(2)}</div>
                                <div class="change ${data.change >= 0 ? 'positive' : 'negative'}">
                                    ${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} 
                                    (${data.change_percent.toFixed(2)}%)
                                </div>
                                <div>成交量: ${data.volume.toLocaleString()}</div>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">请求失败: ${error.message}</div>`;
                }
            }
            
            // 加载热门股票
            async function loadPopularStocks() {
                const container = document.getElementById('popularStocks');
                
                const symbols = ['AAPL', 'MSFT', 'GOOGL', '000001.SZ', '600519.SS'];
                container.innerHTML = '';
                
                for (const symbol of symbols) {
                    try {
                        const response = await fetch(`/api/stock/${symbol}`);
                        const data = await response.json();
                        
                        if (!data.error) {
                            const card = document.createElement('div');
                            card.className = 'stock-card';
                            card.innerHTML = `
                                <h3>${data.symbol}</h3>
                                <div class="price">$${data.price.toFixed(2)}</div>
                                <div class="change ${data.change >= 0 ? 'positive' : 'negative'}">
                                    ${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} 
                                    (${data.change_percent.toFixed(2)}%)
                                </div>
                            `;
                            container.appendChild(card);
                        }
                    } catch (error) {
                        console.error(`Failed to load ${symbol}:`, error);
                    }
                }
            }
            
            // 加载新闻
            async function loadNews() {
                const container = document.getElementById('newsContent');
                container.innerHTML = '<div class="loading">正在加载新闻...</div>';
                
                try {
                    const response = await fetch('/api/news');
                    const data = await response.json();
                    
                    if (data.error) {
                        container.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                    } else {
                        let html = '';
                        data.articles.forEach(article => {
                            html += `
                                <div class="news-item">
                                    <h4><a href="${article.url}" target="_blank">${article.title}</a></h4>
                                    <p>${article.text}</p>
                                    <small>📅 ${article.date}</small>
                                </div>
                            `;
                        });
                        container.innerHTML = html;
                    }
                } catch (error) {
                    container.innerHTML = `<div class="error">加载失败: ${error.message}</div>`;
                }
            }
            
            // 技术分析
            async function performAnalysis() {
                const symbol = document.getElementById('analysisSymbol').value;
                if (!symbol) return;
                
                const resultDiv = document.getElementById('analysisResult');
                resultDiv.innerHTML = '<div class="loading">正在分析...</div>';
                
                try {
                    const response = await fetch(`/api/analysis/${symbol}`);
                    const data = await response.json();
                    
                    if (data.error) {
                        resultDiv.innerHTML = `<div class="error">错误: ${data.error}</div>`;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="stock-card">
                                <h3>${data.symbol} 技术分析</h3>
                                <p>数据点数: ${data.count}</p>
                                <p>分析时间段: 最近30天</p>
                                <p>💡 这里可以添加RSI、MACD等技术指标计算结果</p>
                            </div>
                        `;
                    }
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">分析失败: ${error.message}</div>`;
                }
            }
            
            // 页面加载时初始化
            window.onload = function() {
                loadPopularStocks();
            };
        </script>
    </body>
    </html>
    """)

@app.get("/api/stock/{symbol}")
async def get_stock_data(symbol: str):
    """获取股票数据API"""
    return await interface.get_stock_quote(symbol)

@app.get("/api/news")
async def get_news_data(symbol: Optional[str] = None):
    """获取新闻数据API"""
    return await interface.get_news(symbol)

@app.get("/api/analysis/{symbol}")
async def get_analysis_data(symbol: str):
    """获取技术分析数据API"""
    return await interface.get_historical_data(symbol)

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动OpenBB Web界面演示")
    print("📱 访问地址: http://localhost:8080")
    uvicorn.run(app, host="0.0.0.0", port=8080)
