#!/usr/bin/env python3
"""
免费AI股票分析系统
使用开源模型和算法，无需API密钥
"""

import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
import json

class FreeAIStockAnalyzer:
    """免费AI股票分析器"""
    
    def __init__(self):
        self.analysis_rules = self._load_analysis_rules()
    
    def _load_analysis_rules(self) -> Dict:
        """加载分析规则"""
        return {
            "rsi_rules": {
                "oversold": {"threshold": 30, "signal": "买入", "weight": 25},
                "overbought": {"threshold": 70, "signal": "卖出", "weight": -20},
                "neutral": {"signal": "观望", "weight": 5}
            },
            "ma_rules": {
                "golden_cross": {"signal": "买入", "weight": 30},
                "death_cross": {"signal": "卖出", "weight": -25},
                "above_ma": {"signal": "持有", "weight": 15},
                "below_ma": {"signal": "观望", "weight": -10}
            },
            "volume_rules": {
                "high_volume": {"threshold": 1.5, "weight": 15},
                "low_volume": {"threshold": 0.5, "weight": -10}
            },
            "trend_rules": {
                "strong_uptrend": {"weight": 25},
                "uptrend": {"weight": 15},
                "sideways": {"weight": 0},
                "downtrend": {"weight": -15},
                "strong_downtrend": {"weight": -25}
            }
        }
    
    def get_stock_data(self, symbol: str, period: str = "60d") -> Dict:
        """获取股票数据"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period=period)
            info = ticker.info
            
            if hist.empty:
                return {"error": "无法获取数据"}
            
            latest = hist.iloc[-1]
            prev_close = info.get('previousClose', latest['Open'])
            
            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'price': float(latest['Close']),
                'change': float(latest['Close'] - prev_close),
                'change_percent': float((latest['Close'] - prev_close) / prev_close * 100),
                'volume': int(latest['Volume']),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'history': hist
            }
        except Exception as e:
            return {"error": str(e)}
    
    def calculate_technical_indicators(self, hist_data: pd.DataFrame) -> Dict:
        """计算技术指标"""
        try:
            # RSI计算
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.iloc[-1] if not rsi.empty else 50
            
            # 移动平均线
            ma5 = hist_data['Close'].rolling(window=5).mean().iloc[-1]
            ma20 = hist_data['Close'].rolling(window=20).mean().iloc[-1]
            ma60 = hist_data['Close'].rolling(window=60).mean().iloc[-1] if len(hist_data) >= 60 else ma20
            
            # RSI
            rsi = calculate_rsi(hist_data['Close'])
            
            # MACD
            exp1 = hist_data['Close'].ewm(span=12).mean()
            exp2 = hist_data['Close'].ewm(span=26).mean()
            macd = exp1 - exp2
            signal = macd.ewm(span=9).mean()
            macd_histogram = macd - signal
            
            # 布林带
            bb_period = 20
            bb_std = 2
            bb_ma = hist_data['Close'].rolling(window=bb_period).mean()
            bb_std_dev = hist_data['Close'].rolling(window=bb_period).std()
            bb_upper = bb_ma + (bb_std_dev * bb_std)
            bb_lower = bb_ma - (bb_std_dev * bb_std)
            
            # 成交量分析
            avg_volume = hist_data['Volume'].rolling(window=20).mean().iloc[-1]
            current_volume = hist_data['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # 价格位置
            high_52w = hist_data['High'].max()
            low_52w = hist_data['Low'].min()
            current_price = hist_data['Close'].iloc[-1]
            price_position = (current_price - low_52w) / (high_52w - low_52w) * 100
            
            # 趋势判断
            trend = self._determine_trend(ma5, ma20, ma60)
            
            return {
                'rsi': float(rsi),
                'ma5': float(ma5),
                'ma20': float(ma20),
                'ma60': float(ma60),
                'macd': float(macd.iloc[-1]),
                'macd_signal': float(signal.iloc[-1]),
                'macd_histogram': float(macd_histogram.iloc[-1]),
                'bb_upper': float(bb_upper.iloc[-1]),
                'bb_lower': float(bb_lower.iloc[-1]),
                'bb_position': self._bb_position(current_price, bb_upper.iloc[-1], bb_lower.iloc[-1]),
                'volume_ratio': float(volume_ratio),
                'price_position': float(price_position),
                'trend': trend,
                'momentum': self._calculate_momentum(hist_data)
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _determine_trend(self, ma5: float, ma20: float, ma60: float) -> str:
        """判断趋势"""
        if ma5 > ma20 > ma60:
            return "strong_uptrend"
        elif ma5 > ma20:
            return "uptrend"
        elif ma5 < ma20 < ma60:
            return "strong_downtrend"
        elif ma5 < ma20:
            return "downtrend"
        else:
            return "sideways"
    
    def _bb_position(self, price: float, upper: float, lower: float) -> str:
        """布林带位置"""
        if price > upper:
            return "above_upper"
        elif price < lower:
            return "below_lower"
        else:
            return "within_bands"
    
    def _calculate_momentum(self, hist_data: pd.DataFrame) -> float:
        """计算动量"""
        if len(hist_data) < 10:
            return 0
        
        recent_prices = hist_data['Close'].tail(10)
        momentum = (recent_prices.iloc[-1] - recent_prices.iloc[0]) / recent_prices.iloc[0] * 100
        return float(momentum)
    
    def generate_ai_analysis(self, stock_data: Dict, technical_data: Dict) -> Dict:
        """生成AI分析"""
        try:
            symbol = stock_data['symbol']
            price = stock_data['price']
            
            # 初始化评分系统
            total_score = 0
            max_score = 0
            signals = []
            reasons = []
            risks = []
            
            # RSI分析
            rsi = technical_data['rsi']
            if rsi <= 30:
                score = self.analysis_rules['rsi_rules']['oversold']['weight']
                total_score += score
                signals.append("RSI超卖")
                reasons.append(f"RSI({rsi:.1f})显示超卖，可能反弹")
            elif rsi >= 70:
                score = self.analysis_rules['rsi_rules']['overbought']['weight']
                total_score += score
                signals.append("RSI超买")
                risks.append(f"RSI({rsi:.1f})显示超买，注意回调")
            else:
                score = self.analysis_rules['rsi_rules']['neutral']['weight']
                total_score += score
                reasons.append(f"RSI({rsi:.1f})处于正常区间")
            max_score += 25
            
            # 移动平均线分析
            trend = technical_data['trend']
            if trend in ['strong_uptrend', 'uptrend']:
                score = self.analysis_rules['trend_rules'][trend]['weight']
                total_score += score
                signals.append("趋势向上")
                reasons.append("均线呈多头排列，趋势向上")
            elif trend in ['strong_downtrend', 'downtrend']:
                score = self.analysis_rules['trend_rules'][trend]['weight']
                total_score += score
                signals.append("趋势向下")
                risks.append("均线呈空头排列，趋势向下")
            max_score += 25
            
            # 成交量分析
            volume_ratio = technical_data['volume_ratio']
            if volume_ratio >= 1.5:
                score = self.analysis_rules['volume_rules']['high_volume']['weight']
                total_score += score
                signals.append("成交量放大")
                reasons.append(f"成交量放大{volume_ratio:.1f}倍，资金关注度高")
            elif volume_ratio <= 0.5:
                score = self.analysis_rules['volume_rules']['low_volume']['weight']
                total_score += score
                signals.append("成交量萎缩")
                risks.append("成交量萎缩，市场关注度低")
            max_score += 15
            
            # MACD分析
            macd = technical_data['macd']
            macd_signal = technical_data['macd_signal']
            if macd > macd_signal and macd > 0:
                total_score += 15
                reasons.append("MACD金叉且在零轴上方")
            elif macd < macd_signal and macd < 0:
                total_score -= 15
                risks.append("MACD死叉且在零轴下方")
            max_score += 15
            
            # 布林带分析
            bb_position = technical_data['bb_position']
            if bb_position == "below_lower":
                total_score += 10
                reasons.append("价格跌破布林带下轨，可能超跌")
            elif bb_position == "above_upper":
                total_score -= 10
                risks.append("价格突破布林带上轨，可能超涨")
            max_score += 10
            
            # 价格位置分析
            price_position = technical_data['price_position']
            if price_position < 20:
                total_score += 15
                reasons.append("价格接近年内低点，安全边际较高")
            elif price_position > 80:
                total_score -= 15
                risks.append("价格接近年内高点，注意高位风险")
            max_score += 15
            
            # 动量分析
            momentum = technical_data['momentum']
            if momentum > 5:
                total_score += 10
                reasons.append(f"近期动量强劲({momentum:.1f}%)")
            elif momentum < -5:
                total_score -= 10
                risks.append(f"近期动量疲弱({momentum:.1f}%)")
            max_score += 10
            
            # 计算最终评分
            if max_score > 0:
                final_score = (total_score + max_score) / (2 * max_score) * 100
            else:
                final_score = 50
            
            # 生成投资建议
            if final_score >= 75:
                action = "强烈买入"
                confidence = min(95, final_score + 10)
            elif final_score >= 60:
                action = "买入"
                confidence = min(85, final_score + 5)
            elif final_score >= 45:
                action = "观望"
                confidence = min(75, final_score)
            elif final_score >= 30:
                action = "谨慎观望"
                confidence = max(40, final_score - 5)
            else:
                action = "卖出"
                confidence = max(30, final_score - 10)
            
            # 确保有足够的理由和风险
            if not reasons:
                reasons = ["基于技术指标的综合分析"]
            if not risks:
                risks = ["市场波动风险", "个股特有风险"]
            
            return {
                'symbol': symbol,
                'action': action,
                'confidence': int(confidence),
                'score': int(final_score),
                'reasons': reasons,
                'risks': risks,
                'signals': signals,
                'technical_summary': {
                    'rsi': rsi,
                    'trend': trend,
                    'volume_ratio': volume_ratio,
                    'momentum': momentum,
                    'price_position': price_position
                },
                'timestamp': datetime.now().isoformat(),
                'ai_engine': 'free_analyzer'
            }
            
        except Exception as e:
            return {
                'symbol': stock_data.get('symbol', 'Unknown'),
                'error': str(e),
                'ai_engine': 'free_analyzer'
            }
    
    def analyze_stock(self, symbol: str) -> Dict:
        """完整股票分析"""
        # 获取股票数据
        stock_data = self.get_stock_data(symbol)
        if 'error' in stock_data:
            return stock_data
        
        # 计算技术指标
        technical_data = self.calculate_technical_indicators(stock_data['history'])
        if 'error' in technical_data:
            return technical_data
        
        # 生成AI分析
        analysis = self.generate_ai_analysis(stock_data, technical_data)
        
        # 合并所有数据
        result = {**stock_data, **analysis}
        result.pop('history', None)  # 移除历史数据以减少输出
        
        return result

# 测试函数
def test_free_analyzer():
    """测试免费分析器"""
    analyzer = FreeAIStockAnalyzer()
    
    test_symbols = ["AAPL", "000001.SZ", "600519.SS"]
    
    print("🤖 免费AI股票分析系统测试")
    print("=" * 60)
    
    for symbol in test_symbols:
        print(f"\n📊 分析 {symbol}...")
        result = analyzer.analyze_stock(symbol)
        
        if 'error' in result:
            print(f"❌ 错误: {result['error']}")
            continue
        
        print(f"✅ {result['name']} ({result['symbol']})")
        print(f"💰 价格: ${result['price']:.2f} ({result['change_percent']:+.2f}%)")
        print(f"🎯 建议: {result['action']} (信心度: {result['confidence']}%)")
        print(f"📈 评分: {result['score']}/100")
        print(f"💡 理由: {', '.join(result['reasons'][:2])}")
        if result['risks']:
            print(f"⚠️  风险: {', '.join(result['risks'][:2])}")

if __name__ == "__main__":
    test_free_analyzer()
