interactions:
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip
      Connection:
      - keep-alive
      X-RapidAPI-Host:
      - biztoc.p.rapidapi.com
      X-RapidAPI-Key:
      - MOCK_API_KEY
    method: GET
    uri: https://biztoc.p.rapidapi.com/sources
  response:
    body:
      string: '[{"id":"tfswallst","title":"24/7 Wall Street","web":"https://247wallst.com/"},{"id":"abc","title":"ABC
        News","web":"https://abcnews.go.com"},{"id":"aol","title":"AOL Finance","web":"https://www.aol.com/finance/"},{"id":"apnews","title":"AP
        News","web":"https://apnews.com"},{"id":"abnormalreturns","title":"Abnormal
        Returns","web":"https://abnormalreturns.com/"},{"id":"asiafinancial","title":"Asia
        Financial","web":"https://www.asiafinancial.com"},{"id":"axios","title":"Axios","web":"https://www.axios.com"},{"id":"bbc","title":"BBC","web":"https://www.bbc.com"},{"id":"barchart","title":"Barchart","web":"https://www.barchart.com/"},{"id":"barrons","title":"Barrons","web":"https://www.barrons.com"},{"id":"ritholtz","title":"Barry
        Ritholtz","web":"https://ritholtz.com/"},{"id":"benzinga","title":"Benzinga","web":"https://www.benzinga.com/"},{"id":"blockworks","title":"Blockworks","web":"https://blockworks.co"},{"id":"bloomberg","title":"Bloomberg","web":"https://www.bloomberg.com/"},{"id":"quicktake","title":"Bloomberg
        Quicktake","web":"https://www.youtube.com/channel/UChirEOpgFCupRAk5etXqPaA"},{"id":"boston","title":"Boston
        Herald","web":"https://www.bostonherald.com/business/"},{"id":"bizjournals","title":"Business
        Journals","web":"https://www.bizjournals.com/"},{"id":"cbc","title":"CBC","web":"https://www.cbc.ca/s"},{"id":"cbs","title":"CBS","web":"https://www.cbsnews.com"},{"id":"cnbc","title":"CNBC","web":"https://www.cnbc.com/"},{"id":"cnn","title":"CNN","web":"https://edition.cnn.com/business"},{"id":"chicago","title":"Chicago
        Tribune","web":"https://www.chicagotribune.com/business/"},{"id":"coindesk","title":"Coindesk","web":"https://www.coindesk.com"},{"id":"dw","title":"DW","web":"https://www.dw.com/en/business/s-1431"},{"id":"dailyupside","title":"Daily
        Upside","web":"https://www.thedailyupside.com/"},{"id":"dealbreaker","title":"Dealbreaker","web":"https://dealbreaker.com/"},{"id":"entrepreneur","title":"Entrepreneur","web":"https://www.entrepreneur.com/"},{"id":"stocktwits","title":"EurActiv","web":"https://www.euractiv.com/"},{"id":"euronews","title":"Euronews","web":"https://www.euronews.com/"},{"id":"fastcompany","title":"FastCompany","web":"https://www.fastcompany.com"},{"id":"finpost","title":"Financial
        Post","web":"https://financialpost.com/"},{"id":"ft","title":"Financial Times","web":"https://www.ft.com"},{"id":"forbes","title":"Forbes","web":"https://www.forbes.com"},{"id":"fortune","title":"Fortune","web":"https://fortune.com/"},{"id":"fox","title":"Fox
        Business","web":"https://www.foxbusiness.com"},{"id":"globeandmail","title":"Globe
        And Mail","web":"https://www.theglobeandmail.com"},{"id":"google_business","title":"Google
        Business","web":"https://news.google.com/topics/CAAqJggKIiBDQkFTRWdvSUwyMHZNRGx6TVdZU0FtVnVHZ0pWVXlnQVAB?hl=en-US&gl=US&ceid=US%3Aen"},{"id":"googletrends","title":"Google
        Trends","web":"https://trends.google.com/trends/?geo=US"},{"id":"inc","title":"Inc.","web":"https://www.inc.com"},{"id":"insider","title":"Insider","web":"https://www.insider.com"},{"id":"instinv","title":"Institutional
        Investor","web":"https://www.institutionalinvestor.com/"},{"id":"investing","title":"Investing.com","web":"https://www.investing.com"},{"id":"investorplace","title":"InvestorPlace","web":"https://investorplace.com"},{"id":"arabnews","title":"Japan
        Times","web":"https://www.japantimes.co.jp"},{"id":"latimes","title":"LA Times","web":"https://www.latimes.com"},{"id":"msnbc","title":"MSNBC","web":"https://www.msnbc.com/"},{"id":"marginalrev","title":"Marginal
        Revolution","web":"https://marginalrevolution.com"},{"id":"marketbeat","title":"MarketBeat","web":"https://www.marketbeat.com/headlines"},{"id":"marketplace","title":"Marketplace","web":"https://www.marketplace.org"},{"id":"marketwatch","title":"Marketwatch","web":"https://www.marketwatch.com"},{"id":"mises","title":"Mises
        Institute","web":"https://mises.org"},{"id":"morningbrew","title":"Morning
        Brew","web":"https://www.morningbrew.com"},{"id":"nbc","title":"NBC","web":"https://www.nbcnews.com/"},{"id":"npr","title":"NPR","web":"https://www.npr.org"},{"id":"nyt","title":"NYT
        Business","web":"https://www.nytimes.com/section/business"},{"id":"nakedcapitalism","title":"Naked
        Capitalism","web":"https://www.nakedcapitalism.com"},{"id":"newsweek","title":"Newsweek","web":"https://www.newsweek.com"},{"id":"nikkei","title":"Nikkei
        Asia","web":"https://asia.nikkei.com/"},{"id":"observer","title":"Observer","web":"https://observer.com/"},{"id":"pbs_newshour","title":"PBS
        Newshour","web":"https://www.pbs.org/"},{"id":"pymnts","title":"PYMNTS","web":"https://www.pymnts.com/"},{"id":"podcasts","title":"Podcasts","web":""},{"id":"politico","title":"Politico","web":"https://www.politico.com"},{"id":"rcm","title":"RCM","web":""},{"id":"reason","title":"Reason","web":"https://reason.com/"},{"id":"reddit","title":"Reddit","web":"https://www.reddit.com"},{"id":"reuters","title":"Reuters","web":"https://reuters.com"},{"id":"scmp","title":"SCMP","web":"https://www.scmp.com/"},{"id":"sec","title":"SEC
        / FED","web":"https://www.sec.gov/"},{"id":"seattle","title":"Seattle Times","web":"https://www.seattletimes.com/business/"},{"id":"seekingalpha","title":"Seeking
        Alpha","web":"https://seekingalpha.com"},{"id":"semafor","title":"Semafor","web":"https://www.semafor.com/"},{"id":"siliconrepublic","title":"Silicon
        Republic","web":"https://www.siliconrepublic.com/"},{"id":"siliconvalley","title":"Silicon
        Valley","web":"https://www.siliconvalley.com/"},{"id":"statista","title":"Statista","web":"https://www.statista.com/"},{"id":"ted","title":"TED","web":"https://www.ted.com/"},{"id":"techco","title":"Tech
        Co","web":"https://tech.co/"},{"id":"techcrunch","title":"Techcrunch","web":"https://techcrunch.com/"},{"id":"techmeme","title":"Techmeme","web":"https://www.techmeme.com/"},{"id":"theblock","title":"The
        Block","web":"https://www.theblock.co/"},{"id":"economist","title":"The Economist","web":"https://www.economist.com"},{"id":"guardian","title":"The
        Guardian","web":"https://www.theguardian.com/"},{"id":"thehill","title":"The
        Hill","web":"https://thehill.com/business/"},{"id":"hustle","title":"The Hustle","web":"https://thehustle.co/"},{"id":"theregister","title":"The
        Register","web":"https://www.theregister.com/"},{"id":"thestreet","title":"The
        Street","web":"https://www.thestreet.com/"},{"id":"thetrade","title":"The
        Trade","web":"https://www.thetradenews.com/"},{"id":"theweek","title":"The
        Week","web":"https://theweek.com/"},{"id":"tipranks","title":"Tipranks","web":"https://www.tipranks.com/"},{"id":"vox","title":"Vox","web":"https://www.vox.com"},{"id":"wsj","title":"Wall
        Street Journal","web":"https://www.wsj.com/"},{"id":"wapo","title":"Washington
        Post","web":"https://www.washingtonpost.com"},{"id":"wolfstreet","title":"Wolf
        Street","web":"https://wolfstreet.com"},{"id":"yahoo_finance","title":"Yahoo
        Finance","web":"https://finance.yahoo.com/news/"},{"id":"yougov","title":"YouGov","web":"https://today.yougov.com"},{"id":"youtube_networks","title":"Youtube","web":"https://www.youtube.com"},{"id":"zerohedge","title":"ZeroHedge","web":"https://www.zerohedge.com/"},{"id":"investors","title":"investors.com","web":"https://www.investors.com/"}]

        '
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8d46253b4f35c990-IAD
      Cache-Control:
      - no-store, no-cache, must-revalidate, max-age=0
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Fri, 18 Oct 2024 05:32:01 GMT
      NEL:
      - '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}'
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=6AJ37sdn9g018QqveznvFOUcc06J0MlsSB7CDEFKhN7XOhnexO7oUWGOx9%2BTeTH%2BUfbmxCFfUB9dRstLZUvq6koiunLgudzqseaUyrLMRwcL9G4TTGHbblRtT88%3D"}],"group":"cf-nel","max_age":604800}'
      Server:
      - RapidAPI-1.2.8
      Strict-Transport-Security:
      - max-age=31556926; includeSubDomains
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding, Cookie
      X-RapidAPI-Region:
      - AWS - us-east-1
      X-RapidAPI-Request-Id:
      - bb52b953b619ebdbae3d20f65f30c5041993d53bfcb4bd646a484856e7ad1bad
      X-RapidAPI-Version:
      - 1.2.8
      X-RateLimit-Requests-Limit:
      - '2000'
      X-RateLimit-Requests-Remaining:
      - '1914'
      X-RateLimit-Requests-Reset:
      - '1620568'
      X-RateLimit-rapid-free-plans-hard-limit-Limit:
      - '500000'
      X-RateLimit-rapid-free-plans-hard-limit-Remaining:
      - '499961'
      X-RateLimit-rapid-free-plans-hard-limit-Reset:
      - '1620568'
      alt-svc:
      - h3=":443"; ma=86400
      permissions-policy:
      - browsing-topics=()
      referrer-policy:
      - strict-origin-when-cross-origin
      rndr-id:
      - 96769602-78bc-4d9d
      x-content-type-options:
      - nosniff
      x-render-origin-server:
      - gunicorn
    status:
      code: 200
      message: OK
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip
      X-RapidAPI-Host:
      - biztoc.p.rapidapi.com
      X-RapidAPI-Key:
      - MOCK_API_KEY
    method: GET
    uri: https://biztoc.p.rapidapi.com/news/source/bloomberg
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAAA6xcbW/cOJL+fr+CMBa4L+ZEry2pgcMhduwkO3GcSzvrm707LCix1OK2RGpIyp2e
        xf73Q5HqTsbKLuRtfQmSjqsfmaV6f4r/87eLUvHDX3oNTwL2F+uLi8sL0W0v1n+7MBfri8ba3qxf
        vaq4/KkUv1lV/VSp7lUdhMVqVfFixTLG8uov5qc9lP3F5YX59SVSv3qxv19e9EPZCtMAv1hf3Gpx
        ScKc3FeWREGUkCBdh/k6isjbu4eLywsrbAsX64vrRkj2v0MUhIUh/zWAsaRWmlwz05APwLghVpFP
        LZPu4wf2VcgtsQ2Qz6JqLi4vBt1+97D7/f6nslWqK0Fv3fNK2Bv3RwvWgjav8GFoGNAwf1UhNjX0
        V4SltdK0YqahLcJSq2jfMuk+tg6W2gaoFlXznxrq//h2Jhd/v/zXNJCVcVbUeV6zMkrqOp+nganU
        CzQQrKPVMw3cDFr1QK40q8C4U2ZkY0Vdk+tWdMwCuWLVrmXm3OMGB0RLB+TOlVGDQLTyQLQcgZY6
        YFbyqKhCxuMkLxK+mnfAU6n5BxyE6yh7dsDvgPFmkBY02TDB8YX+YoDcsh2Q9xykFVaAe8/fDD2Q
        R9a2ZGM1gCUPmnHQZu7BM21F1cLvTr05oVPDBMf3ejBAa7YDKk7o+DEfeqB71rbUOHRqPfpS2uBp
        VierAlYsKMKgLudpYyo1VxvJOinWYfBMGw9M7JkkG3YwxDkf8kVvgZONGmxDXtdaVAx1ca85aPIz
        HMh9XYsKyJ16An6OJqxDpoYdDPWuZ0BkahCZMoeMelCITHdwoMoh0w6RF3M6SVwAD9I4D9KkZjO1
        MJWar4UgWAfhMy08AmttcyC3GmTVkGvV9UweyP1egjbeFzlX9F42oIVlsgJ0/uS6YXILZxnE3kPT
        2kHTmnWiRQtwfslHAQeCqhDf4DEIUD20sJg9pLBieZ5BmdVVzpN6niamUi/QRLhOn9vD1f3PZAM+
        6j40GoB8Rp9/PViDf2sAP2aSPOwVERK/Kr0km0E/wcEZ0TmqKNWOGvCh1yI21RgGqsEa/FsD+DGT
        1O5RFRSxqXHQzoqWM4mcs1XIWJmxNK7mxuGJ1MtM4rkiNnthDNkwWVmhpCGvtyCrA/mkVQmG3Ard
        GXLzxDgmQJ8HYwQjn8FYLfzPn6MHg9DUHKEpc9C0d9C0RmgKHppqB031d9BL6aGI87qM4iqtqnKV
        rGZmpFOpJfTwznkJ8hlqpTvylglpyGboe6UtuRosuVZDy8kvYMktE+0l+aQwajdqv4AiGodNtcOm
        W8SmxmPTcrC0Qmx6QMNhoqW9wpCN0IspIilWQZwmSRYlBY9neqap1HmKuPlyrAveagBJNo2oLfki
        OXhPxSxhxtUC74TFkH2tpBk6DCBvAb0XsPYcZcBADd0iNDUITQeEdo6KWcqMqwcaYTFQVEdougV0
        XsDapZSRxMAjSCtelisWxTO901TqTGVcX5F3zJDPYttYcquqwRAlyRWTO/JZmJ0hjLyBinEg9/KS
        3HylLm0SrD07SkBV0oYZqhGa1ghNlaQlkzuqEZoyyh00fgxffdokWLtokCiykq+gDFiZJrzOs5k2
        MZF6QfYarMPnedMb1gpFblu2NaMNmFOy2h6wXpBGoFMmqiaf1B40Bm20kS+bc3TAEZfWiDsagDml
        qu0BK4URl6qa9oiLARsNZFguXwojKLNVEYUrFsSrmdXcVGq+BuJ8HRfP64eGWbKRrO8xEG/UIDl5
        bwhW0fS9rFuGp3B0XB+h2s04dtULKZT8h2//MSvCbMhSM4Jj4SA5FYZiZU3FEZwaKqHaLfbir4q8
        CuM4ilZxxqq5UXki9YJjD6ZF9CPThu3J+0/35A2Ug8WqzHn6VnS9Af+647eSO6Z3GJaVtoOcXyr8
        g6bF3uFS0SvKERedi3PzHte/7ohLO4eLuazDXer4qyrM4wqKrArSrKzZvOOfSp3n/r/8TK4GIyQY
        AxhZOyYkeWT6gAf/gZVq0OR1jS2O64bpzmlHGvEE5zidYUfLEyjVDpTumcbCmLYOlDIExYpNd041
        DnSps4/DLIcwCXnEyryuw3lnP5U67+w/4Ru1ZS15q1oOkvxJGEZe930rKiatIRs7VDt08x9EV/re
        0gfAn7/vz64M+hGbbh02fRKGUXbCpgax0dW3iO06Sy1iU9UvWhpEGV9VeZonNQvKcG6tPJV6kSbi
        +Jkm3kuy6VkFl+SjIvcSyDWTWClocjeY3b9jdvTEWkM2lQbWnen4u8HsKJOcGoT8SrX7blphPUwN
        QOfyTmarhg79Yj26KueMpWUcVkmep3xmj24i9aKexMTZvyZXWu1AknvRkk+ih1ZIIJ/aAXs/Y6du
        M3B2irPXrBeWteS9xDKgYWr2S18Ds4N+VoW5hpxBAGroX4eSUQPSUiHxxPHbR7ejsPoaH4+WGthu
        ObfPeZhGVR2FWREn0cw23VTqRS989DzZeavUtoXjKV/hbzj05CMAN4RJPG7Q0iUdrCUPbEsezn/v
        tw6TGn+gQ08lwlEm8fy/wVGGrWs9GEst21ILrFtsbBCtwigLyzKuoiLjM0PuVOplzmbSIm1cPu/6
        PeqYw5ObFlzTBXNORpxm8lul2qOObrRmkvt/nef4ecWMpfvGZfeu9aOOGT2F8Skw+WS0VqqlhoKD
        XqzsDYI8KVarOgwhWWUzlTCVOs8AHvTQ9ZfkHdNamEvyZ2hBmp04rMlHhb/V8djdhPJRtC25kdzr
        aqcZeq1Hps80CIvPQBv3CPS34xNQ6R6AGj+m3Iu2pSC515DHxlxpKXWEWRrzqAo5j1lazrWJqdTL
        1PE8MFy1rNqRR6V3mPsLSa6F9Smokly5qQ55L6t2MKOJfFT4X1vAoeYnLZQW9nCOWVTC+uzT4aGT
        wieie/9E2HCg4oiPxiEV/ugWcNbZj/hLqaROS17xOl7FnMc8CGYO8CdS57mpt6A7JsmVaFv0y0KD
        cQHCN+Q6QC3datU5o7hWLT83PDg8Wn6H5yKE78J1gMlorVXnDKFS7WIOKViFSRpBzuswARan8457
        KjX3uON1Gq7D53yJK7ElH1U3aIbjecD8/x1rx0mxIY/CNm5WQO6fQJM7JkU/+LYAcirOKsdKsaXS
        QePAHqFpw9pxTGzoXtjGzQqoegJNu++gkVexWFHGozCMeJCWaV6xFU9mpqgTqfl6iIJ1kjwvyq7u
        r53XN+QOwGKQdmd/rYELSz8z95Gf2qBjsjhUUzV5L/lgrD7LCfWlqpzXN7Tz2P7wK4+tHfY4tkEn
        hdjotMSIvZQmyiwJ0gyiPK+LFKqZxcJUar4mwnCd5j/SxMYyjaVwwzSQq+GATJJLcvPrgPFhs2c9
        ztG2mnWuXXqllLFjo8icrQnjsKlBbFp6bAoOmpo963GO5qDRRZUIPfaKlutN53EZFGGa52EaQj23
        RTeRepEiJlQulyt5YsU7II9uQIYjGYEDm8/Qiq1QgyE3X6HzzQEfIp5Ao6m8/9PtWeQKlyQ5bkUD
        dO8mZNUITvURnMIJ3I33n2paMb2Ya8qjvIjrKirjpI5iFs3Tw1Rqrh6idRpPXdPvKHUbC9CS+8H2
        gyVMcnKtB5zRiBZnm0Li0V8raYUcAPWxaYeuPytDGkl1BoGpcsCujVEhsCuZ9QiMCnLAqAuDwMs1
        TaEMAlgFYVYlWZTObZo+l5qviChfp8+Lh5sONE7S4Vt/wngK2HWrjDvuz0wYVMEfwmMGhUHidpD8
        vJHZEZlWHhlzUUf/qhAZz1t7ZBoeUymMEDUCL2cNLEjThK1WUVgkyWxreC71Amso1lHwQ6903QhM
        j3wd57gum53wI5zHRligDwLIGyElaIKxlHz85ZrctMLC+U6pctjHAg65LmbEpnuHbQVQ7rB9HJeH
        igJiL6UJiLKiyjKW1hnUSTDTHKZSL/JLk1r6vayVOeD04AnQ27wdBHfELqvIBxztv5dcYFfpgWyg
        skqfc/QCv+s4kymH337D+RjCU+3h6XaER2NocbwvLDW90GK5uByzpAqzLC3KgCVZOXN0P5Waf+5J
        OiX4epbjZzBq0EjxvYIn0GwLJ2+Ebh8s1scVkD+skoLcjb4Ix23MkgfVo1v6jLS482ODPj4JLccn
        OXknjANgsVKugK6Sgnajb8L5G0OmRY9uSuODLFZGl2Fe11XM4irI49XMzsZUan7yhJ2NeOKmRAnV
        KWx/EAP5yEpD3gjkWZWDq+CQSXxJrm/unQf7CHvyDvgWXLz41/WSvbIenBraioFKVhrKv8NFQhJ3
        jkvCnjYI6SLFYhVdkFSrMCzCMCxxWjmzoptIzTeT+AdjtiumnVWommysqnaO7tUe0Eo+MIuLCQ/H
        7YTRUflqw5cRZxXWHhnfbIPIjuzVHtAqWodM7XFBwbk1OdYa3rstR0cNojznEEV5la2KcC4d9bnU
        fC2ExXTEdsVa4aoGqwff6L7C7lKrqh1wUh7IB1W50WavPOHkktwJKYxn159JR2WtcEXDERs5RrT0
        2LQ80BaxqTph026EXpRpBFXJGVtVEEIalmxmg2MqNV8RQTBtNP2uirgdtGMCb1q152ovyTuxbVrk
        YxlHn0eiKhrGndKAyyPd0A5miSqi9sDUjMC0OQE79jzSVNEuOqUBl0kc8GI975yXLK6TGlarMK9n
        9jemUi+wiGgdPC/nPiptG/Kz0sB8pJachMFlEATkQSvVuzJ6nDZ8N6HAwcN5s3+JwHSHwD4wS47/
        EwQBtQ7Y7ZKMo4Zvc4k94i5WQwRRWearVVGlQVHMbfZNpeaqIFwH+XRb4U5YM5TCNAJ7S1hU4+6O
        kC4x+tSI1qXzQG5xQlk1yD0if4jD/JhHnaOF7oSNvSWsq93mjnS5UH/CprXHdtYQh/kxc1pMEXFQ
        Qs3yVRhCzuOZ0WEq9SKnNJn//JH134b+D/jmkQ/YQDLkNTqeIyvvS+9jsxsRMXIFLW7QkDuwWp1V
        z6ndQdEOv8ZlpC0yB6gGizRsIZ/AWKUNKuev+KBjSxz3Gix0PTbOt6qtF0ubOGQsX/Eg5FGVzuXE
        TKXm20aSTG1jzIWQCGy8ZjAj2iJf/t2gLYbsG5yHPQF5Ay5wGqRtf1syed33yKDBRJdJe3bN54nB
        BpMjfAjaDMiaP1DwD0H5+BBI4P62bcLcQ7isl8nFUqqoCBNe1CXkRVoW9UzvNZV6gYbidfK8/vsF
        TiazaQV39XaYBuQTnr6zKJxQvG5B++z2GOwfge0A+3Tn6OSA7HlqEBfDRZgGtMcD9waiJGWI67zW
        Mdbvj7iLzSlSiKMyrliSVhDxeOacYiI1Xw1ptE6yf7B4uGc9TkgZ9sQrpbnLpdyQ6E5JsMipfMAE
        xx0BuTpzQnFcOkRUKvEd1w7VJVJuPNSNqNSeUGm54HCCFzmkqzzLGGdxUcysr6dSLzCCcB39sClO
        3uFA2vc5XKObvNHIpL+X5A2YXljA5hTy7MhNjUTeBXLYBmfSvqHhWtyUIyQePPeQ2JBCSAoecrHm
        U5kUYZ2WSRIGZRLOPPep1NxzD1z79YdlBHmr1R45e26Np1V7fLNvnkBiJHg92AZ5EbgD/VpKNUin
        Hdj7SvxltUSLLr5s1XaqiC3v3VItVEqqTlSulq4YRuzFiGNhWUJYrgqep5DDzD7rVGr+qx7F6zj6
        Z0nS7X9j7xtqXw64UfTAkUWM68xm5Gz/AljRWePCwtldPu/aDa2/Yusbal8RuEm0g3b7zEcKJcaH
        BsMxRoZF+3pFkMZRHnOI0yzOspnKmEq9oHQIp9Xb78roO9gynymRBy26Y0LkkiGXFR1fd6+NT1rV
        wi5SR3ewZWNiZLXojlmQy4BcKnSsnb0yeo+8WAhY5VEBnBdRXUQxn8lUmkrNd0VJPN3cuVW6wx6G
        ZXWNvuU1kiFYKxyH+9hGJZ8ddR6z2soCxwLCld7nKKF2wFi51TU2+dgJ+LtmKtWONy9GYCwiXOm9
        2Ei0gCheRUUapxGrYC6heCL1Ah2s1kn6z8zBs1fxLQdjPIf4uhEt18eElTwy6TY7r4YD+QDGuAn2
        zdcetHBzVew3ncP0di/6ERy9lQOn2Fz1W24+HTpaEaZOT7AcgSbMyjrP6qIMw7JKwnwuqfK51Hyd
        YJR+rpPHx5uRyYQThStoxEgj3VQgwZA3qho6kC4z3eDJm3H8YOtWfD3rKoY9jDwmHCyU35CNQ6b8
        GzI1DnmcQjjkxaq0VRJmrE7zOmRQzO23TqV+rIWHZrgkYfZNCxiyk3X6PFAgI2NjgXFxovEdl2zv
        BOctkBtm7Biw37GOGXc/EmjyBphtzhn/jEMGpGaMA1P8UUfaYJK1BzOOrVVlaZgvlp+mQZ6zNORx
        DHFQzs1PJ1Jzjz1aR8WUo3ENuFRQaoF+GC86Ao77bDj18ZvmnzQYM2g4rbdBJ3ACcSs0nHPo1ffA
        pQPGnTYc+vg9834EPq24eWDk9S2WI2VpCkFZVDULV0mZzOxwT6Xmv/pJuA7iyZZJy8k738f7vjBm
        Bl99YG70Jo2jjG16pnfkHcPi4Q107LzBJ2610cYh/644ZoZ2HpnaERnXsPSONohMuUNebPK2gqha
        lRAEAdRlNjM0T6VeoIV8Onn7Xdlw2mR25Zp3+LdCoyZEB2QjMPq+7rVoyWucDmM+dXX/x3N0cSwb
        vttjRmjfGkJoasVI78Yir1PSNkghqHH0U6q/Lnf5RVIHENRQxnmaRsXcyy+eS83Xxo+WPj8+CSSw
        jB7JMbpvDhiMmWXEOy1vK79T28ehavFDt/l/jjKkhx/9kmN1wwEjMrOMetflDeaoNemR/e7/YiEi
        CNOihjyGYlXn5czG3VRqfoiIi3W8+oF7whUH3G0zOFtw0dmtohirpKdNQtuSj+oJWl9F3ArpqE1X
        6nwHhesOuN5m6ND7kUJ5wvaDubalErF9IVF7bFqq5VxUuUqLMmYZK+p4tZo7/plKzddEEk5d1JeN
        a5bSt7hxMFLtyabX7p7IW7w3zyq3Z/JB7ZGrgRO6PUh7IL8AO+caveyVq4y3Dd0i9JFpbzw0rfHS
        PKtc6to6aDegc9D0gNCLBeyqrPNVhTlnEAZzm6lTqfnOKczX4fNW9sdfrq9ODSa8E8Qy7Yc5WRCM
        BbYzEscHwIsNoWfa6eOLRJWx9qzcSR6qEhtMI7Qf4WRBMJbYzkYcIwBvNXTQqI9BospYu1j2lKQ1
        z9IiZEVdp1Uwk3Y/lXqBMtIf3Fc13krliEldJ7baB27cjmOScXZanEae5ckw/GU9Z9nE8U4qR0z6
        hoxnXTlk2ntkR7E82YW/q2cxk0iyMApZXadBHCV1NvNinqnUy+qI52NQdPTGh4H7usbUdby7k3wG
        1/0nt8C/TT3Xx+0T8qhZf5YSHGNsrNus4uxAudrjWih1zfCh51jeLdfizvIqZBleZsR4PPdK1YnU
        CwJBuo6TaSDAe0bwpR7nz28xiZfYLCA3Xd+qAziCxqdWuQbSz7iai1LCit9AnhkJKo99ZONtT9gU
        Rmx8z/tWubvBdriai1Iee7E8NYoqgKDM6yJi2ewLqSZSL3jv86n3wYsvjrOFeuzo3TFO7tjXsYen
        9Pjxn1VXCqwakEV36M14rwC5RaPwxbbn55+jHXdrxjhwqGnHOO3YV6o0/c2hU3ZCpzVO/32t7bn5
        E83837/9PwAAAP//AwCUFTALXFsAAA==
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8d46253e7fd558ba-IAD
      Cache-Control:
      - no-store, no-cache, must-revalidate, max-age=0
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Fri, 18 Oct 2024 05:32:01 GMT
      NEL:
      - '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}'
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=1jncDmrg1XfuP0IcLzSdFqxnDMUXcq%2FYLEq7Y47JCzrfHGKMIYKTdVDqO5wAJ1CDVG8lCorUMgO5Aw5pGQ4cBY8eGoB%2FI0iTPcN%2FHHf9%2Bz3zMUu9I%2FdQYPbfHmI%3D"}],"group":"cf-nel","max_age":604800}'
      Server:
      - RapidAPI-1.2.8
      Strict-Transport-Security:
      - max-age=31556926; includeSubDomains
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding, Cookie
      X-RapidAPI-Region:
      - AWS - us-east-1
      X-RapidAPI-Request-Id:
      - c9f0a6fb613c5c0a2361b3f48bba6dcc7bc83feaa827675cb22eb492b1669872
      X-RapidAPI-Version:
      - 1.2.8
      X-RateLimit-Requests-Limit:
      - '2000'
      X-RateLimit-Requests-Remaining:
      - '1913'
      X-RateLimit-Requests-Reset:
      - '1620568'
      X-RateLimit-rapid-free-plans-hard-limit-Limit:
      - '500000'
      X-RateLimit-rapid-free-plans-hard-limit-Remaining:
      - '499960'
      X-RateLimit-rapid-free-plans-hard-limit-Reset:
      - '1620568'
      alt-svc:
      - h3=":443"; ma=86400
      permissions-policy:
      - browsing-topics=()
      referrer-policy:
      - strict-origin-when-cross-origin
      rndr-id:
      - 8911030f-1e15-4bb9
      x-content-type-options:
      - nosniff
      x-render-origin-server:
      - gunicorn
    status:
      code: 200
      message: OK
version: 1
