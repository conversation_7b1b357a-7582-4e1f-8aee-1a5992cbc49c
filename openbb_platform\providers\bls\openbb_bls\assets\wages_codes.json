{"ci": {"owner_code": {"1": "Civilian workers", "2": "Private industry workers", "3": "State and local government workers"}, "industry_code": {"000000": "All industries", "220000": "Utilities", "230000": "Construction", "300000": "Manufacturing", "310000": "Nondurable goods manufacturers", "320000": "Durable goods manufacturers", "336411": "Aircraft manufacturing", "400000": "Trade, transportation, and utilities", "412000": "Retail trade", "420000": "Wholesale trade", "430000": "Transportation and warehousing", "510000": "Information", "520000": "Finance and insurance", "520A00": "Financial activities", "522000": "Credit intermediation", "524000": "Insurance carriers", "530000": "Real estate and rental and leasing", "540000": "Professional, scientific, and technical services", "540A00": "Professional and business services", "560000": "Administrative and support and waste management and remediation services", "600000": "Education and health services", "610000": "Educational services", "610500": "Schools", "611100": "Elementary and secondary schools", "612000": "Junior colleges, colleges, universities, and professional schools", "620000": "Health care and social assistance", "622000": "Hospitals", "623000": "Nursing and residential care facilities", "623100": "Nursing care facilities", "700000": "Leisure and hospitality", "720000": "Accommodation and food services", "810000": "Other services (except public administration)", "920000": "Public administration", "DISCON": "Discontinued codes", "G00000": "Goods-producing", "S00000": "Service-providing"}, "occupation_code": {"000000": "All occupations", "000001": "All occupations, excluding sales", "111300": "Management, business, and financial occupations", "112900": "Management, professional, and related occupations", "114300": "All white-collar occupations", "114301": "All white-collar occupations, excluding sales", "152900": "Professional and related occupations", "313900": "Service occupations", "410000": "Sales and related occupations", "414300": "Sales and office occupations", "430000": "Office and administrative support occupations", "43000D": "Office and administrative support occupations (SOC 2010)", "454700": "Construction, and extraction, farming, fishing, and forestry occupations", "454900": "Natural resources, construction, and maintenance occupations", "455300": "All-blue collar occupations", "490000": "Installation, maintenance, and repair occupations", "510000": "Production occupations", "515300": "Production, transportation, and material moving occupations", "530000": "Transportation and material moving occupations", "53000D": "Transportation and material moving occupations (SOC 2010)", "DISCON": "Discontinued Codes"}, "subcell_code": {"00": "All workers", "23": "Union", "24": "Nonunion", "27": "Time-based pay", "AA": "Establishment Size", "AB": "Region and Division", "AC": "Metropolitan Statistical Areas", "AD": "Bargaining Status", "AE": "Full-time and Part-time Work Status", "AF": "Time and Incentive Status", "AG": "Average Wage", "AH": "Civilian Wage Percentiles", "AI": "Private Wage Percentiles", "AJ": "Government Wage Percentiles", "AK": "Plan Sponsor"}, "area_code": {"00122": "Atlanta-Athens-Clarke County-Sandy Springs, GA CSA", "00148": "Boston-Worcester-Providence, MA-RI-NH-CT CSA", "00176": "Chicago-Naperville, IL-IN-WI CSA", "00206": "Dallas-Fort Worth, TX-OK CSA", "00220": "Detroit-Warren-Ann Arbor, MI CSA", "00288": "Houston-The Woodlands, TX CSA", "00348": "Los Angeles-Long Beach, CA CSA", "00378": "Minneapolis-St. Paul, MN-WI CSA", "00408": "New York-Newark, NY-NJ-CT-PA CSA", "00428": "Philadelphia-Reading-Camden, PA-NJ-DE-MD CSA", "00488": "San Jose-San Francisco-Oakland, CA CSA", "00500": "Seattle-Tacoma, WA CSA", "00548": "Washington-Baltimore-Arlington, DC-MD-VA-WV-PA CSA", "33100": "Miami-Fort Lauderdale-Port St. Lucie, FL CSA", "38060": "Phoenix-Mesa-Scottsdale, AZ MSA", "98100": "Northeast census region", "98200": "South census region", "98300": "Midwest census region", "98400": "West census region", "98999": "Regions, divisions, and statistical areas", "99100": "New England census division", "99120": "Middle Atlantic census division", "99130": "East South Central census division", "99140": "South Atlantic census division", "99150": "East North Central census division", "99160": "West North Central census division", "99170": "West South Central census division", "99180": "Mountain census division", "99190": "Pacific census division", "99200": "Metropolitan statistical areas", "99210": "Metropolitan", "99220": "Nonmetropolitan", "99999": "United States (National)"}, "estimate_code": {"01": "Total compensation", "02": "Wages and salaries", "03": "Total benefits", "15": "Health insurance"}, "footnote_code": {"2": "See Footnote 2 on <a href=\"/eci/overview/eci-labstat-footnotes.htm#2\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "3": "See Footnote 3 on <a href=\"/eci/overview/eci-labstat-footnotes.htm#3\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "8": "See Footnote 8 on <a href=\"/eci/overview/eci-labstat-footnotes.htm#8\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "9": "See Footnote 9 on <a href=\"/eci/overview/eci-labstat-footnotes.htm#9\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "A": "Dashes indicate data not available.", "B": "Includes wages, salaries, and employer costs for employee benefits.", "C": "See Footnote C on <a href=\"/eci/overview/eci-labstat-footnotes.htm#C\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "D": "See Footnote D on <a href=\"/eci/overview/eci-labstat-footnotes.htm#D\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "E": "See Footnote E on <a href=\"/eci/overview/eci-labstat-footnotes.htm#E\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "F": "See Footnote F on <a href=\"/eci/overview/eci-labstat-footnotes.htm#F\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "G": "Includes mining, construction, and manufacturing.", "H": "See Footnote H on <a href=\"/eci/overview/eci-labstat-footnotes.htm#H\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "J": "See Footnote J on <a href=\"/eci/overview/eci-labstat-footnotes.htm#J\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "K": "Includes ambulatory health services and social assistance, not shown separately.", "N": "Includes farming, fishing, and forestry occupations.", "O": "See Footnote O on <a href=\"/eci/overview/eci-labstat-footnotes.htm#O\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "Q": "The index for this series is not strictly comparable to other series in this family.", "S": "Historical data are available beginning with December 2005.", "X": "See Footnote X on <a href=\"/eci/overview/eci-labstat-footnotes.htm#X\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "Y": "See Footnote Y on <a href=\"/eci/overview/eci-labstat-footnotes.htm#Y\" target=\"new\">www.bls.gov/eci/overview/eci-labstat-footnotes.htm</a>.", "Z": "Series discontinued beginning December 2008."}}, "wm": {"area_code": {"0000000": "National", "0010180": "Abilene, TX", "0010420": "Akron, OH", "0010500": "Albany, GA", "0010540": "Albany, OR", "0010580": "Albany-Schenectady-Troy, NY", "0010740": "Albuquerque, NM", "0010780": "Alexandria, LA", "0010900": "Allentown-Bethlehem-Easton, PA-NJ", "0011020": "Altoona, PA", "0011100": "Amarillo, TX", "0011180": "Ames, IA", "0011260": "Anchorage, AK", "0011460": "Ann Arbor, MI", "0011500": "<PERSON><PERSON><PERSON>-Oxford-Jacksonville, AL", "0011540": "Appleton, WI", "0011700": "Asheville, NC", "0012020": "Athens-Clarke County, GA", "0012060": "Atlanta-Sandy Springs-Roswell, GA", "0012100": "Atlantic City-Hammonton, NJ", "0012220": "Auburn-<PERSON>eli<PERSON>, AL", "0012260": "Augusta-Richmond County, GA-SC", "0012420": "Austin-Round Rock, TX", "0012540": "Bakersfield, CA", "0012580": "Baltimore-Columbia-Towson, MD", "0012940": "Baton Rouge, LA", "0012980": "Battle Creek, MI", "0013020": "Bay City, MI", "0013140": "Beaumont-Port Arthur, TX", "0013220": "Beckley, WV", "0013380": "Bellingham, WA", "0013460": "Bend-Redmond, OR", "0013740": "Billings, MT", "0013780": "Binghamton, NY", "0013820": "Birmingham-Hoover, AL", "0013900": "Bismarck, ND", "0013980": "Blacksburg-Christiansburg-Radford, VA", "0014010": "Bloomington, IL", "0014020": "Bloomington, IN", "0014100": "Bloomsburg-Berwick, PA", "0014260": "Boise City, ID", "0014500": "Boulder, CO", "0014540": "Bowling Green, KY", "0014740": "Bremerton-Silverdale, WA", "0015180": "Brownsville-Harlingen, TX", "0015260": "Brunswick, GA", "0015380": "Buffalo-Cheektowaga-Niagara Falls, NY", "0015500": "Burlington, NC", "0015680": "California-Lexington Park, MD", "0015940": "Canton-Massillon, OH", "0015980": "Cape Coral-Fort Myers, FL", "0016020": "Cape Girardeau, MO-IL", "0016060": "Carbondale-Marion, IL", "0016180": "Carson City, NV", "0016220": "Casper, WY", "0016300": "Cedar Rapids, IA", "0016540": "Chambersburg-Waynesboro, PA", "0016580": "Champaign-Urbana, IL", "0016620": "Charleston, WV", "0016700": "Charleston-North Charleston, SC", "0016740": "Charlotte-Concord-Gastonia, NC-SC", "0016820": "Charlottesville, VA", "0016860": "Chattanooga, TN-GA", "0016940": "Cheyenne, WY", "0016980": "Chicago-Naperville-Elgin, IL-IN-WI", "0017020": "Chico, CA", "0017140": "Cincinnati, OH-KY-IN", "0017300": "Clarksville, TN-KY", "0017420": "Cleveland, TN", "0017460": "Cleveland-Elyria, OH", "0017660": "<PERSON><PERSON>, ID", "0017780": "College Station-Bryan, TX", "0017820": "Colorado Springs, CO", "0017860": "Columbia, MO", "0017900": "Columbia, SC", "0017980": "Columbus, GA-AL", "0018020": "Columbus, IN", "0018140": "Columbus, OH", "0018580": "Corpus Christi, TX", "0018700": "Corvallis, OR", "0018880": "Crestview-Fort Walton Beach-Destin, FL", "0019060": "Cumberland, MD-WV", "0019100": "Dallas-Fort Worth-Arlington, TX", "0019140": "Dalton, GA", "0019180": "Danville, IL", "0019300": "Daphne<PERSON><PERSON><PERSON><PERSON>-<PERSON>, AL", "0019340": "Davenport-Moline-Rock Island, IA-IL", "0019380": "Dayton, OH", "0019460": "Decatur, AL", "0019500": "Decatur, IL", "0019660": "Deltona-Daytona Beach-Ormond Beach, FL", "0019740": "Denver-Aurora-Lakewood, CO", "0019780": "Des Moines-West Des Moines, IA", "0019820": "Detroit-Warren-Dearborn, MI", "0020020": "<PERSON><PERSON>, AL", "0020100": "Dover, DE", "0020220": "Dubuque, IA", "0020260": "Duluth, MN-WI", "0020500": "Durham-Chapel Hill, NC", "0020700": "East Stroudsburg, PA", "0020740": "Eau Claire, WI", "0020940": "El Centro, CA", "0021060": "Elizabethtown-Fort Knox, KY", "0021140": "Elkhart-Goshen, IN", "0021300": "Elmira, NY", "0021340": "El Paso, TX", "0021420": "<PERSON>id, OK", "0021500": "Erie, PA", "0021660": "Eugene, OR", "0021780": "Evansville, IN-KY", "0021820": "Fairbanks, AK", "0022020": "Fargo, ND-MN", "0022140": "Farmington, NM", "0022180": "Fayetteville, NC", "0022220": "Fayetteville-Springdale-Rogers, AR-MO", "0022380": "Flagstaff, AZ", "0022420": "Flint, MI", "0022500": "Florence, SC", "0022520": "Florence-<PERSON><PERSON><PERSON>, AL", "0022540": "Fond du Lac, WI", "0022660": "Fort <PERSON>, CO", "0022900": "Fort Smith, AR-OK", "0023060": "Fort Wayne, IN", "0023420": "Fresno, CA", "0023460": "Gadsden, AL", "0023540": "Gainesville, FL", "0023580": "Gainesville, GA", "0023900": "Gettysburg, PA", "0024020": "Glens Falls, NY", "0024140": "Goldsboro, NC", "0024220": "Grand Forks, ND-MN", "0024260": "Grand Island, NE", "0024300": "Grand Junction, CO", "0024340": "Grand Rapids-Wyoming, MI", "0024420": "Grants Pass, OR", "0024500": "Great Falls, MT", "0024540": "<PERSON><PERSON><PERSON>, CO", "0024580": "Green Bay, WI", "0024660": "Greensboro-High Point, NC", "0024780": "Greenville, NC", "0024860": "Greenville-Anderson<PERSON><PERSON>, SC", "0025060": "Gulfport-Biloxi-Pascagoula, MS", "0025180": "Hagerstown-Martinsburg, MD-WV", "0025220": "Hammond, LA", "0025260": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CA", "0025420": "Harrisburg-Carlisle, PA", "0025500": "Harrisonburg, VA", "0025620": "Hattiesburg, MS", "0025860": "Hickory-Lenoir-Morganton, NC", "0025940": "Hilton Head Island-Bluffton-Beaufort, SC", "0025980": "Hinesville, GA", "0026140": "Homosassa Springs, FL", "0026300": "Hot Springs, AR", "0026380": "Houma-Thibodaux, LA", "0026420": "Houston-The Woodlands-Sugar Land, TX", "0026580": "Huntington-Ashland, WV-KY-OH", "0026620": "Huntsville, AL", "0026820": "Idaho Falls, ID", "0026900": "Indianapolis-Carmel-Anderson, IN", "0026980": "Iowa City, IA", "0027060": "Ithaca, NY", "0027100": "Jackson, MI", "0027140": "<PERSON>, MS", "0027180": "Jackson, TN", "0027260": "Jacksonville, FL", "0027340": "Jacksonville, NC", "0027500": "Janesville-Beloit, WI", "0027620": "Jefferson City, MO", "0027740": "Johnson City, TN", "0027780": "Johnstown, PA", "0027860": "Jonesboro, AR", "0027900": "<PERSON><PERSON><PERSON>, MO", "0027980": "Kahului-Wail<PERSON>-Lahai<PERSON>, HI", "0028020": "Kalamazoo-Portage, MI", "0028100": "Kankakee, IL", "0028140": "Kansas City, MO-KS", "0028420": "Kennewick-Richland, WA", "0028660": "Killeen-Temple, TX", "0028700": "Kingsport-Bristol-Bristol, TN-VA", "0028740": "Kingston, NY", "0028940": "Knoxville, TN", "0029020": "Kokomo, IN", "0029100": "La Crosse-Onalaska, WI-MN", "0029180": "Lafayette, LA", "0029200": "Lafayette-West Lafayette, IN", "0029340": "Lake Charles, LA", "0029420": "Lake Havasu City-Kingman, AZ", "0029460": "Lakeland-Winter Haven, FL", "0029540": "Lancaster, PA", "0029620": "Lansing-East Lansing, MI", "0029700": "Laredo, TX", "0029740": "Las Cruces, NM", "0029820": "Las Vegas-Henderson-Paradise, NV", "0029940": "Lawrence, KS", "0030020": "Lawton, OK", "0030140": "Lebanon, PA", "0030300": "Lewiston, ID-WA", "0030460": "Lexington-Fayette, KY", "0030620": "Lima, OH", "0030700": "Lincoln, NE", "0030780": "Little Rock-North Little Rock-Conway, AR", "0030860": "Logan, UT-ID", "0030980": "Longview, TX", "0031020": "Longview, WA", "0031080": "Los Angeles-Long Beach-Anaheim, CA", "0031140": "Louisville/Jefferson County, KY-IN", "0031180": "Lubbock, TX", "0031340": "Lynchburg, VA", "0031420": "Macon, GA", "0031460": "Madera, CA", "0031540": "Madison, WI", "0031740": "Manhattan, KS", "0031860": "Mankato-North Mankato, MN", "0031900": "Mansfield, OH", "0032580": "McAllen-Edinburg-Mission, TX", "0032780": "Medford, OR", "0032820": "Memphis, TN-MS-AR", "0032900": "Merced, CA", "0033100": "Miami-Fort Lauderdale-West Palm Beach, FL", "0033140": "Michigan City-La Porte, IN", "0033220": "Midland, MI", "0033260": "Midland, TX", "0033340": "Milwaukee-Waukesha-West Allis, WI", "0033460": "Minneapolis-St<PERSON> Paul-Bloomington, MN-WI", "0033540": "Missoula, MT", "0033660": "Mobile, AL", "0033700": "Modesto, CA", "0033740": "Monroe, LA", "0033780": "Monroe, MI", "0033860": "Montgomery, AL", "0034060": "Morgantown, WV", "0034100": "Morristown, TN", "0034580": "Mount Vernon-Anacortes, WA", "0034620": "Muncie, IN", "0034740": "Muskegon, MI", "0034820": "Myrtle Beach-Conway-North Myrtle Beach, SC-NC", "0034900": "Napa, CA", "0034940": "Naples-Immokalee-Marco Island, FL", "0034980": "Nashville-Davidson--Mu<PERSON><PERSON>sboro--<PERSON>, TN", "0035100": "New Bern, NC", "0035380": "New Orleans-Metairie, LA", "0035620": "New York-Newark-Jersey City, NY-NJ-PA", "0035660": "Niles-Benton Harbor, MI", "0035840": "North Port-Sarasota-Bradenton, FL", "0036100": "Ocala, FL", "0036140": "Ocean City, NJ", "0036220": "Odessa, TX", "0036260": "Ogden-Clearfield, UT", "0036420": "Oklahoma City, OK", "0036500": "Olympia-Tumwater, WA", "0036540": "Omaha-Council Bluffs, NE-IA", "0036740": "Orlando-Kissimmee-Sanford, FL", "0036780": "Oshkosh-Neenah, WI", "0036980": "Owensboro, KY", "0037100": "Oxnard-Thousand Oaks-Ventura, CA", "0037340": "Palm Bay-Melbourne-Titusville, FL", "0037460": "Panama City, FL", "0037620": "Parkersburg-Vienna, WV", "0037860": "Pensacola-Ferry Pass-Brent, FL", "0037900": "Peoria, IL", "0037980": "Philadelphia-Camden-Wilmington, PA-NJ-DE-MD", "0038060": "Phoenix-Mesa-Scottsdale, AZ", "0038220": "Pine Bluff, AR", "0038300": "Pittsburgh, PA", "0038540": "Pocatello, ID", "0038900": "Portland-Vancouver-Hillsboro, OR-WA", "0038940": "Port St. Lucie, FL", "0039140": "Prescott, AZ", "0039340": "Provo-Orem, UT", "0039380": "Pueblo, CO", "0039460": "Punta Gorda, FL", "0039540": "Racine, WI", "0039580": "Raleigh, NC", "0039660": "Rapid City, SD", "0039740": "Reading, PA", "0039820": "Redding, CA", "0039900": "Reno, NV", "0040060": "Richmond, VA", "0040140": "Riverside-San Bernardino-Ontario, CA", "0040220": "Roanoke, VA", "0040340": "Rochester, MN", "0040380": "Rochester, NY", "0040420": "Rockford, IL", "0040580": "Rocky Mount, NC", "0040660": "Rome, GA", "0040900": "Sacramento--Roseville--Arden-Arcade, CA", "0040980": "Saginaw, MI", "0041060": "St. Cloud, MN", "0041100": "St. George, UT", "0041140": "St. Joseph, MO-KS", "0041180": "St. Louis, MO-IL", "0041420": "Salem, OR", "0041500": "Salinas, CA", "0041540": "Salisbury, MD-DE", "0041620": "Salt Lake City, UT", "0041660": "San Angelo, TX", "0041700": "San Antonio-New Braunfels, TX", "0041740": "San Diego-Carlsbad, CA", "0041860": "San Francisco-Oakland-Hayward, CA", "0041940": "San Jose-Sunnyvale-Santa Clara, CA", "0042020": "San Luis Obispo-Paso Robles-Arroyo Grande, CA", "0042100": "Santa Cruz-Watsonville, CA", "0042140": "Santa Fe, NM", "0042200": "Santa Maria-Santa Barbara, CA", "0042220": "Santa Rosa, CA", "0042340": "Savannah, GA", "0042540": "Scranton--<PERSON>-<PERSON><PERSON>--Hazleton, PA", "0042660": "Seattle-Tacoma-Bellevue, WA", "0042680": "Sebastian-Vero Beach, FL", "0042700": "Sebring, FL", "0043100": "Sheboygan, WI", "0043300": "Sherman-Denison, TX", "0043340": "Shreveport-Bossier City, LA", "0043420": "Sierra Vista-Douglas, AZ", "0043580": "Sioux City, IA-NE-SD", "0043620": "Sioux Falls, SD", "0043780": "South Bend-Mishawaka, IN-MI", "0043900": "Spartanburg, SC", "0044060": "Spokane-Spokane Valley, WA", "0044100": "Springfield, IL", "0044180": "Springfield, MO", "0044220": "Springfield, OH", "0044300": "State College, PA", "0044420": "Staunton-Waynesboro, VA", "0044700": "Stockton-Lodi, CA", "0044940": "<PERSON><PERSON><PERSON>, SC", "0045060": "Syracuse, NY", "0045220": "Tallahassee, FL", "0045300": "Tampa-St. Petersburg-Clearwater, FL", "0045460": "Terre Haute, IN", "0045500": "Texarkana, TX-AR", "0045540": "The Villages, FL", "0045780": "Toledo, OH", "0045820": "Topeka, KS", "0045940": "Trenton, NJ", "0046060": "Tucson, AZ", "0046140": "Tulsa, OK", "0046220": "Tuscaloosa, AL", "0046300": "Twin Falls, ID", "0046340": "Tyler, TX", "0046520": "Urban Honolulu, HI", "0046540": "Utica-Rome, NY", "0046660": "Valdosta, GA", "0046700": "Vallejo-Fairfield, CA", "0047020": "Victoria, TX", "0047220": "Vineland-Bridgeton, NJ", "0047260": "Virginia Beach-Norfolk-Newport News, VA-NC", "0047300": "Visalia-Porterville, CA", "0047380": "Waco, TX", "0047460": "Walla Wall<PERSON>, WA", "0047580": "<PERSON>, GA", "0047900": "Washington-Arlington-Alexandria, DC-VA-MD-WV", "0047940": "Waterloo-Cedar Falls, IA", "0048060": "Watertown-Fort Drum, NY", "0048140": "Wausau, WI", "0048260": "Weirton-Steubenville, WV-OH", "0048300": "Wenatchee, WA", "0048540": "Wheeling, WV-OH", "0048620": "Wichita, KS", "0048660": "Wichita Falls, TX", "0048700": "Williamsport, PA", "0048900": "Wilmington, NC", "0049020": "Winchester, VA-WV", "0049180": "Winston-Salem, NC", "0049420": "Yakima, WA", "0049620": "York-Hanover, PA", "0049660": "Youngstown-Warren-Boardman, OH-PA", "0049700": "Yuba City, CA", "0049740": "Yuma, AZ", "0070750": "Bangor, ME", "0070900": "Barnstable Town, MA", "0071650": "Boston-Cambridge-Nashua, MA-NH", "0071950": "Bridgeport-Stamford-Norwalk, CT", "0072400": "Burlington-South Burlington, VT", "0072850": "Danbury, CT", "0073050": "Dover-Durham, NH-ME", "0073450": "Hartford-West Hartford-East Hartford, CT", "0074500": "<PERSON><PERSON><PERSON>, MA", "0074650": "<PERSON>ton-Auburn, ME", "0074950": "Manchester, NH", "0075550": "New Bedford, MA", "0075700": "New Haven, CT", "0076450": "Norwich-New London-Westerly, CT-RI", "0076600": "Pittsfield, MA", "0076750": "Portland-South Portland, ME", "0076900": "Portsmouth, NH-ME", "0077200": "Providence-Warwick, RI-MA", "0078100": "Springfield, MA-CT", "0078700": "Waterbury, CT", "0079600": "Worcester, MA-CT", "0100000": "Alabama", "0100001": "Northwest Alabama nonmetropolitan area", "0100002": "Northeast Alabama nonmetropolitan area", "0100003": "Southwest Alabama nonmetropolitan area", "0100004": "Southeast Alabama nonmetropolitan area", "0200000": "Alaska", "0200006": "Alaska nonmetropolitan area", "0400000": "Arizona", "0400001": "Arizona nonmetropolitan area", "0500000": "Arkansas", "0500001": "North Arkansas nonmetropolitan area", "0500002": "East Arkansas nonmetropolitan area", "0500003": "West Arkansas nonmetropolitan area", "0500004": "South Arkansas nonmetropolitan area", "0600000": "California", "0600003": "North Coast Region of California nonmetropolitan area", "0600006": "Eastern Sierra-Mother Lode Region of California nonmetropolitan area", "0600007": "North Valley-Northern Mountains Region of California nonmetropolitan area", "0800000": "Colorado", "0800001": "Eastern and Southern Colorado nonmetropolitan area", "0800002": "Southwest Colorado nonmetropolitan area", "0800003": "Northwest Colorado nonmetropolitan area", "0900000": "Connecticut", "0900001": "Connecticut nonmetropolitan area", "1000000": "Delaware", "1100000": "District of Columbia", "1200000": "Florida", "1200003": "South Florida nonmetropolitan area", "1200006": "North Florida nonmetropolitan area", "1300000": "Georgia", "1300001": "North Georgia nonmetropolitan area", "1300002": "Middle Georgia nonmetropolitan area", "1300003": "East Georgia nonmetropolitan area", "1300004": "South Georgia nonmetropolitan area", "1500000": "Hawaii", "1500001": "Hawaii / Kauai nonmetropolitan area", "1600000": "Idaho", "1600006": "Northwestern Idaho nonmetropolitan area", "1600007": "Southeast-Central Idaho nonmetropolitan area", "1700000": "Illinois", "1700001": "Northwest Illinois nonmetropolitan area", "1700002": "West Central Illinois nonmetropolitan area", "1700003": "East Central Illinois nonmetropolitan area", "1700004": "South Illinois nonmetropolitan area", "1800000": "Indiana", "1800001": "Northern Indiana nonmetropolitan area", "1800002": "Central Indiana nonmetropolitan area", "1800003": "Southern Indiana nonmetropolitan area", "1900000": "Iowa", "1900001": "Northeast Iowa nonmetropolitan area", "1900002": "Northwest Iowa nonmetropolitan area", "1900003": "Southwest Iowa nonmetropolitan area", "1900004": "Southeast Iowa nonmetropolitan area", "2000000": "Kansas", "2000006": "Kansas nonmetropolitan area", "2100000": "Kentucky", "2100001": "West Kentucky nonmetropolitan area", "2100002": "South Central Kentucky nonmetropolitan area", "2100003": "Central Kentucky nonmetropolitan area", "2100004": "East Kentucky nonmetropolitan area", "2200000": "Louisiana", "2200002": "Central Louisiana nonmetropolitan area", "2200003": "Northeast Louisiana nonmetropolitan area", "2200006": "Southwest Louisiana nonmetropolitan area", "2300000": "Maine", "2300001": "Northeast Maine nonmetropolitan area", "2300002": "Southwest Maine nonmetropolitan area", "2400000": "Maryland", "2400006": "Maryland nonmetropolitan area", "2500000": "Massachusetts", "2500006": "Massachusetts nonmetropolitan area", "2600000": "Michigan", "2600001": "Upper Peninsula of Michigan nonmetropolitan area", "2600002": "Northeast Lower Peninsula of Michigan nonmetropolitan area", "2600003": "Northwest Lower Peninsula of Michigan nonmetropolitan area", "2600004": "Balance of Lower Peninsula of Michigan nonmetropolitan area", "2700000": "Minnesota", "2700001": "Northwest Minnesota nonmetropolitan area", "2700002": "Northeast Minnesota nonmetropolitan area", "2700003": "Southwest Minnesota nonmetropolitan area", "2700004": "Southeast Minnesota nonmetropolitan area", "2800000": "Mississippi", "2800001": "Northeast Mississippi nonmetropolitan area", "2800002": "Northwest Mississippi nonmetropolitan area", "2800003": "Southeast Mississippi nonmetropolitan area", "2800004": "Southwest Mississippi nonmetropolitan area", "2900000": "Missouri", "2900001": "Central Missouri nonmetropolitan area", "2900002": "North Missouri nonmetropolitan area", "2900003": "Southeast Missouri nonmetropolitan area", "2900004": "Southwest Missouri nonmetropolitan area", "3000000": "Montana", "3000003": "Southwest Montana nonmetropolitan area", "3000004": "West Montana nonmetropolitan area", "3000006": "East-Central Montana nonmetropolitan area", "3100000": "Nebraska", "3100001": "Northwest Nebraska nonmetropolitan area", "3100003": "Northeast Nebraska nonmetropolitan area", "3100006": "South Nebraska nonmetropolitan area", "3200000": "Nevada", "3200006": "Nevada nonmetropolitan area", "3300000": "New Hampshire", "3300001": "Northern New Hampshire nonmetropolitan area", "3300002": "Central New Hampshire nonmetropolitan area", "3300006": "West Central-Southwest New Hampshire nonmetropolitan area", "3400000": "New Jersey", "3500000": "New Mexico", "3500006": "Northern New Mexico nonmetropolitan area", "3500007": "Eastern New Mexico nonmetropolitan area", "3600000": "New York", "3600001": "Capital/Northern New York nonmetropolitan area", "3600004": "Southwest New York nonmetropolitan area", "3600006": "Central East New York nonmetropolitan area", "3700000": "North Carolina", "3700001": "Southeast Coastal North Carolina nonmetropolitan area", "3700002": "Northeast Coastal North Carolina nonmetropolitan area", "3700003": "Piedmont North Carolina nonmetropolitan area", "3700004": "Mountain North Carolina nonmetropolitan area", "3800000": "North Dakota", "3800006": "West North Dakota nonmetropolitan area", "3800007": "East North Dakota nonmetropolitan area", "3900000": "Ohio", "3900001": "West Northwestern Ohio nonmetropolitan area", "3900002": "North Northeastern Ohio nonmetropolitan area", "3900003": "Eastern Ohio nonmetropolitan area", "3900004": "Southern Ohio nonmetropolitan area", "4000000": "Oklahoma", "4000001": "Northeast Oklahoma nonmetropolitan area", "4000002": "Northwest Oklahoma nonmetropolitan area", "4000003": "Southwest Oklahoma nonmetropolitan area", "4000004": "Southeast Oklahoma nonmetropolitan area", "4100000": "Oregon", "4100006": "Coast Oregon nonmetropolitan area", "4100007": "Central Oregon nonmetropolitan area", "4100008": "Eastern Oregon nonmetropolitan area", "4200000": "Pennsylvania", "4200001": "Western Pennsylvania nonmetropolitan area", "4200002": "Northern Pennsylvania nonmetropolitan area", "4200003": "Southern Pennsylvania nonmetropolitan area", "4400000": "Rhode Island", "4500000": "South Carolina", "4500002": "Upper Savannah South Carolina nonmetropolitan area", "4500004": "Lower Savannah South Carolina nonmetropolitan area", "4500006": "Northeast South Carolina nonmetropolitan area", "4600000": "South Dakota", "4600002": "East South Dakota nonmetropolitan area", "4600003": "West South Dakota nonmetropolitan area", "4700000": "Tennessee", "4700001": "West Tennessee nonmetropolitan area", "4700002": "South Central Tennessee nonmetropolitan area", "4700003": "North Central Tennessee nonmetropolitan area", "4700004": "East Tennessee nonmetropolitan area", "4800000": "Texas", "4800001": "West Texas Region of Texas nonmetropolitan area", "4800002": "North Texas Region of Texas nonmetropolitan area", "4800003": "Big Thicket Region of Texas nonmetropolitan area", "4800004": "Hill Country Region of Texas nonmetropolitan area", "4800005": "Border Region of Texas nonmetropolitan area", "4800006": "Coastal Plains Region of Texas nonmetropolitan area", "4900000": "Utah", "4900006": "Eastern Utah nonmetropolitan area", "4900007": "Central Utah nonmetropolitan area", "5000000": "Vermont", "5000001": "Southern Vermont nonmetropolitan area", "5000002": "Northern Vermont nonmetropolitan area", "5100000": "Virginia", "5100001": "Southwest Virginia nonmetropolitan area", "5100002": "Southside Virginia nonmetropolitan area", "5100003": "Northeast Virginia nonmetropolitan area", "5100004": "Northwest Virginia nonmetropolitan area", "5300000": "Washington", "5300006": "Western Washington nonmetropolitan area", "5300007": "Eastern Washington nonmetropolitan area", "5400000": "West Virginia", "5400001": "Southern West Virginia nonmetropolitan area", "5400002": "Northern West Virginia nonmetropolitan area", "5500000": "Wisconsin", "5500001": "Northwestern Wisconsin nonmetropolitan area", "5500002": "Northeastern Wisconsin nonmetropolitan area", "5500003": "South Central Wisconsin nonmetropolitan area", "5500004": "Western Wisconsin nonmetropolitan area", "5600000": "Wyoming", "5600006": "Western Wyoming nonmetropolitan area", "5600007": "Eastern Wyoming nonmetropolitan area"}, "ownership_code": {"1": "Civilian workers"}, "estimate_code": {"02": "Average Hourly Wage"}, "industry_code": {"000000": "All industries"}, "occupation_code": {"110000": "Management Occupations", "111011": "Chief Executives", "111021": "General and Operations Managers", "112011": "Advertising and Promotions Managers", "112021": "Marketing Managers", "112022": "Sales Managers", "112032": "Public Relations Managers", "112033": "Fundraising Managers", "113012": "Administrative Services Managers", "113013": "Facilities Managers", "113021": "Computer and Information Systems Managers", "113031": "Financial Managers", "113051": "Industrial Production Managers", "113061": "Purchasing Managers", "113071": "Transportation, Storage, and Distribution Managers", "113111": "Compensation and Benefits Managers", "113121": "Human Resources Managers", "113131": "Training and Development Managers", "119021": "Construction Managers", "119031": "Education and Childcare Administrators, Preschool and Daycare", "119033": "Education Administrators, Postsecondary", "119039": "Education Administrators, All Other", "119041": "Architectural and Engineering Managers", "119051": "Food Service Managers", "119081": "Lodging Managers", "119111": "Medical and Health Services Managers", "119121": "Natural Sciences Managers", "119141": "Property, Real Estate, and Community Association Managers", "119151": "Social and Community Service Managers", "119161": "Emergency Management Directors", "119199": "Managers, All Other", "130000": "Business and Financial Operations Occupations", "131020": "Buyers and Purchasing Agents", "131031": "Claims Adjusters, Examiners, and Investigators", "131032": "Insurance Appraisers, Auto Damage", "131041": "Compliance Officers", "131051": "Cost Estimators", "131071": "Human Resources Specialists", "131075": "Labor Relations Specialists", "131081": "Logisticians", "131082": "Project Management Specialists", "131111": "Management Analysts", "131121": "Meeting, Convention, and Event Planners", "131131": "Fundraisers", "131141": "Compensation, Benefits, and Job Analysis Specialists", "131151": "Training and Development Specialists", "131161": "Market Research Analysts and Marketing Specialists", "131199": "Business Operations Specialists, All Other", "132011": "Accountants and Auditors", "132020": "Property Appraisers and Assessors", "132031": "Budget Analysts", "132041": "Credit Analysts", "132051": "Financial and Investment Analysts", "132052": "Personal Financial Advisors", "132053": "Insurance Underwriters", "132054": "Financial Risk Specialists", "132061": "Financial Examiners", "132071": "Credit Counselors", "132072": "Loan Officers", "132081": "Tax Examiners and Collectors, and Revenue Agents", "132082": "Tax Preparers", "132099": "Financial Specialists, All Other", "150000": "Computer and Mathematical Occupations", "151211": "Computer Systems Analysts", "151212": "Information Security Analysts", "151221": "Computer and Information Research Scientists", "151231": "Computer Network Support Specialists", "151232": "Computer User Support Specialists", "151241": "Computer Network Architects", "151242": "Database Administrators", "151243": "Database Architects", "151244": "Network and Computer Systems Administrators", "151251": "Computer Programmers", "151252": "Software Developers", "151253": "Software Quality Assurance Analysts and Testers", "151254": "Web Developers", "151255": "Web and Digital Interface Designers", "151299": "Computer Occupations, All Other", "152011": "Actuaries", "152031": "Operations Research Analysts", "152041": "Statisticians", "152051": "Data Scientists", "170000": "Architecture and Engineering Occupations", "171011": "Architects, Except Landscape and Naval", "171022": "Surveyors", "172011": "Aerospace Engineers", "172031": "Bioengineers and Biomedical Engineers", "172041": "Chemical Engineers", "172051": "Civil Engineers", "172061": "Computer Hardware Engineers", "172071": "Electrical Engineers", "172072": "Electronics Engineers, Except Computer", "172081": "Environmental Engineers", "172111": "Health and Safety Engineers, Except Mining Safety Engineers and Inspectors", "172112": "Industrial Engineers", "172131": "Materials Engineers", "172141": "Mechanical Engineers", "172161": "Nuclear Engineers", "172171": "Petroleum Engineers", "172199": "Engineers, All Other", "173011": "Architectural and Civil Drafters", "173012": "Electrical and Electronics Drafters", "173013": "Mechanical Drafters", "173019": "Drafters, All Other", "173021": "Aerospace Engineering and Operations Technologists and Technicians", "173022": "Civil Engineering Technologists and Technicians", "173023": "Electrical and Electronic Engineering Technologists and Technicians", "173024": "Electro-Mechanical and Mechatronics Technologists and Technicians", "173025": "Environmental Engineering Technologists and Technicians", "173026": "Industrial Engineering Technologists and Technicians", "173027": "Mechanical Engineering Technologists and Technicians", "173029": "Engineering Technologists and Technicians, Except Drafters, All Other", "173031": "Surveying and Mapping Technicians", "190000": "Life, Physical, and Social Science Occupations", "191021": "Biochemists and Biophysicists", "191022": "Microbiologists", "191023": "Zoologists and Wildlife Biologists", "191029": "Biological Scientists, All Other", "191041": "Epidemiologists", "191042": "Medical Scientists, Except Epidemiologists", "191099": "Life Scientists, All Other", "192012": "Physicists", "192031": "Chemists", "192032": "Materials Scientists", "192041": "Environmental Scientists and Specialists, Including Health", "192042": "Geoscientists, Except Hydrologists and Geographers", "192099": "Physical Scientists, All Other", "193011": "Economists", "193022": "Survey Researchers", "193033": "Clinical and Counseling Psychologists", "193034": "School Psychologists", "193039": "Psychologists, All Other", "193051": "Urban and Regional Planners", "193094": "Political Scientists", "193099": "Social Scientists and Related Workers, All Other", "194012": "Agricultural Technicians", "194013": "Food Science Technicians", "194021": "Biological Technicians", "194031": "Chemical Technicians", "194042": "Environmental Science and Protection Technicians, Including Health", "194051": "Nuclear Technicians", "194061": "Social Science Research Assistants", "194099": "Life, Physical, and Social Science Technicians, All Other", "195011": "Occupational Health and Safety Specialists", "195012": "Occupational Health and Safety Technicians", "210000": "Community and Social Service Occupations", "211012": "Educational, Guidance, and Career Counselors and Advisors", "211013": "Marriage and Family Therapists", "211015": "Rehabilitation Counselors", "211018": "Substance abuse, behavioral disorder, and mental health counselors", "211019": "Counselors, All Other", "211021": "Child, Family, and School Social Workers", "211022": "Healthcare Social Workers", "211023": "Mental Health and Substance Abuse Social Workers", "211029": "Social Workers, All Other", "211091": "Health Education Specialists", "211092": "Probation Officers and Correctional Treatment Specialists", "211093": "Social and Human Service Assistants", "211094": "Community Health Workers", "211099": "Community and Social Service Specialists, All Other", "212011": "Clergy", "212021": "Directors, Religious Activities and Education", "212099": "Religious Workers, All Other", "230000": "Legal Occupations", "231011": "Lawyers", "231021": "Administrative Law Judges, Adjudicators, and Hearing Officers", "231023": "Judges, Magistrate Judges, and Magistrates", "232011": "Paralegals and Legal Assistants", "232093": "Title Examiners, Abstractors, and Searchers", "232099": "Legal Support Workers, All Other", "250000": "Educational Instruction and Library Occupations", "251194": "Career/Technical Education Teachers, Postsecondary", "252011": "Preschool Teachers, Except Special Education", "253011": "Adult Basic Education, Adult Secondary Education, and English as a Second Language Instructors", "253021": "Self-Enrichment Teachers", "253031": "Substitute Teachers, Short-Term", "253041": "Tutors", "254011": "Archivists", "254012": "Curators", "254013": "Museum Technicians and Conservators", "254022": "Librarians and Media Collections Specialists", "254031": "Library Technicians", "259021": "Farm and Home Management Educators", "259031": "Instructional Coordinators", "259099": "Educational Instruction and Library Workers, All Other", "270000": "Arts, Design, Entertainment, Sports, and Media Occupations", "271011": "Art Directors", "271014": "Special Effects Artists and Animators", "271021": "Commercial and Industrial Designers", "271022": "Fashion Designers", "271023": "Floral Designers", "271024": "Graphic Designers", "271025": "Interior Designers", "271026": "Merchandise Displayers and Window Trimmers", "272012": "Producers and Directors", "272099": "Entertainers and Performers, Sports and Related Workers, All Other", "273011": "Broadcast Announcers and Radio Disc Jockeys", "273023": "News Analysts, Reporters, and Journalists", "273031": "Public Relations Specialists", "273041": "Editors", "273042": "Technical Writers", "273043": "Writers and Authors", "273091": "Interpreters and Translators", "274011": "Audio and Video Technicians", "274021": "Photographers", "274031": "Camera Operators, Television, Video, and Film", "274032": "Film and Video Editors", "274099": "Media and Communication Equipment Workers, All Other", "290000": "Healthcare Practitioners and Technical Occupations", "291021": "Dentists, General", "291031": "Dietitians and Nutritionists", "291051": "Pharmacists", "291071": "Physician Assistants", "291122": "Occupational Therapists", "291123": "Physical Therapists", "291124": "Radiation Therapists", "291125": "Recreational Therapists", "291126": "Respiratory Therapists", "291127": "Speech-Language Pathologists", "291128": "Exercise Physiologists", "291131": "Veterinarians", "291141": "Registered Nurses", "291151": "Nurse Anesthetists", "291171": "Nurse Practitioners", "291181": "Audiologists", "291214": "Emergency Medicine Physicians", "291215": "Family Medicine Physicians", "291216": "General Internal Medicine Physicians", "291218": "Obstetricians and Gynecologists", "291221": "Pediatricians, General", "291223": "Psychiatrists", "291229": "Physicians, All Other", "291249": "Surgeons, All Other", "291292": "Dental Hygienists", "292010": "Clinical Laboratory Technologists and Technicians", "292031": "Cardiovascular Technologists and Technicians", "292032": "Diagnostic Medical Sonographers", "292033": "Nuclear Medicine Technologists", "292034": "Radiologic Technologists and Technicians", "292035": "Magnetic Resonance Imaging Technologists", "292042": "Emergency Medical Technicians", "292043": "Paramedics", "292051": "Dietetic Technicians", "292052": "Pharmacy Technicians", "292053": "Psychiatric Technicians", "292055": "Surgical Technologists", "292056": "Veterinary Technologists and Technicians", "292057": "Ophthalmic Medical Technicians", "292061": "Licensed Practical and Licensed Vocational Nurses", "292072": "Medical Records Specialists", "292091": "Orthotists and Prosthetists", "292092": "Hearing Aid Specialists", "292099": "Health Technologists and Technicians, All Other", "299021": "Health Information Technologists and Medical Registrars", "299099": "Healthcare Practitioners and Technical Workers, All Other", "310000": "Healthcare Support Occupations", "311120": "Home Health and Personal Care Aides", "311131": "Nursing Assistants", "311132": "Orderlies", "311133": "Psychiatric Aides", "312011": "Occupational Therapy Assistants", "312012": "Occupational Therapy Aides", "312021": "Physical Therapist Assistants", "312022": "Physical Therapist Aides", "319011": "Massage Therapists", "319091": "Dental Assistants", "319092": "Medical Assistants", "319093": "Medical Equipment Preparers", "319094": "Medical Transcriptionists", "319095": "Pharmacy Aides", "319096": "Veterinary Assistants and Laboratory Animal Caretakers", "319097": "Phlebotomists", "319099": "Healthcare Support Workers, All Other", "330000": "Protective Service Occupations", "331011": "First-Line Supervisors of Correctional Officers", "331012": "First-Line Supervisors of Police and Detectives", "331021": "First-Line Supervisors of Firefighting and Prevention Workers", "331091": "First-Line Supervisors of Security Workers", "331099": "First-Line Supervisors of Protective Service Workers, All Other", "332011": "Firefighters", "332021": "Fire Inspectors and Investigators", "333012": "Correctional Officers and Jailers", "333021": "Detectives and Criminal Investigators", "333041": "Parking Enforcement Workers", "333051": "Police and Sheriff's Patrol Officers", "339011": "Animal Control Workers", "339021": "Private Detectives and Investigators", "339031": "Gaming Surveillance Officers and Gambling Investigators", "339032": "Security Guards", "339091": "Crossing Guards and Flaggers", "339092": "Lifeguards, Ski Patrol, and Other Recreational Protective Service Workers", "339094": "School Bus Monitors", "339099": "Protective Service Workers, All Other", "350000": "Food Preparation and Serving Related Occupations", "351011": "Chefs and Head Cooks", "351012": "First-Line Supervisors of Food Preparation and Serving Workers", "352011": "Cooks, Fast Food", "352012": "Cooks, Institution and Cafeteria", "352014": "Cooks, Restaurant", "352015": "<PERSON><PERSON>, Short Order", "352019": "<PERSON><PERSON>, All Other", "352021": "Food Preparation Workers", "353011": "Bart<PERSON>s", "353023": "Fast Food and Counter Workers", "353031": "Waiters and Waitresses", "353041": "Food Servers, Nonrestaurant", "359011": "Dining Room and Cafeteria Attendants and Bartender Helpers", "359021": "Dishwashers", "359031": "Hosts and Hostesses, Restaurant, Lounge, and Coffee Shop", "359099": "Food Preparation and Serving Related Workers, All Other", "370000": "Building and Grounds Cleaning and Maintenance Occupations", "371011": "First-Line Supervisors of Housekeeping and Janitorial Workers", "371012": "First-Line Supervisors of Landscaping, Lawn Service, and Groundskeeping Workers", "372011": "Janitors and Cleaners, Except Maids and Housekeeping Cleaners", "372012": "Maids and Housekeeping Cleaners", "372019": "Building Cleaning Workers, All Other", "372021": "Pest Control Workers", "373011": "Landscaping and Groundskeeping Workers", "373012": "Pesticide Handlers, Sprayers, and Applicators, Vegetation", "373013": "Tree Trimmers and Pruners", "373019": "Grounds Maintenance Workers, All Other", "390000": "Personal Care and Service Occupations", "391013": "First-Line Supervisors of Gambling Services Workers", "391014": "First-Line Supervisors of Entertainment and Recreation Workers, Except Gambling Services", "391022": "First-Line Supervisors of Personal Service Workers", "392011": "Animal Trainers", "392021": "Animal Caretakers", "393011": "Gambling Dealers", "393019": "Gambling Service Workers, All Other", "393031": "Ushers, <PERSON>bby Attendants, and Ticket Takers", "393091": "Amusement and Recreation Attendants", "393093": "Locker Room, Coatroom, and Dressing Room Attendants", "393099": "Entertainment Attendants and Related Workers, All Other", "394031": "Morticians, Undertakers, and Funeral Arrangers", "395012": "Hairdressers, Hairstylists, and Cosmetologists", "395092": "Manicurists and Pedicurists", "395094": "Skincare Specialists", "396011": "Baggage Porters and Bellhops", "396012": "Concierges", "397010": "Tour and Travel Guides", "399011": "Childcare Workers", "399031": "Exercise Trainers and Group Fitness Instructors", "399032": "Recreation Workers", "399041": "Residential Advisors", "399099": "Personal Care and Service Workers, All Other", "410000": "Sales and Related Occupations", "411011": "First-Line Supervisors of Retail Sales Workers", "411012": "First-Line Supervisors of Non-Retail Sales Workers", "412011": "Cashiers", "412012": "Gambling Change Persons and Booth Cashiers", "412021": "Counter and Rental Clerks", "412022": "Parts Salespersons", "412031": "Retail Salespersons", "413011": "Advertising Sales Agents", "413021": "Insurance Sales Agents", "413031": "Securities, Commodities, and Financial Services Sales Agents", "413041": "Travel Agents", "413091": "Sales Representatives of Services, Except Advertising, Insurance, Financial Services, and Travel", "414011": "Sales Representatives, Wholesale and Manufacturing, Technical and Scientific Products", "414012": "Sales Representatives, Wholesale and Manufacturing, Except Technical and Scientific Products", "419011": "Demonstrators and Product Promoters", "419021": "Real Estate Brokers", "419022": "Real Estate Sales Agents", "419031": "Sales Engineers", "419041": "Telemarketers", "419099": "Sales and Related Workers, All Other", "430000": "Office and Administrative Support Occupations", "431011": "First-Line Supervisors of Office and Administrative Support Workers", "432011": "Switchboard Operators, Including Answering Service", "433011": "Bill and Account Collectors", "433021": "Billing and Posting Clerks", "433031": "Bookkeeping, Accounting, and Auditing Clerks", "433041": "Gambling Cage Workers", "433051": "Payroll and Timekeeping Clerks", "433061": "Procurement Clerks", "433071": "Tellers", "433099": "Financial Clerks, All Other", "434011": "Brokerage Clerks", "434021": "Correspondence Clerks", "434031": "Court, Municipal, and License Clerks", "434041": "Credit Authorizers, Checkers, and Clerks", "434051": "Customer Service Representatives", "434061": "Eligibility Interviewers, Government Programs", "434071": "File Clerks", "434081": "Hotel, Motel, and Resort Desk Clerks", "434111": "Interviewers, Except <PERSON><PERSON><PERSON> and Loan", "434121": "Library Assistants, Clerical", "434131": "Loan Interviewers and Clerks", "434141": "New Accounts Clerks", "434151": "Order Clerks", "434161": "Human Resources Assistants, Except Payroll and Timekeeping", "434171": "Receptionists and Information Clerks", "434181": "Reservation and Transportation Ticket Agents and Travel Clerks", "434199": "Information and Record Clerks, All Other", "435011": "Cargo and Freight Agents", "435021": "Couriers and Messengers", "435031": "Public Safety Telecommunicators", "435032": "Dispatchers, Except Police, Fire, and Ambulance", "435041": "Meter Readers, Utilities", "435061": "Production, Planning, and Expediting Clerks", "435071": "Shipping, Receiving, and Inventory Clerks", "435111": "Weighers, Measurers, Checkers, and Samplers, Recordkeeping", "436011": "Executive Secretaries and Executive Administrative Assistants", "436012": "Legal Secretaries and Administrative Assistants", "436013": "Medical Secretaries and Administrative Assistants", "436014": "Secretaries and Administrative Assistants, Except Legal, Medical, and Executive", "439021": "Data Entry Keyers", "439022": "Word Processors and Typists", "439031": "Desktop Publishers", "439041": "Insurance Claims and Policy Processing Clerks", "439051": "Mail Clerks and Mail Machine Operators, Except Postal Service", "439061": "Office Clerks, General", "439071": "Office Machine Operators, Except Computer", "439081": "Proofreaders and Copy Markers", "439111": "Statistical Assistants", "439199": "Office and Administrative Support Workers, All Other", "470000": "Construction and Extraction Occupations", "471011": "First-Line Supervisors of Construction Trades and Extraction Workers", "472011": "Boilermakers", "472021": "Brickmasons and Blockmasons", "472022": "Stonemasons", "472031": "<PERSON><PERSON>", "472042": "Floor Layers, Except Car<PERSON>, Wood, and Hard Tiles", "472044": "Tile and Stone Setters", "472051": "Cement Masons and Concrete Finishers", "472061": "Construction Laborers", "472071": "Paving, Surfacing, and Tamping Equipment Operators", "472073": "Operating Engineers and Other Construction Equipment Operators", "472081": "Drywall and Ceiling Tile Installers", "472082": "Tapers", "472111": "Electricians", "472121": "Glaziers", "472131": "Insulation Workers, Floor, Ceiling, and Wall", "472132": "Insulation Workers, Mechanical", "472141": "Painters, Construction and Maintenance", "472151": "Pipelayers", "472152": "Plumbers, Pipefitters, and Steamfitters", "472161": "Plasterers and Stucco Masons", "472171": "Reinforcing Iron and Rebar Workers", "472181": "Roofers", "472211": "Sheet Metal Workers", "472221": "Structural Iron and Steel Workers", "473011": "Helpers--Brickmasons, Blockmasons, Stonemasons, and Tile and Marble Setters", "473012": "Helpers--Carpenter<PERSON>", "473013": "Helpers--Electricians", "473014": "Helpers--Painters, Paperhangers, Plasterers, and Stucco Masons", "473015": "Helpers--Pipelayers, Plumbers, Pipefitters, and Steamfitters", "473019": "Helpers, Construction Trades, All Other", "474011": "Construction and Building Inspectors", "474021": "Elevator and Escalator Installers and Repairers", "474031": "<PERSON>ce E<PERSON>ctors", "474041": "Hazardous Materials Removal Workers", "474051": "Highway Maintenance Workers", "474061": "Rail-Track Laying and Maintenance Equipment Operators", "474071": "Septic Tank Servicers and Sewer Pipe Cleaners", "474090": "Miscellaneous Construction and Related Workers", "475011": "Derrick Operators, Oil and Gas", "475012": "Rotary Drill Operators, Oil and Gas", "475013": "Service Unit Operators, Oil and Gas", "475022": "Excavating and Loading Machine and Dragline Operators, Surface Mining", "475023": "Earth Drillers, Except Oil and Gas", "475051": "Rock Splitters, Quarry", "475071": "Roustabouts, Oil and Gas", "475081": "Helpers--Extraction Workers", "475099": "Extraction Workers, All Other", "490000": "Installation, Maintenance, and Repair Occupations", "491011": "First-Line Supervisors of Mechanics, Installers, and Repairers", "492011": "Computer, Automated Teller, and Office Machine Repairers", "492021": "Radio, Cellular, and Tower Equipment Installers and Repairers", "492022": "Telecommunications Equipment Installers and Repairers, Except Line Installers", "492091": "Avionics Technicians", "492092": "Electric Motor, Power Tool, and Related Repairers", "492093": "Electrical and Electronics Installers and Repairers, Transportation Equipment", "492094": "Electrical and Electronics Repairers, Commercial and Industrial Equipment", "492095": "Electrical and Electronics Repairers, Powerhouse, Substation, and Relay", "492096": "Electronic Equipment Installers and Repairers, Motor Vehicles", "492097": "Audiovisual Equipment Installers and Repairers", "492098": "Security and Fire Alarm Systems Installers", "493011": "Aircraft Mechanics and Service Technicians", "493021": "Automotive Body and Related Repairers", "493022": "Automotive Glass Installers and Repairers", "493023": "Automotive Service Technicians and Mechanics", "493031": "Bus and Truck Mechanics and Diesel Engine Specialists", "493041": "Farm Equipment Mechanics and Service Technicians", "493042": "Mobile Heavy Equipment Mechanics, Except Engines", "493043": "Rail Car Repairers", "493051": "Motorboat Mechanics and Service Technicians", "493053": "Outdoor Power Equipment and Other Small Engine Mechanics", "493091": "Bicycle Repairers", "493092": "Recreational Vehicle Service Technicians", "493093": "Tire Repairers and Changers", "499011": "Mechanical Door Repairers", "499012": "Control and Valve Installers and Repairers, Except Mechanical Door", "499021": "Heating, Air Conditioning, and Refrigeration Mechanics and Installers", "499031": "Home Appliance Repairers", "499041": "Industrial Machinery Mechanics", "499043": "Maintenance Workers, Machinery", "499044": "Millwrights", "499051": "Electrical Power-Line Installers and Repairers", "499052": "Telecommunications Line Installers and Repairers", "499062": "Medical Equipment Repairers", "499069": "Precision Instrument and Equipment Repairers, All Other", "499071": "Maintenance and Repair Workers, General", "499091": "Coin, Vending, and Amusement Machine Servicers and Repairers", "499096": "Riggers", "499097": "Signal and Track Switch Repairers", "499098": "Helpers--Installation, Maintenance, and Repair Workers", "499099": "Installation, Maintenance, and Repair Workers, All Other", "510000": "Production Occupations", "511011": "First-Line Supervisors of Production and Operating Workers", "512011": "Aircraft Structure, Surfaces, Rigging, and Systems Assemblers", "512021": "Coil Winders, Tapers, and Finishers", "512028": "Electrical, electronic, and electromechanical assemblers, except coil winders, tapers, and finishers", "512031": "Engine and Other Machine Assemblers", "512041": "Structural Metal Fabricators and Fitters", "512051": "Fiberglass Laminators and Fabricators", "512090": "Miscellaneous Assemblers and Fabricators", "513011": "<PERSON><PERSON>", "513021": "Butchers and Meat Cutters", "513022": "Meat, Poultry, and Fish Cutters and Trimmers", "513023": "Slaughterers and Meat Packers", "513091": "Food and Tobacco Roasting, Baking, and Drying Machine Operators and Tenders", "513092": "Food Batchmakers", "513093": "Food Cooking Machine Operators and Tenders", "513099": "Food Processing Workers, All Other", "514021": "Extruding and Drawing Machine Setters, Operators, and Tenders, Metal and Plastic", "514022": "Forging Machine Setters, Operators, and Tenders, Metal and Plastic", "514023": "Rolling Machine Setters, Operators, and Tenders, Metal and Plastic", "514031": "Cutting, Punching, and Press Machine Setters, Operators, and Tenders, Metal and Plastic", "514032": "Drilling and Boring Machine Tool Setters, Operators, and Tenders, Metal and Plastic", "514033": "Grinding/Lapping/Polishing/Buffing Machine Tool Setters, Operators, and Tenders, Metal and Plastic", "514034": "Lathe and Turning Machine Tool Setters, Operators, and Tenders, Metal and Plastic", "514035": "Milling and Planing Machine Setters, Operators, and Tenders, Metal and Plastic", "514041": "Machinists", "514051": "Metal-Refining Furnace Operators and Tenders", "514052": "Pourers and Casters, Metal", "514071": "Foundry Mold and Coremakers", "514072": "Molding, Coremaking, and Casting Machine Setters, Operators, and Tenders, Metal and Plastic", "514081": "Multiple Machine Tool Setters, Operators, and Tenders, Metal and Plastic", "514111": "Tool and Die Makers", "514121": "Welders, Cutters, Solderers, and Brazers", "514122": "Welding, Soldering, and Brazing Machine Setters, Operators, and Tenders", "514191": "Heat Treating Equipment Setters, Operators, and Tenders, Metal and Plastic", "514192": "Layout Workers, Metal and Plastic", "514193": "Plating Machine Setters, Operators, and Tenders, Metal and Plastic", "514194": "Tool Grinders, Filers, and Sharpeners", "514199": "Metal Workers and Plastic Workers, All Other", "515111": "Prepress Technicians and Workers", "515112": "Printing Press Operators", "515113": "Print Binding and Finishing Workers", "516011": "Laundry and Dry-Cleaning Workers", "516021": "Pressers, Textile, Garment, and Related Materials", "516031": "Sewing Machine Operators", "516052": "Tailors, Dressmakers, and Custom Sewers", "516062": "Textile Cutting Machine Setters, Operators, and Tenders", "516063": "Textile Knitting and Weaving Machine Setters, Operators, and Tenders", "516064": "Textile Winding, Twisting, and Drawing Out Machine Setters, Operators, and Tenders", "516092": "<PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> Patternmakers", "516093": "Upholsterers", "516099": "Textile, Apparel, and Furnishings Workers, All Other", "517011": "Cabinetmakers and Bench Carpenters", "517021": "Furniture Finishers", "517041": "Sawing Machine Setters, Operators, and Tenders, Wood", "517042": "Woodworking Machine Setters, Operators, and Tenders, Except Sawing", "517099": "Woodworkers, All Other", "518011": "Nuclear Power Reactor Operators", "518012": "Power Distributors and Dispatchers", "518013": "Power Plant Operators", "518021": "Stationary Engineers and Boiler Operators", "518031": "Water and Wastewater Treatment Plant and System Operators", "518091": "Chemical Plant and System Operators", "518092": "Gas Plant Operators", "518093": "Petroleum Pump System Operators, Refinery Operators, and Gaugers", "518099": "Plant and System Operators, All Other", "519011": "Chemical Equipment Operators and Tenders", "519012": "Separating, Filtering, Clarifying, Precipitating, and Still Machine Setters, Operators, and Tenders", "519021": "Crushing, Grinding, and Polishing Machine Setters, Operators, and Tenders", "519022": "Grinding and Polishing Workers, Hand", "519023": "Mixing and Blending Machine Setters, Operators, and Tenders", "519031": "Cutters and Trimmers, Hand", "519032": "Cutting and Slicing Machine Setters, Operators, and Tenders", "519041": "Extruding, Forming, Pressing, and Compacting Machine Setters, Operators, and Tenders", "519051": "Furnace, Kiln, Oven, Drier, and Kettle Operators and Tenders", "519061": "Inspectors, Testers, Sorters, Sam<PERSON>s, and Weighers", "519071": "Jewelers and Precious Stone and Metal Workers", "519081": "Dental Laboratory Technicians", "519083": "Ophthalmic Laboratory Technicians", "519111": "Packaging and Filling Machine Operators and Tenders", "519123": "Painting, Coating, and Decorating Workers", "519124": "Coating, Painting, and Spraying Machine Setters, Operators, and Tenders", "519141": "Semiconductor Processing Technicians", "519151": "Photographic Process Workers and Processing Machine Operators", "519161": "Computer Numerically Controlled Tool Operators", "519162": "Computer Numerically Controlled Tool Programmers", "519191": "Adhesive Bonding Machine Operators and Tenders", "519192": "Cleaning, Washing, and Metal Pickling Equipment Operators and Tenders", "519194": "Etchers and Engravers", "519195": "Molders, <PERSON><PERSON><PERSON>, and Casters, Except Metal and Plastic", "519196": "Paper Goods Machine Setters, Operators, and Tenders", "519197": "Tire Builders", "519198": "Helpers--Production Workers", "519199": "Production Workers, All Other", "530000": "Transportation and Material Moving Occupations", "531041": "Aircraft Cargo Handling Supervisors", "531047": "First-Line Supervisors of Transportation Workers, Except Aircraft Cargo Handling Supervisors", "533031": "Driver/Sales Workers", "533032": "Heavy and Tractor-Trailer Truck Drivers", "533033": "Light Truck Drivers", "533051": "Bus Drivers, School", "533052": "Bus Drivers, Transit and Intercity", "533053": "Shuttle Drivers and Chauffeurs", "533099": "Motor Vehicle Operators, All Other", "534011": "Locomotive Engineers", "534022": "Railroad Brake, Signal, and Switch Operators and Locomotive Firers", "534031": "Railroad Conductors and Yardmasters", "534041": "Subway and Streetcar Operators", "535011": "Sailors and Marine Oilers", "535021": "Captains, Mates, and Pilots of Water Vessels", "536021": "Parking Attendants", "536031": "Automotive and Watercraft Service Attendants", "536032": "Aircraft Service Attendants", "536041": "Traffic Technicians", "536051": "Transportation Inspectors", "536061": "Passenger Attendants", "536099": "Transportation Workers, All Other", "537011": "Conveyor Operators and Tenders", "537021": "Crane and Tower Operators", "537041": "Hoist and Winch Operators", "537051": "Industrial Truck and Tractor Operators", "537061": "Cleaners of Vehicles and Equipment", "537062": "Laborers and Freight, Stock, and Material Movers, Hand", "537063": "Machine Feeders and Offbearers", "537064": "Packers and Packagers, Hand", "537065": "Stockers and Order Fillers", "537073": "Wellhead Pumpers", "537081": "Refuse and Recyclable Material Collectors", "537121": "Tank Car, Truck, and Ship Loaders", "537199": "Material Moving Workers, All Other"}, "subcell_code": {"00": "All workers", "23": "Union", "24": "Nonunion", "25": "Full-time", "26": "Part-time", "27": "Time-based pay", "28": "Incentive-based pay", "AD": "Bargaining Status", "AE": "Work Status", "AF": "Time and Incentive Status"}, "datatype_code": {"30": "Mean"}, "level_code": {"00": "All levels", "01": "Level 01", "02": "Level 02", "03": "Level 03", "04": "Level 04", "05": "Level 05", "06": "Level 06", "07": "Level 07", "08": "Level 08", "09": "Level 09", "10": "Level 10", "11": "Level 11", "12": "Level 12", "13": "Level 13", "14": "Level 14", "16": "Not able to be leveled", "A1": "Entry", "A2": "Intermediate", "A3": "Experienced"}, "footnote_code": {"2": "BLS is currently developing an approach to calculate the relative standard errors due to implementation of modeled-based estimates (MB3) used by the Occupational Employment and Wage Statistics (OEWS)."}}}