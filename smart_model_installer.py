#!/usr/bin/env python3
"""
智能AI模型安装器
基于DeepSeek评估结果自动选择和安装最适合的模型
"""

import asyncio
import json
import subprocess
import sys
import os
from pathlib import Path
from ai_model_evaluator import DeepSeekModelEvaluator

class SmartModelInstaller:
    """智能模型安装器"""
    
    def __init__(self):
        self.evaluator = DeepSeekModelEvaluator()
        self.evaluation_file = "deepseek_model_evaluation.json"
    
    def check_system_resources(self) -> dict:
        """检查系统资源"""
        import psutil
        
        # 获取内存信息
        memory = psutil.virtual_memory()
        total_memory_gb = memory.total / (1024**3)
        available_memory_gb = memory.available / (1024**3)
        
        # 获取磁盘空间
        disk = psutil.disk_usage('/')
        free_space_gb = disk.free / (1024**3)
        
        return {
            "total_memory_gb": round(total_memory_gb, 1),
            "available_memory_gb": round(available_memory_gb, 1),
            "free_disk_gb": round(free_space_gb, 1),
            "cpu_count": psutil.cpu_count()
        }
    
    def filter_models_by_resources(self, evaluations: list, system_resources: dict) -> list:
        """根据系统资源过滤模型"""
        available_memory = system_resources["available_memory_gb"]
        free_disk = system_resources["free_disk_gb"]
        
        # 模型资源需求映射
        resource_requirements = {
            "phi3:3.8b": {"memory": 4, "disk": 3},
            "yi:6b-chat": {"memory": 6, "disk": 4},
            "deepseek-coder:6.7b-instruct": {"memory": 8, "disk": 4},
            "qwen2.5:7b-instruct": {"memory": 8, "disk": 5},
            "deepseek-math:7b-instruct": {"memory": 8, "disk": 5},
            "gemma:7b-instruct": {"memory": 8, "disk": 5},
            "llama3.1:8b-instruct": {"memory": 8, "disk": 5},
            "qwen2.5:14b-instruct": {"memory": 16, "disk": 9},
            "llama3.1:70b-instruct": {"memory": 64, "disk": 45}
        }
        
        filtered = []
        for eval_result in evaluations:
            model_name = eval_result.get("model", "")
            requirements = resource_requirements.get(model_name, {"memory": 8, "disk": 5})
            
            if (available_memory >= requirements["memory"] and 
                free_disk >= requirements["disk"]):
                eval_result["resource_compatible"] = True
                filtered.append(eval_result)
            else:
                eval_result["resource_compatible"] = False
                eval_result["resource_issue"] = f"需要{requirements['memory']}GB内存和{requirements['disk']}GB磁盘空间"
        
        return filtered
    
    async def get_deepseek_recommendation(self) -> dict:
        """获取DeepSeek的推荐"""
        print("🤖 启动DeepSeek AI评估专家...")
        
        # 检查是否已有评估结果
        if Path(self.evaluation_file).exists():
            print("📋 发现已有评估结果，是否重新评估？")
            choice = input("输入 'y' 重新评估，其他键使用已有结果: ").strip().lower()
            
            if choice != 'y':
                with open(self.evaluation_file, 'r', encoding='utf-8') as f:
                    evaluations = json.load(f)
                print("✅ 使用已有评估结果")
                return self._process_evaluations(evaluations)
        
        # 执行新的评估
        task_description = """
        智能股票分析和投资建议系统，需要AI模型具备：
        1. 股票技术指标分析能力
        2. 基本面数据解读能力  
        3. 中文金融术语理解
        4. 投资建议生成能力
        5. 风险评估和提示
        6. 实时响应能力
        7. 8-16GB内存环境稳定运行
        """
        
        evaluations = await self.evaluator.comprehensive_evaluation(task_description)
        
        # 保存评估结果
        with open(self.evaluation_file, 'w', encoding='utf-8') as f:
            json.dump(evaluations, f, ensure_ascii=False, indent=2)
        
        return self._process_evaluations(evaluations)
    
    def _process_evaluations(self, evaluations: list) -> dict:
        """处理评估结果"""
        # 过滤有效评估
        valid_evaluations = [e for e in evaluations if 'overall_score' in e and not e.get('error')]
        
        if not valid_evaluations:
            return {"error": "没有有效的评估结果"}
        
        # 检查系统资源
        system_resources = self.check_system_resources()
        print(f"\n💻 系统资源: {system_resources['available_memory_gb']}GB可用内存, {system_resources['free_disk_gb']}GB可用磁盘")
        
        # 根据资源过滤
        compatible_models = self.filter_models_by_resources(valid_evaluations, system_resources)
        
        if not compatible_models:
            print("⚠️ 没有与当前系统资源兼容的模型")
            return {"error": "资源不足", "system_resources": system_resources}
        
        # 按评分排序
        compatible_models.sort(key=lambda x: x['overall_score'], reverse=True)
        
        return {
            "recommended_model": compatible_models[0],
            "alternative_models": compatible_models[1:3],  # 前3个备选
            "system_resources": system_resources,
            "all_evaluations": valid_evaluations
        }
    
    def install_model(self, model_name: str) -> bool:
        """安装指定模型"""
        print(f"\n📥 正在安装模型: {model_name}")
        print("⏳ 下载可能需要几分钟，请耐心等待...")
        
        try:
            # 显示下载进度
            process = subprocess.Popen(
                ["ollama", "pull", model_name],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                universal_newlines=True
            )
            
            # 实时显示输出
            for line in process.stdout:
                if line.strip():
                    print(f"   {line.strip()}")
            
            process.wait()
            
            if process.returncode == 0:
                print(f"✅ 模型 {model_name} 安装成功!")
                return True
            else:
                print(f"❌ 模型 {model_name} 安装失败")
                return False
                
        except FileNotFoundError:
            print("❌ 未找到ollama命令，请先安装Ollama")
            print("访问 https://ollama.ai/ 下载安装")
            return False
        except Exception as e:
            print(f"❌ 安装过程中出错: {e}")
            return False
    
    def update_system_config(self, model_name: str):
        """更新系统配置"""
        config_file = "ai_engine/local_llm.py"
        
        if not Path(config_file).exists():
            print(f"⚠️ 配置文件 {config_file} 不存在")
            return
        
        try:
            # 读取配置文件
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 更新默认模型
            old_pattern = 'model_name: str = "'
            if old_pattern in content:
                # 找到默认模型配置行
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if old_pattern in line and 'def __init__' in lines[max(0, i-5):i+1]:
                        # 更新这一行
                        start = line.find(old_pattern) + len(old_pattern)
                        end = line.find('"', start)
                        if end > start:
                            lines[i] = line[:start] + model_name + line[end:]
                            break
                
                # 写回文件
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                print(f"✅ 已更新配置文件，默认模型设为: {model_name}")
            else:
                print("⚠️ 未找到配置项，请手动更新")
                
        except Exception as e:
            print(f"❌ 更新配置失败: {e}")
    
    async def smart_install(self):
        """智能安装流程"""
        print("🧠 智能AI模型选择和安装系统")
        print("=" * 50)
        print("基于DeepSeek专家评估，为您的股票分析系统选择最佳AI模型")
        
        # 获取DeepSeek推荐
        recommendation = await self.get_deepseek_recommendation()
        
        if "error" in recommendation:
            print(f"❌ 获取推荐失败: {recommendation['error']}")
            return
        
        recommended = recommendation["recommended_model"]
        alternatives = recommendation["alternative_models"]
        
        print(f"\n🎯 DeepSeek专家推荐:")
        print("-" * 40)
        print(f"🥇 首选模型: {recommended['model']}")
        print(f"📊 综合评分: {recommended['overall_score']}/10")
        print(f"⭐ 推荐指数: {'★' * recommended.get('recommendation_stars', 0)}")
        
        if recommended.get('key_advantages'):
            print(f"💪 关键优势: {recommended['key_advantages']}")
        
        if alternatives:
            print(f"\n🔄 备选方案:")
            for i, alt in enumerate(alternatives, 1):
                print(f"   {i}. {alt['model']} (评分: {alt['overall_score']}/10)")
        
        # 用户确认
        print(f"\n❓ 是否安装推荐模型 '{recommended['model']}'?")
        print("1. 是，安装推荐模型")
        print("2. 选择备选模型")
        print("3. 查看详细评估报告")
        print("0. 退出")
        
        try:
            choice = input("\n请选择 (0-3): ").strip()
            
            if choice == "0":
                print("👋 退出安装")
                return
            elif choice == "1":
                model_to_install = recommended['model']
            elif choice == "2":
                self._show_alternatives_menu(alternatives)
                return
            elif choice == "3":
                self._show_detailed_report(recommendation["all_evaluations"])
                return
            else:
                print("❌ 无效选择")
                return
            
            # 执行安装
            if self.install_model(model_to_install):
                # 更新配置
                self.update_system_config(model_to_install)
                
                print(f"\n🎉 智能安装完成!")
                print(f"✅ 已安装模型: {model_to_install}")
                print(f"✅ 已更新系统配置")
                print(f"\n🚀 现在可以启动智能选股系统:")
                print(f"python start.py")
            else:
                print(f"\n❌ 安装失败")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消安装")
    
    def _show_alternatives_menu(self, alternatives: list):
        """显示备选模型菜单"""
        print(f"\n🔄 备选模型:")
        for i, alt in enumerate(alternatives, 1):
            print(f"{i}. {alt['model']} (评分: {alt['overall_score']}/10)")
        
        try:
            choice = int(input(f"\n请选择模型 (1-{len(alternatives)}): "))
            if 1 <= choice <= len(alternatives):
                model_to_install = alternatives[choice-1]['model']
                if self.install_model(model_to_install):
                    self.update_system_config(model_to_install)
                    print(f"\n🎉 安装完成: {model_to_install}")
            else:
                print("❌ 无效选择")
        except (ValueError, KeyboardInterrupt):
            print("❌ 无效输入")
    
    def _show_detailed_report(self, evaluations: list):
        """显示详细评估报告"""
        print(f"\n📊 详细评估报告:")
        print("=" * 60)
        
        for eval_result in evaluations:
            if 'overall_score' in eval_result:
                print(f"\n🤖 模型: {eval_result['model']}")
                print(f"📊 综合评分: {eval_result['overall_score']}/10")
                
                if 'scores' in eval_result:
                    print("📋 详细评分:")
                    for key, value in eval_result['scores'].items():
                        print(f"   {key}: {value}/10")
                
                if eval_result.get('key_advantages'):
                    print(f"💪 关键优势: {eval_result['key_advantages']}")
                
                if eval_result.get('main_risks'):
                    print(f"⚠️ 主要风险: {eval_result['main_risks']}")
                
                print("-" * 40)

async def main():
    """主函数"""
    installer = SmartModelInstaller()
    
    # 检查是否安装了psutil
    try:
        import psutil
    except ImportError:
        print("📦 安装系统资源检测库...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        import psutil
    
    await installer.smart_install()

if __name__ == "__main__":
    asyncio.run(main())
