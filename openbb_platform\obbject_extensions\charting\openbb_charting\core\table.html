<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>OpenBB Interactive Tables</title>
    <script>
      if (
        // check if user had saved dark as their
        // theme when accessing page before
        localStorage.theme === "dark" ||
        // or user's requesting dark color
        // scheme through operating system
        (!("theme" in localStorage) &&
          window.matchMedia("(prefers-color-scheme: dark)").matches)
      ) {
        // then if we have access to the document and the element
        // we add the dark class to the html element and
        // store the dark value in the localStorage
        if (document && document.documentElement) {
          document.documentElement.classList.add("dark");
          localStorage.setItem("theme", "dark");
        }
      } else {
        // else if we have access to the document and the element
        // we remove the dark class to the html element and
        // store the value light in the localStorage
        if (document && document.documentElement) {
          document.documentElement.classList.remove("dark");
          localStorage.setItem("theme", "light");
        }
      }
    </script>
    <script type="module" crossorigin>
function TS(e,n){for(var r=0;r<n.length;r++){const i=n[r];if(typeof i!="string"&&!Array.isArray(i)){for(const a in i)if(a!=="default"&&!(a in e)){const l=Object.getOwnPropertyDescriptor(i,a);l&&Object.defineProperty(e,a,l.get?l:{enumerable:!0,get:()=>i[a]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const n=document.createElement("link").relList;if(n&&n.supports&&n.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))i(a);new MutationObserver(a=>{for(const l of a)if(l.type==="childList")for(const c of l.addedNodes)c.tagName==="LINK"&&c.rel==="modulepreload"&&i(c)}).observe(document,{childList:!0,subtree:!0});function r(a){const l={};return a.integrity&&(l.integrity=a.integrity),a.referrerPolicy&&(l.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?l.credentials="include":a.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function i(a){if(a.ep)return;a.ep=!0;const l=r(a);fetch(a.href,l)}})();function lo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var mu={exports:{}},Qo={},vu={exports:{}},Ie={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yg;function IS(){if(yg)return Ie;yg=1;var e=Symbol.for("react.element"),n=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),c=Symbol.for("react.context"),d=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),v=Symbol.for("react.memo"),w=Symbol.for("react.lazy"),m=Symbol.iterator;function S(O){return O===null||typeof O!="object"?null:(O=m&&O[m]||O["@@iterator"],typeof O=="function"?O:null)}var y={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},P=Object.assign,x={};function E(O,z,ue){this.props=O,this.context=z,this.refs=x,this.updater=ue||y}E.prototype.isReactComponent={},E.prototype.setState=function(O,z){if(typeof O!="object"&&typeof O!="function"&&O!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,O,z,"setState")},E.prototype.forceUpdate=function(O){this.updater.enqueueForceUpdate(this,O,"forceUpdate")};function _(){}_.prototype=E.prototype;function T(O,z,ue){this.props=O,this.context=z,this.refs=x,this.updater=ue||y}var I=T.prototype=new _;I.constructor=T,P(I,E.prototype),I.isPureReactComponent=!0;var $=Array.isArray,L=Object.prototype.hasOwnProperty,B={current:null},N={key:!0,ref:!0,__self:!0,__source:!0};function b(O,z,ue){var de,X={},se=null,Q=null;if(z!=null)for(de in z.ref!==void 0&&(Q=z.ref),z.key!==void 0&&(se=""+z.key),z)L.call(z,de)&&!N.hasOwnProperty(de)&&(X[de]=z[de]);var oe=arguments.length-2;if(oe===1)X.children=ue;else if(1<oe){for(var q=Array(oe),fe=0;fe<oe;fe++)q[fe]=arguments[fe+2];X.children=q}if(O&&O.defaultProps)for(de in oe=O.defaultProps,oe)X[de]===void 0&&(X[de]=oe[de]);return{$$typeof:e,type:O,key:se,ref:Q,props:X,_owner:B.current}}function K(O,z){return{$$typeof:e,type:O.type,key:z,ref:O.ref,props:O.props,_owner:O._owner}}function J(O){return typeof O=="object"&&O!==null&&O.$$typeof===e}function ne(O){var z={"=":"=0",":":"=2"};return"$"+O.replace(/[=:]/g,function(ue){return z[ue]})}var te=/\/+/g;function Z(O,z){return typeof O=="object"&&O!==null&&O.key!=null?ne(""+O.key):z.toString(36)}function G(O,z,ue,de,X){var se=typeof O;(se==="undefined"||se==="boolean")&&(O=null);var Q=!1;if(O===null)Q=!0;else switch(se){case"string":case"number":Q=!0;break;case"object":switch(O.$$typeof){case e:case n:Q=!0}}if(Q)return Q=O,X=X(Q),O=de===""?"."+Z(Q,0):de,$(X)?(ue="",O!=null&&(ue=O.replace(te,"$&/")+"/"),G(X,z,ue,"",function(fe){return fe})):X!=null&&(J(X)&&(X=K(X,ue+(!X.key||Q&&Q.key===X.key?"":(""+X.key).replace(te,"$&/")+"/")+O)),z.push(X)),1;if(Q=0,de=de===""?".":de+":",$(O))for(var oe=0;oe<O.length;oe++){se=O[oe];var q=de+Z(se,oe);Q+=G(se,z,ue,q,X)}else if(q=S(O),typeof q=="function")for(O=q.call(O),oe=0;!(se=O.next()).done;)se=se.value,q=de+Z(se,oe++),Q+=G(se,z,ue,q,X);else if(se==="object")throw z=String(O),Error("Objects are not valid as a React child (found: "+(z==="[object Object]"?"object with keys {"+Object.keys(O).join(", ")+"}":z)+"). If you meant to render a collection of children, use an array instead.");return Q}function ae(O,z,ue){if(O==null)return O;var de=[],X=0;return G(O,de,"","",function(se){return z.call(ue,se,X++)}),de}function W(O){if(O._status===-1){var z=O._result;z=z(),z.then(function(ue){(O._status===0||O._status===-1)&&(O._status=1,O._result=ue)},function(ue){(O._status===0||O._status===-1)&&(O._status=2,O._result=ue)}),O._status===-1&&(O._status=0,O._result=z)}if(O._status===1)return O._result.default;throw O._result}var Y={current:null},A={transition:null},F={ReactCurrentDispatcher:Y,ReactCurrentBatchConfig:A,ReactCurrentOwner:B};function U(){throw Error("act(...) is not supported in production builds of React.")}return Ie.Children={map:ae,forEach:function(O,z,ue){ae(O,function(){z.apply(this,arguments)},ue)},count:function(O){var z=0;return ae(O,function(){z++}),z},toArray:function(O){return ae(O,function(z){return z})||[]},only:function(O){if(!J(O))throw Error("React.Children.only expected to receive a single React element child.");return O}},Ie.Component=E,Ie.Fragment=r,Ie.Profiler=a,Ie.PureComponent=T,Ie.StrictMode=i,Ie.Suspense=g,Ie.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=F,Ie.act=U,Ie.cloneElement=function(O,z,ue){if(O==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+O+".");var de=P({},O.props),X=O.key,se=O.ref,Q=O._owner;if(z!=null){if(z.ref!==void 0&&(se=z.ref,Q=B.current),z.key!==void 0&&(X=""+z.key),O.type&&O.type.defaultProps)var oe=O.type.defaultProps;for(q in z)L.call(z,q)&&!N.hasOwnProperty(q)&&(de[q]=z[q]===void 0&&oe!==void 0?oe[q]:z[q])}var q=arguments.length-2;if(q===1)de.children=ue;else if(1<q){oe=Array(q);for(var fe=0;fe<q;fe++)oe[fe]=arguments[fe+2];de.children=oe}return{$$typeof:e,type:O.type,key:X,ref:se,props:de,_owner:Q}},Ie.createContext=function(O){return O={$$typeof:c,_currentValue:O,_currentValue2:O,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},O.Provider={$$typeof:l,_context:O},O.Consumer=O},Ie.createElement=b,Ie.createFactory=function(O){var z=b.bind(null,O);return z.type=O,z},Ie.createRef=function(){return{current:null}},Ie.forwardRef=function(O){return{$$typeof:d,render:O}},Ie.isValidElement=J,Ie.lazy=function(O){return{$$typeof:w,_payload:{_status:-1,_result:O},_init:W}},Ie.memo=function(O,z){return{$$typeof:v,type:O,compare:z===void 0?null:z}},Ie.startTransition=function(O){var z=A.transition;A.transition={};try{O()}finally{A.transition=z}},Ie.unstable_act=U,Ie.useCallback=function(O,z){return Y.current.useCallback(O,z)},Ie.useContext=function(O){return Y.current.useContext(O)},Ie.useDebugValue=function(){},Ie.useDeferredValue=function(O){return Y.current.useDeferredValue(O)},Ie.useEffect=function(O,z){return Y.current.useEffect(O,z)},Ie.useId=function(){return Y.current.useId()},Ie.useImperativeHandle=function(O,z,ue){return Y.current.useImperativeHandle(O,z,ue)},Ie.useInsertionEffect=function(O,z){return Y.current.useInsertionEffect(O,z)},Ie.useLayoutEffect=function(O,z){return Y.current.useLayoutEffect(O,z)},Ie.useMemo=function(O,z){return Y.current.useMemo(O,z)},Ie.useReducer=function(O,z,ue){return Y.current.useReducer(O,z,ue)},Ie.useRef=function(O){return Y.current.useRef(O)},Ie.useState=function(O){return Y.current.useState(O)},Ie.useSyncExternalStore=function(O,z,ue){return Y.current.useSyncExternalStore(O,z,ue)},Ie.useTransition=function(){return Y.current.useTransition()},Ie.version="18.3.1",Ie}var wg;function Ac(){return wg||(wg=1,vu.exports=IS()),vu.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sg;function DS(){if(Sg)return Qo;Sg=1;var e=Ac(),n=Symbol.for("react.element"),r=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,a=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function c(d,g,v){var w,m={},S=null,y=null;v!==void 0&&(S=""+v),g.key!==void 0&&(S=""+g.key),g.ref!==void 0&&(y=g.ref);for(w in g)i.call(g,w)&&!l.hasOwnProperty(w)&&(m[w]=g[w]);if(d&&d.defaultProps)for(w in g=d.defaultProps,g)m[w]===void 0&&(m[w]=g[w]);return{$$typeof:n,type:d,key:S,ref:y,props:m,_owner:a.current}}return Qo.Fragment=r,Qo.jsx=c,Qo.jsxs=c,Qo}var xg;function OS(){return xg||(xg=1,mu.exports=DS()),mu.exports}var R=OS(),p=Ac();const qe=lo(p),bc=TS({__proto__:null,default:qe},[p]);var Ps={},yu={exports:{}},Pt={},wu={exports:{}},Su={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cg;function MS(){return Cg||(Cg=1,function(e){function n(A,F){var U=A.length;A.push(F);e:for(;0<U;){var O=U-1>>>1,z=A[O];if(0<a(z,F))A[O]=F,A[U]=z,U=O;else break e}}function r(A){return A.length===0?null:A[0]}function i(A){if(A.length===0)return null;var F=A[0],U=A.pop();if(U!==F){A[0]=U;e:for(var O=0,z=A.length,ue=z>>>1;O<ue;){var de=2*(O+1)-1,X=A[de],se=de+1,Q=A[se];if(0>a(X,U))se<z&&0>a(Q,X)?(A[O]=Q,A[se]=U,O=se):(A[O]=X,A[de]=U,O=de);else if(se<z&&0>a(Q,U))A[O]=Q,A[se]=U,O=se;else break e}}return F}function a(A,F){var U=A.sortIndex-F.sortIndex;return U!==0?U:A.id-F.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var c=Date,d=c.now();e.unstable_now=function(){return c.now()-d}}var g=[],v=[],w=1,m=null,S=3,y=!1,P=!1,x=!1,E=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,T=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function I(A){for(var F=r(v);F!==null;){if(F.callback===null)i(v);else if(F.startTime<=A)i(v),F.sortIndex=F.expirationTime,n(g,F);else break;F=r(v)}}function $(A){if(x=!1,I(A),!P)if(r(g)!==null)P=!0,W(L);else{var F=r(v);F!==null&&Y($,F.startTime-A)}}function L(A,F){P=!1,x&&(x=!1,_(b),b=-1),y=!0;var U=S;try{for(I(F),m=r(g);m!==null&&(!(m.expirationTime>F)||A&&!ne());){var O=m.callback;if(typeof O=="function"){m.callback=null,S=m.priorityLevel;var z=O(m.expirationTime<=F);F=e.unstable_now(),typeof z=="function"?m.callback=z:m===r(g)&&i(g),I(F)}else i(g);m=r(g)}if(m!==null)var ue=!0;else{var de=r(v);de!==null&&Y($,de.startTime-F),ue=!1}return ue}finally{m=null,S=U,y=!1}}var B=!1,N=null,b=-1,K=5,J=-1;function ne(){return!(e.unstable_now()-J<K)}function te(){if(N!==null){var A=e.unstable_now();J=A;var F=!0;try{F=N(!0,A)}finally{F?Z():(B=!1,N=null)}}else B=!1}var Z;if(typeof T=="function")Z=function(){T(te)};else if(typeof MessageChannel<"u"){var G=new MessageChannel,ae=G.port2;G.port1.onmessage=te,Z=function(){ae.postMessage(null)}}else Z=function(){E(te,0)};function W(A){N=A,B||(B=!0,Z())}function Y(A,F){b=E(function(){A(e.unstable_now())},F)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(A){A.callback=null},e.unstable_continueExecution=function(){P||y||(P=!0,W(L))},e.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):K=0<A?Math.floor(1e3/A):5},e.unstable_getCurrentPriorityLevel=function(){return S},e.unstable_getFirstCallbackNode=function(){return r(g)},e.unstable_next=function(A){switch(S){case 1:case 2:case 3:var F=3;break;default:F=S}var U=S;S=F;try{return A()}finally{S=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(A,F){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var U=S;S=A;try{return F()}finally{S=U}},e.unstable_scheduleCallback=function(A,F,U){var O=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?O+U:O):U=O,A){case 1:var z=-1;break;case 2:z=250;break;case 5:z=**********;break;case 4:z=1e4;break;default:z=5e3}return z=U+z,A={id:w++,callback:F,priorityLevel:A,startTime:U,expirationTime:z,sortIndex:-1},U>O?(A.sortIndex=U,n(v,A),r(g)===null&&A===r(v)&&(x?(_(b),b=-1):x=!0,Y($,U-O))):(A.sortIndex=z,n(g,A),P||y||(P=!0,W(L))),A},e.unstable_shouldYield=ne,e.unstable_wrapCallback=function(A){var F=S;return function(){var U=S;S=F;try{return A.apply(this,arguments)}finally{S=U}}}}(Su)),Su}var Eg;function kS(){return Eg||(Eg=1,wu.exports=MS()),wu.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rg;function $S(){if(Rg)return Pt;Rg=1;var e=Ac(),n=kS();function r(t){for(var o="https://reactjs.org/docs/error-decoder.html?invariant="+t,s=1;s<arguments.length;s++)o+="&args[]="+encodeURIComponent(arguments[s]);return"Minified React error #"+t+"; visit "+o+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,a={};function l(t,o){c(t,o),c(t+"Capture",o)}function c(t,o){for(a[t]=o,t=0;t<o.length;t++)i.add(o[t])}var d=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),g=Object.prototype.hasOwnProperty,v=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,w={},m={};function S(t){return g.call(m,t)?!0:g.call(w,t)?!1:v.test(t)?m[t]=!0:(w[t]=!0,!1)}function y(t,o,s,u){if(s!==null&&s.type===0)return!1;switch(typeof o){case"function":case"symbol":return!0;case"boolean":return u?!1:s!==null?!s.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function P(t,o,s,u){if(o===null||typeof o>"u"||y(t,o,s,u))return!0;if(u)return!1;if(s!==null)switch(s.type){case 3:return!o;case 4:return o===!1;case 5:return isNaN(o);case 6:return isNaN(o)||1>o}return!1}function x(t,o,s,u,f,h,C){this.acceptsBooleans=o===2||o===3||o===4,this.attributeName=u,this.attributeNamespace=f,this.mustUseProperty=s,this.propertyName=t,this.type=o,this.sanitizeURL=h,this.removeEmptyString=C}var E={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){E[t]=new x(t,0,!1,t,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var o=t[0];E[o]=new x(o,1,!1,t[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(t){E[t]=new x(t,2,!1,t.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){E[t]=new x(t,2,!1,t,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){E[t]=new x(t,3,!1,t.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(t){E[t]=new x(t,3,!0,t,null,!1,!1)}),["capture","download"].forEach(function(t){E[t]=new x(t,4,!1,t,null,!1,!1)}),["cols","rows","size","span"].forEach(function(t){E[t]=new x(t,6,!1,t,null,!1,!1)}),["rowSpan","start"].forEach(function(t){E[t]=new x(t,5,!1,t.toLowerCase(),null,!1,!1)});var _=/[\-:]([a-z])/g;function T(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var o=t.replace(_,T);E[o]=new x(o,1,!1,t,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var o=t.replace(_,T);E[o]=new x(o,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(t){var o=t.replace(_,T);E[o]=new x(o,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(t){E[t]=new x(t,1,!1,t.toLowerCase(),null,!1,!1)}),E.xlinkHref=new x("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(t){E[t]=new x(t,1,!1,t.toLowerCase(),null,!0,!0)});function I(t,o,s,u){var f=E.hasOwnProperty(o)?E[o]:null;(f!==null?f.type!==0:u||!(2<o.length)||o[0]!=="o"&&o[0]!=="O"||o[1]!=="n"&&o[1]!=="N")&&(P(o,s,f,u)&&(s=null),u||f===null?S(o)&&(s===null?t.removeAttribute(o):t.setAttribute(o,""+s)):f.mustUseProperty?t[f.propertyName]=s===null?f.type===3?!1:"":s:(o=f.attributeName,u=f.attributeNamespace,s===null?t.removeAttribute(o):(f=f.type,s=f===3||f===4&&s===!0?"":""+s,u?t.setAttributeNS(u,o,s):t.setAttribute(o,s))))}var $=e.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,L=Symbol.for("react.element"),B=Symbol.for("react.portal"),N=Symbol.for("react.fragment"),b=Symbol.for("react.strict_mode"),K=Symbol.for("react.profiler"),J=Symbol.for("react.provider"),ne=Symbol.for("react.context"),te=Symbol.for("react.forward_ref"),Z=Symbol.for("react.suspense"),G=Symbol.for("react.suspense_list"),ae=Symbol.for("react.memo"),W=Symbol.for("react.lazy"),Y=Symbol.for("react.offscreen"),A=Symbol.iterator;function F(t){return t===null||typeof t!="object"?null:(t=A&&t[A]||t["@@iterator"],typeof t=="function"?t:null)}var U=Object.assign,O;function z(t){if(O===void 0)try{throw Error()}catch(s){var o=s.stack.trim().match(/\n( *(at )?)/);O=o&&o[1]||""}return`
`+O+t}var ue=!1;function de(t,o){if(!t||ue)return"";ue=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(o)if(o=function(){throw Error()},Object.defineProperty(o.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(o,[])}catch(H){var u=H}Reflect.construct(t,[],o)}else{try{o.call()}catch(H){u=H}t.call(o.prototype)}else{try{throw Error()}catch(H){u=H}t()}}catch(H){if(H&&u&&typeof H.stack=="string"){for(var f=H.stack.split(`
`),h=u.stack.split(`
`),C=f.length-1,D=h.length-1;1<=C&&0<=D&&f[C]!==h[D];)D--;for(;1<=C&&0<=D;C--,D--)if(f[C]!==h[D]){if(C!==1||D!==1)do if(C--,D--,0>D||f[C]!==h[D]){var M=`
`+f[C].replace(" at new "," at ");return t.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",t.displayName)),M}while(1<=C&&0<=D);break}}}finally{ue=!1,Error.prepareStackTrace=s}return(t=t?t.displayName||t.name:"")?z(t):""}function X(t){switch(t.tag){case 5:return z(t.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return t=de(t.type,!1),t;case 11:return t=de(t.type.render,!1),t;case 1:return t=de(t.type,!0),t;default:return""}}function se(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case N:return"Fragment";case B:return"Portal";case K:return"Profiler";case b:return"StrictMode";case Z:return"Suspense";case G:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case ne:return(t.displayName||"Context")+".Consumer";case J:return(t._context.displayName||"Context")+".Provider";case te:var o=t.render;return t=t.displayName,t||(t=o.displayName||o.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case ae:return o=t.displayName||null,o!==null?o:se(t.type)||"Memo";case W:o=t._payload,t=t._init;try{return se(t(o))}catch{}}return null}function Q(t){var o=t.type;switch(t.tag){case 24:return"Cache";case 9:return(o.displayName||"Context")+".Consumer";case 10:return(o._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=o.render,t=t.displayName||t.name||"",o.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return o;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return se(o);case 8:return o===b?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof o=="function")return o.displayName||o.name||null;if(typeof o=="string")return o}return null}function oe(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function q(t){var o=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(o==="checkbox"||o==="radio")}function fe(t){var o=q(t)?"checked":"value",s=Object.getOwnPropertyDescriptor(t.constructor.prototype,o),u=""+t[o];if(!t.hasOwnProperty(o)&&typeof s<"u"&&typeof s.get=="function"&&typeof s.set=="function"){var f=s.get,h=s.set;return Object.defineProperty(t,o,{configurable:!0,get:function(){return f.call(this)},set:function(C){u=""+C,h.call(this,C)}}),Object.defineProperty(t,o,{enumerable:s.enumerable}),{getValue:function(){return u},setValue:function(C){u=""+C},stopTracking:function(){t._valueTracker=null,delete t[o]}}}}function pe(t){t._valueTracker||(t._valueTracker=fe(t))}function Pe(t){if(!t)return!1;var o=t._valueTracker;if(!o)return!0;var s=o.getValue(),u="";return t&&(u=q(t)?t.checked?"true":"false":t.value),t=u,t!==s?(o.setValue(t),!0):!1}function we(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}function ce(t,o){var s=o.checked;return U({},o,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:s??t._wrapperState.initialChecked})}function Ne(t,o){var s=o.defaultValue==null?"":o.defaultValue,u=o.checked!=null?o.checked:o.defaultChecked;s=oe(o.value!=null?o.value:s),t._wrapperState={initialChecked:u,initialValue:s,controlled:o.type==="checkbox"||o.type==="radio"?o.checked!=null:o.value!=null}}function nt(t,o){o=o.checked,o!=null&&I(t,"checked",o,!1)}function rt(t,o){nt(t,o);var s=oe(o.value),u=o.type;if(s!=null)u==="number"?(s===0&&t.value===""||t.value!=s)&&(t.value=""+s):t.value!==""+s&&(t.value=""+s);else if(u==="submit"||u==="reset"){t.removeAttribute("value");return}o.hasOwnProperty("value")?st(t,o.type,s):o.hasOwnProperty("defaultValue")&&st(t,o.type,oe(o.defaultValue)),o.checked==null&&o.defaultChecked!=null&&(t.defaultChecked=!!o.defaultChecked)}function rn(t,o,s){if(o.hasOwnProperty("value")||o.hasOwnProperty("defaultValue")){var u=o.type;if(!(u!=="submit"&&u!=="reset"||o.value!==void 0&&o.value!==null))return;o=""+t._wrapperState.initialValue,s||o===t.value||(t.value=o),t.defaultValue=o}s=t.name,s!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,s!==""&&(t.name=s)}function st(t,o,s){(o!=="number"||we(t.ownerDocument)!==t)&&(s==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+s&&(t.defaultValue=""+s))}var It=Array.isArray;function pn(t,o,s,u){if(t=t.options,o){o={};for(var f=0;f<s.length;f++)o["$"+s[f]]=!0;for(s=0;s<t.length;s++)f=o.hasOwnProperty("$"+t[s].value),t[s].selected!==f&&(t[s].selected=f),f&&u&&(t[s].defaultSelected=!0)}else{for(s=""+oe(s),o=null,f=0;f<t.length;f++){if(t[f].value===s){t[f].selected=!0,u&&(t[f].defaultSelected=!0);return}o!==null||t[f].disabled||(o=t[f])}o!==null&&(o.selected=!0)}}function Rr(t,o){if(o.dangerouslySetInnerHTML!=null)throw Error(r(91));return U({},o,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function Ue(t,o){var s=o.value;if(s==null){if(s=o.children,o=o.defaultValue,s!=null){if(o!=null)throw Error(r(92));if(It(s)){if(1<s.length)throw Error(r(93));s=s[0]}o=s}o==null&&(o=""),s=o}t._wrapperState={initialValue:oe(s)}}function Qe(t,o){var s=oe(o.value),u=oe(o.defaultValue);s!=null&&(s=""+s,s!==t.value&&(t.value=s),o.defaultValue==null&&t.defaultValue!==s&&(t.defaultValue=s)),u!=null&&(t.defaultValue=""+u)}function mi(t){var o=t.textContent;o===t._wrapperState.initialValue&&o!==""&&o!==null&&(t.value=o)}function Od(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Tl(t,o){return t==null||t==="http://www.w3.org/1999/xhtml"?Od(o):t==="http://www.w3.org/2000/svg"&&o==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var vi,Md=function(t){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(o,s,u,f){MSApp.execUnsafeLocalFunction(function(){return t(o,s,u,f)})}:t}(function(t,o){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=o;else{for(vi=vi||document.createElement("div"),vi.innerHTML="<svg>"+o.valueOf().toString()+"</svg>",o=vi.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;o.firstChild;)t.appendChild(o.firstChild)}});function po(t,o){if(o){var s=t.firstChild;if(s&&s===t.lastChild&&s.nodeType===3){s.nodeValue=o;return}}t.textContent=o}var go={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Mw=["Webkit","ms","Moz","O"];Object.keys(go).forEach(function(t){Mw.forEach(function(o){o=o+t.charAt(0).toUpperCase()+t.substring(1),go[o]=go[t]})});function kd(t,o,s){return o==null||typeof o=="boolean"||o===""?"":s||typeof o!="number"||o===0||go.hasOwnProperty(t)&&go[t]?(""+o).trim():o+"px"}function $d(t,o){t=t.style;for(var s in o)if(o.hasOwnProperty(s)){var u=s.indexOf("--")===0,f=kd(s,o[s],u);s==="float"&&(s="cssFloat"),u?t.setProperty(s,f):t[s]=f}}var kw=U({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Il(t,o){if(o){if(kw[t]&&(o.children!=null||o.dangerouslySetInnerHTML!=null))throw Error(r(137,t));if(o.dangerouslySetInnerHTML!=null){if(o.children!=null)throw Error(r(60));if(typeof o.dangerouslySetInnerHTML!="object"||!("__html"in o.dangerouslySetInnerHTML))throw Error(r(61))}if(o.style!=null&&typeof o.style!="object")throw Error(r(62))}}function Dl(t,o){if(t.indexOf("-")===-1)return typeof o.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ol=null;function Ml(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var kl=null,Pr=null,_r=null;function Nd(t){if(t=bo(t)){if(typeof kl!="function")throw Error(r(280));var o=t.stateNode;o&&(o=Vi(o),kl(t.stateNode,t.type,o))}}function Ad(t){Pr?_r?_r.push(t):_r=[t]:Pr=t}function bd(){if(Pr){var t=Pr,o=_r;if(_r=Pr=null,Nd(t),o)for(t=0;t<o.length;t++)Nd(o[t])}}function Ld(t,o){return t(o)}function Fd(){}var $l=!1;function jd(t,o,s){if($l)return t(o,s);$l=!0;try{return Ld(t,o,s)}finally{$l=!1,(Pr!==null||_r!==null)&&(Fd(),bd())}}function ho(t,o){var s=t.stateNode;if(s===null)return null;var u=Vi(s);if(u===null)return null;s=u[o];e:switch(o){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(t=t.type,u=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!u;break e;default:t=!1}if(t)return null;if(s&&typeof s!="function")throw Error(r(231,o,typeof s));return s}var Nl=!1;if(d)try{var mo={};Object.defineProperty(mo,"passive",{get:function(){Nl=!0}}),window.addEventListener("test",mo,mo),window.removeEventListener("test",mo,mo)}catch{Nl=!1}function $w(t,o,s,u,f,h,C,D,M){var H=Array.prototype.slice.call(arguments,3);try{o.apply(s,H)}catch(re){this.onError(re)}}var vo=!1,yi=null,wi=!1,Al=null,Nw={onError:function(t){vo=!0,yi=t}};function Aw(t,o,s,u,f,h,C,D,M){vo=!1,yi=null,$w.apply(Nw,arguments)}function bw(t,o,s,u,f,h,C,D,M){if(Aw.apply(this,arguments),vo){if(vo){var H=yi;vo=!1,yi=null}else throw Error(r(198));wi||(wi=!0,Al=H)}}function nr(t){var o=t,s=t;if(t.alternate)for(;o.return;)o=o.return;else{t=o;do o=t,(o.flags&4098)!==0&&(s=o.return),t=o.return;while(t)}return o.tag===3?s:null}function Vd(t){if(t.tag===13){var o=t.memoizedState;if(o===null&&(t=t.alternate,t!==null&&(o=t.memoizedState)),o!==null)return o.dehydrated}return null}function zd(t){if(nr(t)!==t)throw Error(r(188))}function Lw(t){var o=t.alternate;if(!o){if(o=nr(t),o===null)throw Error(r(188));return o!==t?null:t}for(var s=t,u=o;;){var f=s.return;if(f===null)break;var h=f.alternate;if(h===null){if(u=f.return,u!==null){s=u;continue}break}if(f.child===h.child){for(h=f.child;h;){if(h===s)return zd(f),t;if(h===u)return zd(f),o;h=h.sibling}throw Error(r(188))}if(s.return!==u.return)s=f,u=h;else{for(var C=!1,D=f.child;D;){if(D===s){C=!0,s=f,u=h;break}if(D===u){C=!0,u=f,s=h;break}D=D.sibling}if(!C){for(D=h.child;D;){if(D===s){C=!0,s=h,u=f;break}if(D===u){C=!0,u=h,s=f;break}D=D.sibling}if(!C)throw Error(r(189))}}if(s.alternate!==u)throw Error(r(190))}if(s.tag!==3)throw Error(r(188));return s.stateNode.current===s?t:o}function Hd(t){return t=Lw(t),t!==null?Ud(t):null}function Ud(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var o=Ud(t);if(o!==null)return o;t=t.sibling}return null}var Bd=n.unstable_scheduleCallback,Wd=n.unstable_cancelCallback,Fw=n.unstable_shouldYield,jw=n.unstable_requestPaint,Be=n.unstable_now,Vw=n.unstable_getCurrentPriorityLevel,bl=n.unstable_ImmediatePriority,Gd=n.unstable_UserBlockingPriority,Si=n.unstable_NormalPriority,zw=n.unstable_LowPriority,Kd=n.unstable_IdlePriority,xi=null,on=null;function Hw(t){if(on&&typeof on.onCommitFiberRoot=="function")try{on.onCommitFiberRoot(xi,t,void 0,(t.current.flags&128)===128)}catch{}}var Gt=Math.clz32?Math.clz32:Ww,Uw=Math.log,Bw=Math.LN2;function Ww(t){return t>>>=0,t===0?32:31-(Uw(t)/Bw|0)|0}var Ci=64,Ei=4194304;function yo(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function Ri(t,o){var s=t.pendingLanes;if(s===0)return 0;var u=0,f=t.suspendedLanes,h=t.pingedLanes,C=s&268435455;if(C!==0){var D=C&~f;D!==0?u=yo(D):(h&=C,h!==0&&(u=yo(h)))}else C=s&~f,C!==0?u=yo(C):h!==0&&(u=yo(h));if(u===0)return 0;if(o!==0&&o!==u&&(o&f)===0&&(f=u&-u,h=o&-o,f>=h||f===16&&(h&4194240)!==0))return o;if((u&4)!==0&&(u|=s&16),o=t.entangledLanes,o!==0)for(t=t.entanglements,o&=u;0<o;)s=31-Gt(o),f=1<<s,u|=t[s],o&=~f;return u}function Gw(t,o){switch(t){case 1:case 2:case 4:return o+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return o+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Kw(t,o){for(var s=t.suspendedLanes,u=t.pingedLanes,f=t.expirationTimes,h=t.pendingLanes;0<h;){var C=31-Gt(h),D=1<<C,M=f[C];M===-1?((D&s)===0||(D&u)!==0)&&(f[C]=Gw(D,o)):M<=o&&(t.expiredLanes|=D),h&=~D}}function Ll(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function Xd(){var t=Ci;return Ci<<=1,(Ci&4194240)===0&&(Ci=64),t}function Fl(t){for(var o=[],s=0;31>s;s++)o.push(t);return o}function wo(t,o,s){t.pendingLanes|=o,o!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,o=31-Gt(o),t[o]=s}function Xw(t,o){var s=t.pendingLanes&~o;t.pendingLanes=o,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=o,t.mutableReadLanes&=o,t.entangledLanes&=o,o=t.entanglements;var u=t.eventTimes;for(t=t.expirationTimes;0<s;){var f=31-Gt(s),h=1<<f;o[f]=0,u[f]=-1,t[f]=-1,s&=~h}}function jl(t,o){var s=t.entangledLanes|=o;for(t=t.entanglements;s;){var u=31-Gt(s),f=1<<u;f&o|t[u]&o&&(t[u]|=o),s&=~f}}var ke=0;function qd(t){return t&=-t,1<t?4<t?(t&268435455)!==0?16:536870912:4:1}var Yd,Vl,Qd,Zd,Jd,zl=!1,Pi=[],On=null,Mn=null,kn=null,So=new Map,xo=new Map,$n=[],qw="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ef(t,o){switch(t){case"focusin":case"focusout":On=null;break;case"dragenter":case"dragleave":Mn=null;break;case"mouseover":case"mouseout":kn=null;break;case"pointerover":case"pointerout":So.delete(o.pointerId);break;case"gotpointercapture":case"lostpointercapture":xo.delete(o.pointerId)}}function Co(t,o,s,u,f,h){return t===null||t.nativeEvent!==h?(t={blockedOn:o,domEventName:s,eventSystemFlags:u,nativeEvent:h,targetContainers:[f]},o!==null&&(o=bo(o),o!==null&&Vl(o)),t):(t.eventSystemFlags|=u,o=t.targetContainers,f!==null&&o.indexOf(f)===-1&&o.push(f),t)}function Yw(t,o,s,u,f){switch(o){case"focusin":return On=Co(On,t,o,s,u,f),!0;case"dragenter":return Mn=Co(Mn,t,o,s,u,f),!0;case"mouseover":return kn=Co(kn,t,o,s,u,f),!0;case"pointerover":var h=f.pointerId;return So.set(h,Co(So.get(h)||null,t,o,s,u,f)),!0;case"gotpointercapture":return h=f.pointerId,xo.set(h,Co(xo.get(h)||null,t,o,s,u,f)),!0}return!1}function tf(t){var o=rr(t.target);if(o!==null){var s=nr(o);if(s!==null){if(o=s.tag,o===13){if(o=Vd(s),o!==null){t.blockedOn=o,Jd(t.priority,function(){Qd(s)});return}}else if(o===3&&s.stateNode.current.memoizedState.isDehydrated){t.blockedOn=s.tag===3?s.stateNode.containerInfo:null;return}}}t.blockedOn=null}function _i(t){if(t.blockedOn!==null)return!1;for(var o=t.targetContainers;0<o.length;){var s=Ul(t.domEventName,t.eventSystemFlags,o[0],t.nativeEvent);if(s===null){s=t.nativeEvent;var u=new s.constructor(s.type,s);Ol=u,s.target.dispatchEvent(u),Ol=null}else return o=bo(s),o!==null&&Vl(o),t.blockedOn=s,!1;o.shift()}return!0}function nf(t,o,s){_i(t)&&s.delete(o)}function Qw(){zl=!1,On!==null&&_i(On)&&(On=null),Mn!==null&&_i(Mn)&&(Mn=null),kn!==null&&_i(kn)&&(kn=null),So.forEach(nf),xo.forEach(nf)}function Eo(t,o){t.blockedOn===o&&(t.blockedOn=null,zl||(zl=!0,n.unstable_scheduleCallback(n.unstable_NormalPriority,Qw)))}function Ro(t){function o(f){return Eo(f,t)}if(0<Pi.length){Eo(Pi[0],t);for(var s=1;s<Pi.length;s++){var u=Pi[s];u.blockedOn===t&&(u.blockedOn=null)}}for(On!==null&&Eo(On,t),Mn!==null&&Eo(Mn,t),kn!==null&&Eo(kn,t),So.forEach(o),xo.forEach(o),s=0;s<$n.length;s++)u=$n[s],u.blockedOn===t&&(u.blockedOn=null);for(;0<$n.length&&(s=$n[0],s.blockedOn===null);)tf(s),s.blockedOn===null&&$n.shift()}var Tr=$.ReactCurrentBatchConfig,Ti=!0;function Zw(t,o,s,u){var f=ke,h=Tr.transition;Tr.transition=null;try{ke=1,Hl(t,o,s,u)}finally{ke=f,Tr.transition=h}}function Jw(t,o,s,u){var f=ke,h=Tr.transition;Tr.transition=null;try{ke=4,Hl(t,o,s,u)}finally{ke=f,Tr.transition=h}}function Hl(t,o,s,u){if(Ti){var f=Ul(t,o,s,u);if(f===null)sa(t,o,u,Ii,s),ef(t,u);else if(Yw(f,t,o,s,u))u.stopPropagation();else if(ef(t,u),o&4&&-1<qw.indexOf(t)){for(;f!==null;){var h=bo(f);if(h!==null&&Yd(h),h=Ul(t,o,s,u),h===null&&sa(t,o,u,Ii,s),h===f)break;f=h}f!==null&&u.stopPropagation()}else sa(t,o,u,null,s)}}var Ii=null;function Ul(t,o,s,u){if(Ii=null,t=Ml(u),t=rr(t),t!==null)if(o=nr(t),o===null)t=null;else if(s=o.tag,s===13){if(t=Vd(o),t!==null)return t;t=null}else if(s===3){if(o.stateNode.current.memoizedState.isDehydrated)return o.tag===3?o.stateNode.containerInfo:null;t=null}else o!==t&&(t=null);return Ii=t,null}function rf(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Vw()){case bl:return 1;case Gd:return 4;case Si:case zw:return 16;case Kd:return 536870912;default:return 16}default:return 16}}var Nn=null,Bl=null,Di=null;function of(){if(Di)return Di;var t,o=Bl,s=o.length,u,f="value"in Nn?Nn.value:Nn.textContent,h=f.length;for(t=0;t<s&&o[t]===f[t];t++);var C=s-t;for(u=1;u<=C&&o[s-u]===f[h-u];u++);return Di=f.slice(t,1<u?1-u:void 0)}function Oi(t){var o=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&o===13&&(t=13)):t=o,t===10&&(t=13),32<=t||t===13?t:0}function Mi(){return!0}function sf(){return!1}function Dt(t){function o(s,u,f,h,C){this._reactName=s,this._targetInst=f,this.type=u,this.nativeEvent=h,this.target=C,this.currentTarget=null;for(var D in t)t.hasOwnProperty(D)&&(s=t[D],this[D]=s?s(h):h[D]);return this.isDefaultPrevented=(h.defaultPrevented!=null?h.defaultPrevented:h.returnValue===!1)?Mi:sf,this.isPropagationStopped=sf,this}return U(o.prototype,{preventDefault:function(){this.defaultPrevented=!0;var s=this.nativeEvent;s&&(s.preventDefault?s.preventDefault():typeof s.returnValue!="unknown"&&(s.returnValue=!1),this.isDefaultPrevented=Mi)},stopPropagation:function(){var s=this.nativeEvent;s&&(s.stopPropagation?s.stopPropagation():typeof s.cancelBubble!="unknown"&&(s.cancelBubble=!0),this.isPropagationStopped=Mi)},persist:function(){},isPersistent:Mi}),o}var Ir={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Wl=Dt(Ir),Po=U({},Ir,{view:0,detail:0}),e0=Dt(Po),Gl,Kl,_o,ki=U({},Po,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ql,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==_o&&(_o&&t.type==="mousemove"?(Gl=t.screenX-_o.screenX,Kl=t.screenY-_o.screenY):Kl=Gl=0,_o=t),Gl)},movementY:function(t){return"movementY"in t?t.movementY:Kl}}),lf=Dt(ki),t0=U({},ki,{dataTransfer:0}),n0=Dt(t0),r0=U({},Po,{relatedTarget:0}),Xl=Dt(r0),o0=U({},Ir,{animationName:0,elapsedTime:0,pseudoElement:0}),i0=Dt(o0),s0=U({},Ir,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),l0=Dt(s0),a0=U({},Ir,{data:0}),af=Dt(a0),u0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},c0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},d0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function f0(t){var o=this.nativeEvent;return o.getModifierState?o.getModifierState(t):(t=d0[t])?!!o[t]:!1}function ql(){return f0}var p0=U({},Po,{key:function(t){if(t.key){var o=u0[t.key]||t.key;if(o!=="Unidentified")return o}return t.type==="keypress"?(t=Oi(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?c0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ql,charCode:function(t){return t.type==="keypress"?Oi(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Oi(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),g0=Dt(p0),h0=U({},ki,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),uf=Dt(h0),m0=U({},Po,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ql}),v0=Dt(m0),y0=U({},Ir,{propertyName:0,elapsedTime:0,pseudoElement:0}),w0=Dt(y0),S0=U({},ki,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),x0=Dt(S0),C0=[9,13,27,32],Yl=d&&"CompositionEvent"in window,To=null;d&&"documentMode"in document&&(To=document.documentMode);var E0=d&&"TextEvent"in window&&!To,cf=d&&(!Yl||To&&8<To&&11>=To),df=" ",ff=!1;function pf(t,o){switch(t){case"keyup":return C0.indexOf(o.keyCode)!==-1;case"keydown":return o.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function gf(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Dr=!1;function R0(t,o){switch(t){case"compositionend":return gf(o);case"keypress":return o.which!==32?null:(ff=!0,df);case"textInput":return t=o.data,t===df&&ff?null:t;default:return null}}function P0(t,o){if(Dr)return t==="compositionend"||!Yl&&pf(t,o)?(t=of(),Di=Bl=Nn=null,Dr=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(o.ctrlKey||o.altKey||o.metaKey)||o.ctrlKey&&o.altKey){if(o.char&&1<o.char.length)return o.char;if(o.which)return String.fromCharCode(o.which)}return null;case"compositionend":return cf&&o.locale!=="ko"?null:o.data;default:return null}}var _0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hf(t){var o=t&&t.nodeName&&t.nodeName.toLowerCase();return o==="input"?!!_0[t.type]:o==="textarea"}function mf(t,o,s,u){Ad(u),o=Li(o,"onChange"),0<o.length&&(s=new Wl("onChange","change",null,s,u),t.push({event:s,listeners:o}))}var Io=null,Do=null;function T0(t){Nf(t,0)}function $i(t){var o=Nr(t);if(Pe(o))return t}function I0(t,o){if(t==="change")return o}var vf=!1;if(d){var Ql;if(d){var Zl="oninput"in document;if(!Zl){var yf=document.createElement("div");yf.setAttribute("oninput","return;"),Zl=typeof yf.oninput=="function"}Ql=Zl}else Ql=!1;vf=Ql&&(!document.documentMode||9<document.documentMode)}function wf(){Io&&(Io.detachEvent("onpropertychange",Sf),Do=Io=null)}function Sf(t){if(t.propertyName==="value"&&$i(Do)){var o=[];mf(o,Do,t,Ml(t)),jd(T0,o)}}function D0(t,o,s){t==="focusin"?(wf(),Io=o,Do=s,Io.attachEvent("onpropertychange",Sf)):t==="focusout"&&wf()}function O0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return $i(Do)}function M0(t,o){if(t==="click")return $i(o)}function k0(t,o){if(t==="input"||t==="change")return $i(o)}function $0(t,o){return t===o&&(t!==0||1/t===1/o)||t!==t&&o!==o}var Kt=typeof Object.is=="function"?Object.is:$0;function Oo(t,o){if(Kt(t,o))return!0;if(typeof t!="object"||t===null||typeof o!="object"||o===null)return!1;var s=Object.keys(t),u=Object.keys(o);if(s.length!==u.length)return!1;for(u=0;u<s.length;u++){var f=s[u];if(!g.call(o,f)||!Kt(t[f],o[f]))return!1}return!0}function xf(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function Cf(t,o){var s=xf(t);t=0;for(var u;s;){if(s.nodeType===3){if(u=t+s.textContent.length,t<=o&&u>=o)return{node:s,offset:o-t};t=u}e:{for(;s;){if(s.nextSibling){s=s.nextSibling;break e}s=s.parentNode}s=void 0}s=xf(s)}}function Ef(t,o){return t&&o?t===o?!0:t&&t.nodeType===3?!1:o&&o.nodeType===3?Ef(t,o.parentNode):"contains"in t?t.contains(o):t.compareDocumentPosition?!!(t.compareDocumentPosition(o)&16):!1:!1}function Rf(){for(var t=window,o=we();o instanceof t.HTMLIFrameElement;){try{var s=typeof o.contentWindow.location.href=="string"}catch{s=!1}if(s)t=o.contentWindow;else break;o=we(t.document)}return o}function Jl(t){var o=t&&t.nodeName&&t.nodeName.toLowerCase();return o&&(o==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||o==="textarea"||t.contentEditable==="true")}function N0(t){var o=Rf(),s=t.focusedElem,u=t.selectionRange;if(o!==s&&s&&s.ownerDocument&&Ef(s.ownerDocument.documentElement,s)){if(u!==null&&Jl(s)){if(o=u.start,t=u.end,t===void 0&&(t=o),"selectionStart"in s)s.selectionStart=o,s.selectionEnd=Math.min(t,s.value.length);else if(t=(o=s.ownerDocument||document)&&o.defaultView||window,t.getSelection){t=t.getSelection();var f=s.textContent.length,h=Math.min(u.start,f);u=u.end===void 0?h:Math.min(u.end,f),!t.extend&&h>u&&(f=u,u=h,h=f),f=Cf(s,h);var C=Cf(s,u);f&&C&&(t.rangeCount!==1||t.anchorNode!==f.node||t.anchorOffset!==f.offset||t.focusNode!==C.node||t.focusOffset!==C.offset)&&(o=o.createRange(),o.setStart(f.node,f.offset),t.removeAllRanges(),h>u?(t.addRange(o),t.extend(C.node,C.offset)):(o.setEnd(C.node,C.offset),t.addRange(o)))}}for(o=[],t=s;t=t.parentNode;)t.nodeType===1&&o.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof s.focus=="function"&&s.focus(),s=0;s<o.length;s++)t=o[s],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var A0=d&&"documentMode"in document&&11>=document.documentMode,Or=null,ea=null,Mo=null,ta=!1;function Pf(t,o,s){var u=s.window===s?s.document:s.nodeType===9?s:s.ownerDocument;ta||Or==null||Or!==we(u)||(u=Or,"selectionStart"in u&&Jl(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),Mo&&Oo(Mo,u)||(Mo=u,u=Li(ea,"onSelect"),0<u.length&&(o=new Wl("onSelect","select",null,o,s),t.push({event:o,listeners:u}),o.target=Or)))}function Ni(t,o){var s={};return s[t.toLowerCase()]=o.toLowerCase(),s["Webkit"+t]="webkit"+o,s["Moz"+t]="moz"+o,s}var Mr={animationend:Ni("Animation","AnimationEnd"),animationiteration:Ni("Animation","AnimationIteration"),animationstart:Ni("Animation","AnimationStart"),transitionend:Ni("Transition","TransitionEnd")},na={},_f={};d&&(_f=document.createElement("div").style,"AnimationEvent"in window||(delete Mr.animationend.animation,delete Mr.animationiteration.animation,delete Mr.animationstart.animation),"TransitionEvent"in window||delete Mr.transitionend.transition);function Ai(t){if(na[t])return na[t];if(!Mr[t])return t;var o=Mr[t],s;for(s in o)if(o.hasOwnProperty(s)&&s in _f)return na[t]=o[s];return t}var Tf=Ai("animationend"),If=Ai("animationiteration"),Df=Ai("animationstart"),Of=Ai("transitionend"),Mf=new Map,kf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function An(t,o){Mf.set(t,o),l(o,[t])}for(var ra=0;ra<kf.length;ra++){var oa=kf[ra],b0=oa.toLowerCase(),L0=oa[0].toUpperCase()+oa.slice(1);An(b0,"on"+L0)}An(Tf,"onAnimationEnd"),An(If,"onAnimationIteration"),An(Df,"onAnimationStart"),An("dblclick","onDoubleClick"),An("focusin","onFocus"),An("focusout","onBlur"),An(Of,"onTransitionEnd"),c("onMouseEnter",["mouseout","mouseover"]),c("onMouseLeave",["mouseout","mouseover"]),c("onPointerEnter",["pointerout","pointerover"]),c("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ko="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),F0=new Set("cancel close invalid load scroll toggle".split(" ").concat(ko));function $f(t,o,s){var u=t.type||"unknown-event";t.currentTarget=s,bw(u,o,void 0,t),t.currentTarget=null}function Nf(t,o){o=(o&4)!==0;for(var s=0;s<t.length;s++){var u=t[s],f=u.event;u=u.listeners;e:{var h=void 0;if(o)for(var C=u.length-1;0<=C;C--){var D=u[C],M=D.instance,H=D.currentTarget;if(D=D.listener,M!==h&&f.isPropagationStopped())break e;$f(f,D,H),h=M}else for(C=0;C<u.length;C++){if(D=u[C],M=D.instance,H=D.currentTarget,D=D.listener,M!==h&&f.isPropagationStopped())break e;$f(f,D,H),h=M}}}if(wi)throw t=Al,wi=!1,Al=null,t}function be(t,o){var s=o[fa];s===void 0&&(s=o[fa]=new Set);var u=t+"__bubble";s.has(u)||(Af(o,t,2,!1),s.add(u))}function ia(t,o,s){var u=0;o&&(u|=4),Af(s,t,u,o)}var bi="_reactListening"+Math.random().toString(36).slice(2);function $o(t){if(!t[bi]){t[bi]=!0,i.forEach(function(s){s!=="selectionchange"&&(F0.has(s)||ia(s,!1,t),ia(s,!0,t))});var o=t.nodeType===9?t:t.ownerDocument;o===null||o[bi]||(o[bi]=!0,ia("selectionchange",!1,o))}}function Af(t,o,s,u){switch(rf(o)){case 1:var f=Zw;break;case 4:f=Jw;break;default:f=Hl}s=f.bind(null,o,s,t),f=void 0,!Nl||o!=="touchstart"&&o!=="touchmove"&&o!=="wheel"||(f=!0),u?f!==void 0?t.addEventListener(o,s,{capture:!0,passive:f}):t.addEventListener(o,s,!0):f!==void 0?t.addEventListener(o,s,{passive:f}):t.addEventListener(o,s,!1)}function sa(t,o,s,u,f){var h=u;if((o&1)===0&&(o&2)===0&&u!==null)e:for(;;){if(u===null)return;var C=u.tag;if(C===3||C===4){var D=u.stateNode.containerInfo;if(D===f||D.nodeType===8&&D.parentNode===f)break;if(C===4)for(C=u.return;C!==null;){var M=C.tag;if((M===3||M===4)&&(M=C.stateNode.containerInfo,M===f||M.nodeType===8&&M.parentNode===f))return;C=C.return}for(;D!==null;){if(C=rr(D),C===null)return;if(M=C.tag,M===5||M===6){u=h=C;continue e}D=D.parentNode}}u=u.return}jd(function(){var H=h,re=Ml(s),ie=[];e:{var ee=Mf.get(t);if(ee!==void 0){var ge=Wl,me=t;switch(t){case"keypress":if(Oi(s)===0)break e;case"keydown":case"keyup":ge=g0;break;case"focusin":me="focus",ge=Xl;break;case"focusout":me="blur",ge=Xl;break;case"beforeblur":case"afterblur":ge=Xl;break;case"click":if(s.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":ge=lf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":ge=n0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":ge=v0;break;case Tf:case If:case Df:ge=i0;break;case Of:ge=w0;break;case"scroll":ge=e0;break;case"wheel":ge=x0;break;case"copy":case"cut":case"paste":ge=l0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":ge=uf}var ve=(o&4)!==0,We=!ve&&t==="scroll",j=ve?ee!==null?ee+"Capture":null:ee;ve=[];for(var k=H,V;k!==null;){V=k;var le=V.stateNode;if(V.tag===5&&le!==null&&(V=le,j!==null&&(le=ho(k,j),le!=null&&ve.push(No(k,le,V)))),We)break;k=k.return}0<ve.length&&(ee=new ge(ee,me,null,s,re),ie.push({event:ee,listeners:ve}))}}if((o&7)===0){e:{if(ee=t==="mouseover"||t==="pointerover",ge=t==="mouseout"||t==="pointerout",ee&&s!==Ol&&(me=s.relatedTarget||s.fromElement)&&(rr(me)||me[gn]))break e;if((ge||ee)&&(ee=re.window===re?re:(ee=re.ownerDocument)?ee.defaultView||ee.parentWindow:window,ge?(me=s.relatedTarget||s.toElement,ge=H,me=me?rr(me):null,me!==null&&(We=nr(me),me!==We||me.tag!==5&&me.tag!==6)&&(me=null)):(ge=null,me=H),ge!==me)){if(ve=lf,le="onMouseLeave",j="onMouseEnter",k="mouse",(t==="pointerout"||t==="pointerover")&&(ve=uf,le="onPointerLeave",j="onPointerEnter",k="pointer"),We=ge==null?ee:Nr(ge),V=me==null?ee:Nr(me),ee=new ve(le,k+"leave",ge,s,re),ee.target=We,ee.relatedTarget=V,le=null,rr(re)===H&&(ve=new ve(j,k+"enter",me,s,re),ve.target=V,ve.relatedTarget=We,le=ve),We=le,ge&&me)t:{for(ve=ge,j=me,k=0,V=ve;V;V=kr(V))k++;for(V=0,le=j;le;le=kr(le))V++;for(;0<k-V;)ve=kr(ve),k--;for(;0<V-k;)j=kr(j),V--;for(;k--;){if(ve===j||j!==null&&ve===j.alternate)break t;ve=kr(ve),j=kr(j)}ve=null}else ve=null;ge!==null&&bf(ie,ee,ge,ve,!1),me!==null&&We!==null&&bf(ie,We,me,ve,!0)}}e:{if(ee=H?Nr(H):window,ge=ee.nodeName&&ee.nodeName.toLowerCase(),ge==="select"||ge==="input"&&ee.type==="file")var ye=I0;else if(hf(ee))if(vf)ye=k0;else{ye=O0;var xe=D0}else(ge=ee.nodeName)&&ge.toLowerCase()==="input"&&(ee.type==="checkbox"||ee.type==="radio")&&(ye=M0);if(ye&&(ye=ye(t,H))){mf(ie,ye,s,re);break e}xe&&xe(t,ee,H),t==="focusout"&&(xe=ee._wrapperState)&&xe.controlled&&ee.type==="number"&&st(ee,"number",ee.value)}switch(xe=H?Nr(H):window,t){case"focusin":(hf(xe)||xe.contentEditable==="true")&&(Or=xe,ea=H,Mo=null);break;case"focusout":Mo=ea=Or=null;break;case"mousedown":ta=!0;break;case"contextmenu":case"mouseup":case"dragend":ta=!1,Pf(ie,s,re);break;case"selectionchange":if(A0)break;case"keydown":case"keyup":Pf(ie,s,re)}var Ce;if(Yl)e:{switch(t){case"compositionstart":var _e="onCompositionStart";break e;case"compositionend":_e="onCompositionEnd";break e;case"compositionupdate":_e="onCompositionUpdate";break e}_e=void 0}else Dr?pf(t,s)&&(_e="onCompositionEnd"):t==="keydown"&&s.keyCode===229&&(_e="onCompositionStart");_e&&(cf&&s.locale!=="ko"&&(Dr||_e!=="onCompositionStart"?_e==="onCompositionEnd"&&Dr&&(Ce=of()):(Nn=re,Bl="value"in Nn?Nn.value:Nn.textContent,Dr=!0)),xe=Li(H,_e),0<xe.length&&(_e=new af(_e,t,null,s,re),ie.push({event:_e,listeners:xe}),Ce?_e.data=Ce:(Ce=gf(s),Ce!==null&&(_e.data=Ce)))),(Ce=E0?R0(t,s):P0(t,s))&&(H=Li(H,"onBeforeInput"),0<H.length&&(re=new af("onBeforeInput","beforeinput",null,s,re),ie.push({event:re,listeners:H}),re.data=Ce))}Nf(ie,o)})}function No(t,o,s){return{instance:t,listener:o,currentTarget:s}}function Li(t,o){for(var s=o+"Capture",u=[];t!==null;){var f=t,h=f.stateNode;f.tag===5&&h!==null&&(f=h,h=ho(t,s),h!=null&&u.unshift(No(t,h,f)),h=ho(t,o),h!=null&&u.push(No(t,h,f))),t=t.return}return u}function kr(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function bf(t,o,s,u,f){for(var h=o._reactName,C=[];s!==null&&s!==u;){var D=s,M=D.alternate,H=D.stateNode;if(M!==null&&M===u)break;D.tag===5&&H!==null&&(D=H,f?(M=ho(s,h),M!=null&&C.unshift(No(s,M,D))):f||(M=ho(s,h),M!=null&&C.push(No(s,M,D)))),s=s.return}C.length!==0&&t.push({event:o,listeners:C})}var j0=/\r\n?/g,V0=/\u0000|\uFFFD/g;function Lf(t){return(typeof t=="string"?t:""+t).replace(j0,`
`).replace(V0,"")}function Fi(t,o,s){if(o=Lf(o),Lf(t)!==o&&s)throw Error(r(425))}function ji(){}var la=null,aa=null;function ua(t,o){return t==="textarea"||t==="noscript"||typeof o.children=="string"||typeof o.children=="number"||typeof o.dangerouslySetInnerHTML=="object"&&o.dangerouslySetInnerHTML!==null&&o.dangerouslySetInnerHTML.__html!=null}var ca=typeof setTimeout=="function"?setTimeout:void 0,z0=typeof clearTimeout=="function"?clearTimeout:void 0,Ff=typeof Promise=="function"?Promise:void 0,H0=typeof queueMicrotask=="function"?queueMicrotask:typeof Ff<"u"?function(t){return Ff.resolve(null).then(t).catch(U0)}:ca;function U0(t){setTimeout(function(){throw t})}function da(t,o){var s=o,u=0;do{var f=s.nextSibling;if(t.removeChild(s),f&&f.nodeType===8)if(s=f.data,s==="/$"){if(u===0){t.removeChild(f),Ro(o);return}u--}else s!=="$"&&s!=="$?"&&s!=="$!"||u++;s=f}while(s);Ro(o)}function bn(t){for(;t!=null;t=t.nextSibling){var o=t.nodeType;if(o===1||o===3)break;if(o===8){if(o=t.data,o==="$"||o==="$!"||o==="$?")break;if(o==="/$")return null}}return t}function jf(t){t=t.previousSibling;for(var o=0;t;){if(t.nodeType===8){var s=t.data;if(s==="$"||s==="$!"||s==="$?"){if(o===0)return t;o--}else s==="/$"&&o++}t=t.previousSibling}return null}var $r=Math.random().toString(36).slice(2),sn="__reactFiber$"+$r,Ao="__reactProps$"+$r,gn="__reactContainer$"+$r,fa="__reactEvents$"+$r,B0="__reactListeners$"+$r,W0="__reactHandles$"+$r;function rr(t){var o=t[sn];if(o)return o;for(var s=t.parentNode;s;){if(o=s[gn]||s[sn]){if(s=o.alternate,o.child!==null||s!==null&&s.child!==null)for(t=jf(t);t!==null;){if(s=t[sn])return s;t=jf(t)}return o}t=s,s=t.parentNode}return null}function bo(t){return t=t[sn]||t[gn],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function Nr(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(r(33))}function Vi(t){return t[Ao]||null}var pa=[],Ar=-1;function Ln(t){return{current:t}}function Le(t){0>Ar||(t.current=pa[Ar],pa[Ar]=null,Ar--)}function Ae(t,o){Ar++,pa[Ar]=t.current,t.current=o}var Fn={},lt=Ln(Fn),St=Ln(!1),or=Fn;function br(t,o){var s=t.type.contextTypes;if(!s)return Fn;var u=t.stateNode;if(u&&u.__reactInternalMemoizedUnmaskedChildContext===o)return u.__reactInternalMemoizedMaskedChildContext;var f={},h;for(h in s)f[h]=o[h];return u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=o,t.__reactInternalMemoizedMaskedChildContext=f),f}function xt(t){return t=t.childContextTypes,t!=null}function zi(){Le(St),Le(lt)}function Vf(t,o,s){if(lt.current!==Fn)throw Error(r(168));Ae(lt,o),Ae(St,s)}function zf(t,o,s){var u=t.stateNode;if(o=o.childContextTypes,typeof u.getChildContext!="function")return s;u=u.getChildContext();for(var f in u)if(!(f in o))throw Error(r(108,Q(t)||"Unknown",f));return U({},s,u)}function Hi(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Fn,or=lt.current,Ae(lt,t),Ae(St,St.current),!0}function Hf(t,o,s){var u=t.stateNode;if(!u)throw Error(r(169));s?(t=zf(t,o,or),u.__reactInternalMemoizedMergedChildContext=t,Le(St),Le(lt),Ae(lt,t)):Le(St),Ae(St,s)}var hn=null,Ui=!1,ga=!1;function Uf(t){hn===null?hn=[t]:hn.push(t)}function G0(t){Ui=!0,Uf(t)}function jn(){if(!ga&&hn!==null){ga=!0;var t=0,o=ke;try{var s=hn;for(ke=1;t<s.length;t++){var u=s[t];do u=u(!0);while(u!==null)}hn=null,Ui=!1}catch(f){throw hn!==null&&(hn=hn.slice(t+1)),Bd(bl,jn),f}finally{ke=o,ga=!1}}return null}var Lr=[],Fr=0,Bi=null,Wi=0,bt=[],Lt=0,ir=null,mn=1,vn="";function sr(t,o){Lr[Fr++]=Wi,Lr[Fr++]=Bi,Bi=t,Wi=o}function Bf(t,o,s){bt[Lt++]=mn,bt[Lt++]=vn,bt[Lt++]=ir,ir=t;var u=mn;t=vn;var f=32-Gt(u)-1;u&=~(1<<f),s+=1;var h=32-Gt(o)+f;if(30<h){var C=f-f%5;h=(u&(1<<C)-1).toString(32),u>>=C,f-=C,mn=1<<32-Gt(o)+f|s<<f|u,vn=h+t}else mn=1<<h|s<<f|u,vn=t}function ha(t){t.return!==null&&(sr(t,1),Bf(t,1,0))}function ma(t){for(;t===Bi;)Bi=Lr[--Fr],Lr[Fr]=null,Wi=Lr[--Fr],Lr[Fr]=null;for(;t===ir;)ir=bt[--Lt],bt[Lt]=null,vn=bt[--Lt],bt[Lt]=null,mn=bt[--Lt],bt[Lt]=null}var Ot=null,Mt=null,Fe=!1,Xt=null;function Wf(t,o){var s=zt(5,null,null,0);s.elementType="DELETED",s.stateNode=o,s.return=t,o=t.deletions,o===null?(t.deletions=[s],t.flags|=16):o.push(s)}function Gf(t,o){switch(t.tag){case 5:var s=t.type;return o=o.nodeType!==1||s.toLowerCase()!==o.nodeName.toLowerCase()?null:o,o!==null?(t.stateNode=o,Ot=t,Mt=bn(o.firstChild),!0):!1;case 6:return o=t.pendingProps===""||o.nodeType!==3?null:o,o!==null?(t.stateNode=o,Ot=t,Mt=null,!0):!1;case 13:return o=o.nodeType!==8?null:o,o!==null?(s=ir!==null?{id:mn,overflow:vn}:null,t.memoizedState={dehydrated:o,treeContext:s,retryLane:1073741824},s=zt(18,null,null,0),s.stateNode=o,s.return=t,t.child=s,Ot=t,Mt=null,!0):!1;default:return!1}}function va(t){return(t.mode&1)!==0&&(t.flags&128)===0}function ya(t){if(Fe){var o=Mt;if(o){var s=o;if(!Gf(t,o)){if(va(t))throw Error(r(418));o=bn(s.nextSibling);var u=Ot;o&&Gf(t,o)?Wf(u,s):(t.flags=t.flags&-4097|2,Fe=!1,Ot=t)}}else{if(va(t))throw Error(r(418));t.flags=t.flags&-4097|2,Fe=!1,Ot=t}}}function Kf(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Ot=t}function Gi(t){if(t!==Ot)return!1;if(!Fe)return Kf(t),Fe=!0,!1;var o;if((o=t.tag!==3)&&!(o=t.tag!==5)&&(o=t.type,o=o!=="head"&&o!=="body"&&!ua(t.type,t.memoizedProps)),o&&(o=Mt)){if(va(t))throw Xf(),Error(r(418));for(;o;)Wf(t,o),o=bn(o.nextSibling)}if(Kf(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(r(317));e:{for(t=t.nextSibling,o=0;t;){if(t.nodeType===8){var s=t.data;if(s==="/$"){if(o===0){Mt=bn(t.nextSibling);break e}o--}else s!=="$"&&s!=="$!"&&s!=="$?"||o++}t=t.nextSibling}Mt=null}}else Mt=Ot?bn(t.stateNode.nextSibling):null;return!0}function Xf(){for(var t=Mt;t;)t=bn(t.nextSibling)}function jr(){Mt=Ot=null,Fe=!1}function wa(t){Xt===null?Xt=[t]:Xt.push(t)}var K0=$.ReactCurrentBatchConfig;function Lo(t,o,s){if(t=s.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(s._owner){if(s=s._owner,s){if(s.tag!==1)throw Error(r(309));var u=s.stateNode}if(!u)throw Error(r(147,t));var f=u,h=""+t;return o!==null&&o.ref!==null&&typeof o.ref=="function"&&o.ref._stringRef===h?o.ref:(o=function(C){var D=f.refs;C===null?delete D[h]:D[h]=C},o._stringRef=h,o)}if(typeof t!="string")throw Error(r(284));if(!s._owner)throw Error(r(290,t))}return t}function Ki(t,o){throw t=Object.prototype.toString.call(o),Error(r(31,t==="[object Object]"?"object with keys {"+Object.keys(o).join(", ")+"}":t))}function qf(t){var o=t._init;return o(t._payload)}function Yf(t){function o(j,k){if(t){var V=j.deletions;V===null?(j.deletions=[k],j.flags|=16):V.push(k)}}function s(j,k){if(!t)return null;for(;k!==null;)o(j,k),k=k.sibling;return null}function u(j,k){for(j=new Map;k!==null;)k.key!==null?j.set(k.key,k):j.set(k.index,k),k=k.sibling;return j}function f(j,k){return j=Kn(j,k),j.index=0,j.sibling=null,j}function h(j,k,V){return j.index=V,t?(V=j.alternate,V!==null?(V=V.index,V<k?(j.flags|=2,k):V):(j.flags|=2,k)):(j.flags|=1048576,k)}function C(j){return t&&j.alternate===null&&(j.flags|=2),j}function D(j,k,V,le){return k===null||k.tag!==6?(k=cu(V,j.mode,le),k.return=j,k):(k=f(k,V),k.return=j,k)}function M(j,k,V,le){var ye=V.type;return ye===N?re(j,k,V.props.children,le,V.key):k!==null&&(k.elementType===ye||typeof ye=="object"&&ye!==null&&ye.$$typeof===W&&qf(ye)===k.type)?(le=f(k,V.props),le.ref=Lo(j,k,V),le.return=j,le):(le=vs(V.type,V.key,V.props,null,j.mode,le),le.ref=Lo(j,k,V),le.return=j,le)}function H(j,k,V,le){return k===null||k.tag!==4||k.stateNode.containerInfo!==V.containerInfo||k.stateNode.implementation!==V.implementation?(k=du(V,j.mode,le),k.return=j,k):(k=f(k,V.children||[]),k.return=j,k)}function re(j,k,V,le,ye){return k===null||k.tag!==7?(k=gr(V,j.mode,le,ye),k.return=j,k):(k=f(k,V),k.return=j,k)}function ie(j,k,V){if(typeof k=="string"&&k!==""||typeof k=="number")return k=cu(""+k,j.mode,V),k.return=j,k;if(typeof k=="object"&&k!==null){switch(k.$$typeof){case L:return V=vs(k.type,k.key,k.props,null,j.mode,V),V.ref=Lo(j,null,k),V.return=j,V;case B:return k=du(k,j.mode,V),k.return=j,k;case W:var le=k._init;return ie(j,le(k._payload),V)}if(It(k)||F(k))return k=gr(k,j.mode,V,null),k.return=j,k;Ki(j,k)}return null}function ee(j,k,V,le){var ye=k!==null?k.key:null;if(typeof V=="string"&&V!==""||typeof V=="number")return ye!==null?null:D(j,k,""+V,le);if(typeof V=="object"&&V!==null){switch(V.$$typeof){case L:return V.key===ye?M(j,k,V,le):null;case B:return V.key===ye?H(j,k,V,le):null;case W:return ye=V._init,ee(j,k,ye(V._payload),le)}if(It(V)||F(V))return ye!==null?null:re(j,k,V,le,null);Ki(j,V)}return null}function ge(j,k,V,le,ye){if(typeof le=="string"&&le!==""||typeof le=="number")return j=j.get(V)||null,D(k,j,""+le,ye);if(typeof le=="object"&&le!==null){switch(le.$$typeof){case L:return j=j.get(le.key===null?V:le.key)||null,M(k,j,le,ye);case B:return j=j.get(le.key===null?V:le.key)||null,H(k,j,le,ye);case W:var xe=le._init;return ge(j,k,V,xe(le._payload),ye)}if(It(le)||F(le))return j=j.get(V)||null,re(k,j,le,ye,null);Ki(k,le)}return null}function me(j,k,V,le){for(var ye=null,xe=null,Ce=k,_e=k=0,et=null;Ce!==null&&_e<V.length;_e++){Ce.index>_e?(et=Ce,Ce=null):et=Ce.sibling;var Me=ee(j,Ce,V[_e],le);if(Me===null){Ce===null&&(Ce=et);break}t&&Ce&&Me.alternate===null&&o(j,Ce),k=h(Me,k,_e),xe===null?ye=Me:xe.sibling=Me,xe=Me,Ce=et}if(_e===V.length)return s(j,Ce),Fe&&sr(j,_e),ye;if(Ce===null){for(;_e<V.length;_e++)Ce=ie(j,V[_e],le),Ce!==null&&(k=h(Ce,k,_e),xe===null?ye=Ce:xe.sibling=Ce,xe=Ce);return Fe&&sr(j,_e),ye}for(Ce=u(j,Ce);_e<V.length;_e++)et=ge(Ce,j,_e,V[_e],le),et!==null&&(t&&et.alternate!==null&&Ce.delete(et.key===null?_e:et.key),k=h(et,k,_e),xe===null?ye=et:xe.sibling=et,xe=et);return t&&Ce.forEach(function(Xn){return o(j,Xn)}),Fe&&sr(j,_e),ye}function ve(j,k,V,le){var ye=F(V);if(typeof ye!="function")throw Error(r(150));if(V=ye.call(V),V==null)throw Error(r(151));for(var xe=ye=null,Ce=k,_e=k=0,et=null,Me=V.next();Ce!==null&&!Me.done;_e++,Me=V.next()){Ce.index>_e?(et=Ce,Ce=null):et=Ce.sibling;var Xn=ee(j,Ce,Me.value,le);if(Xn===null){Ce===null&&(Ce=et);break}t&&Ce&&Xn.alternate===null&&o(j,Ce),k=h(Xn,k,_e),xe===null?ye=Xn:xe.sibling=Xn,xe=Xn,Ce=et}if(Me.done)return s(j,Ce),Fe&&sr(j,_e),ye;if(Ce===null){for(;!Me.done;_e++,Me=V.next())Me=ie(j,Me.value,le),Me!==null&&(k=h(Me,k,_e),xe===null?ye=Me:xe.sibling=Me,xe=Me);return Fe&&sr(j,_e),ye}for(Ce=u(j,Ce);!Me.done;_e++,Me=V.next())Me=ge(Ce,j,_e,Me.value,le),Me!==null&&(t&&Me.alternate!==null&&Ce.delete(Me.key===null?_e:Me.key),k=h(Me,k,_e),xe===null?ye=Me:xe.sibling=Me,xe=Me);return t&&Ce.forEach(function(_S){return o(j,_S)}),Fe&&sr(j,_e),ye}function We(j,k,V,le){if(typeof V=="object"&&V!==null&&V.type===N&&V.key===null&&(V=V.props.children),typeof V=="object"&&V!==null){switch(V.$$typeof){case L:e:{for(var ye=V.key,xe=k;xe!==null;){if(xe.key===ye){if(ye=V.type,ye===N){if(xe.tag===7){s(j,xe.sibling),k=f(xe,V.props.children),k.return=j,j=k;break e}}else if(xe.elementType===ye||typeof ye=="object"&&ye!==null&&ye.$$typeof===W&&qf(ye)===xe.type){s(j,xe.sibling),k=f(xe,V.props),k.ref=Lo(j,xe,V),k.return=j,j=k;break e}s(j,xe);break}else o(j,xe);xe=xe.sibling}V.type===N?(k=gr(V.props.children,j.mode,le,V.key),k.return=j,j=k):(le=vs(V.type,V.key,V.props,null,j.mode,le),le.ref=Lo(j,k,V),le.return=j,j=le)}return C(j);case B:e:{for(xe=V.key;k!==null;){if(k.key===xe)if(k.tag===4&&k.stateNode.containerInfo===V.containerInfo&&k.stateNode.implementation===V.implementation){s(j,k.sibling),k=f(k,V.children||[]),k.return=j,j=k;break e}else{s(j,k);break}else o(j,k);k=k.sibling}k=du(V,j.mode,le),k.return=j,j=k}return C(j);case W:return xe=V._init,We(j,k,xe(V._payload),le)}if(It(V))return me(j,k,V,le);if(F(V))return ve(j,k,V,le);Ki(j,V)}return typeof V=="string"&&V!==""||typeof V=="number"?(V=""+V,k!==null&&k.tag===6?(s(j,k.sibling),k=f(k,V),k.return=j,j=k):(s(j,k),k=cu(V,j.mode,le),k.return=j,j=k),C(j)):s(j,k)}return We}var Vr=Yf(!0),Qf=Yf(!1),Xi=Ln(null),qi=null,zr=null,Sa=null;function xa(){Sa=zr=qi=null}function Ca(t){var o=Xi.current;Le(Xi),t._currentValue=o}function Ea(t,o,s){for(;t!==null;){var u=t.alternate;if((t.childLanes&o)!==o?(t.childLanes|=o,u!==null&&(u.childLanes|=o)):u!==null&&(u.childLanes&o)!==o&&(u.childLanes|=o),t===s)break;t=t.return}}function Hr(t,o){qi=t,Sa=zr=null,t=t.dependencies,t!==null&&t.firstContext!==null&&((t.lanes&o)!==0&&(Ct=!0),t.firstContext=null)}function Ft(t){var o=t._currentValue;if(Sa!==t)if(t={context:t,memoizedValue:o,next:null},zr===null){if(qi===null)throw Error(r(308));zr=t,qi.dependencies={lanes:0,firstContext:t}}else zr=zr.next=t;return o}var lr=null;function Ra(t){lr===null?lr=[t]:lr.push(t)}function Zf(t,o,s,u){var f=o.interleaved;return f===null?(s.next=s,Ra(o)):(s.next=f.next,f.next=s),o.interleaved=s,yn(t,u)}function yn(t,o){t.lanes|=o;var s=t.alternate;for(s!==null&&(s.lanes|=o),s=t,t=t.return;t!==null;)t.childLanes|=o,s=t.alternate,s!==null&&(s.childLanes|=o),s=t,t=t.return;return s.tag===3?s.stateNode:null}var Vn=!1;function Pa(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Jf(t,o){t=t.updateQueue,o.updateQueue===t&&(o.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function wn(t,o){return{eventTime:t,lane:o,tag:0,payload:null,callback:null,next:null}}function zn(t,o,s){var u=t.updateQueue;if(u===null)return null;if(u=u.shared,(De&2)!==0){var f=u.pending;return f===null?o.next=o:(o.next=f.next,f.next=o),u.pending=o,yn(t,s)}return f=u.interleaved,f===null?(o.next=o,Ra(u)):(o.next=f.next,f.next=o),u.interleaved=o,yn(t,s)}function Yi(t,o,s){if(o=o.updateQueue,o!==null&&(o=o.shared,(s&4194240)!==0)){var u=o.lanes;u&=t.pendingLanes,s|=u,o.lanes=s,jl(t,s)}}function ep(t,o){var s=t.updateQueue,u=t.alternate;if(u!==null&&(u=u.updateQueue,s===u)){var f=null,h=null;if(s=s.firstBaseUpdate,s!==null){do{var C={eventTime:s.eventTime,lane:s.lane,tag:s.tag,payload:s.payload,callback:s.callback,next:null};h===null?f=h=C:h=h.next=C,s=s.next}while(s!==null);h===null?f=h=o:h=h.next=o}else f=h=o;s={baseState:u.baseState,firstBaseUpdate:f,lastBaseUpdate:h,shared:u.shared,effects:u.effects},t.updateQueue=s;return}t=s.lastBaseUpdate,t===null?s.firstBaseUpdate=o:t.next=o,s.lastBaseUpdate=o}function Qi(t,o,s,u){var f=t.updateQueue;Vn=!1;var h=f.firstBaseUpdate,C=f.lastBaseUpdate,D=f.shared.pending;if(D!==null){f.shared.pending=null;var M=D,H=M.next;M.next=null,C===null?h=H:C.next=H,C=M;var re=t.alternate;re!==null&&(re=re.updateQueue,D=re.lastBaseUpdate,D!==C&&(D===null?re.firstBaseUpdate=H:D.next=H,re.lastBaseUpdate=M))}if(h!==null){var ie=f.baseState;C=0,re=H=M=null,D=h;do{var ee=D.lane,ge=D.eventTime;if((u&ee)===ee){re!==null&&(re=re.next={eventTime:ge,lane:0,tag:D.tag,payload:D.payload,callback:D.callback,next:null});e:{var me=t,ve=D;switch(ee=o,ge=s,ve.tag){case 1:if(me=ve.payload,typeof me=="function"){ie=me.call(ge,ie,ee);break e}ie=me;break e;case 3:me.flags=me.flags&-65537|128;case 0:if(me=ve.payload,ee=typeof me=="function"?me.call(ge,ie,ee):me,ee==null)break e;ie=U({},ie,ee);break e;case 2:Vn=!0}}D.callback!==null&&D.lane!==0&&(t.flags|=64,ee=f.effects,ee===null?f.effects=[D]:ee.push(D))}else ge={eventTime:ge,lane:ee,tag:D.tag,payload:D.payload,callback:D.callback,next:null},re===null?(H=re=ge,M=ie):re=re.next=ge,C|=ee;if(D=D.next,D===null){if(D=f.shared.pending,D===null)break;ee=D,D=ee.next,ee.next=null,f.lastBaseUpdate=ee,f.shared.pending=null}}while(!0);if(re===null&&(M=ie),f.baseState=M,f.firstBaseUpdate=H,f.lastBaseUpdate=re,o=f.shared.interleaved,o!==null){f=o;do C|=f.lane,f=f.next;while(f!==o)}else h===null&&(f.shared.lanes=0);cr|=C,t.lanes=C,t.memoizedState=ie}}function tp(t,o,s){if(t=o.effects,o.effects=null,t!==null)for(o=0;o<t.length;o++){var u=t[o],f=u.callback;if(f!==null){if(u.callback=null,u=s,typeof f!="function")throw Error(r(191,f));f.call(u)}}}var Fo={},ln=Ln(Fo),jo=Ln(Fo),Vo=Ln(Fo);function ar(t){if(t===Fo)throw Error(r(174));return t}function _a(t,o){switch(Ae(Vo,o),Ae(jo,t),Ae(ln,Fo),t=o.nodeType,t){case 9:case 11:o=(o=o.documentElement)?o.namespaceURI:Tl(null,"");break;default:t=t===8?o.parentNode:o,o=t.namespaceURI||null,t=t.tagName,o=Tl(o,t)}Le(ln),Ae(ln,o)}function Ur(){Le(ln),Le(jo),Le(Vo)}function np(t){ar(Vo.current);var o=ar(ln.current),s=Tl(o,t.type);o!==s&&(Ae(jo,t),Ae(ln,s))}function Ta(t){jo.current===t&&(Le(ln),Le(jo))}var Ve=Ln(0);function Zi(t){for(var o=t;o!==null;){if(o.tag===13){var s=o.memoizedState;if(s!==null&&(s=s.dehydrated,s===null||s.data==="$?"||s.data==="$!"))return o}else if(o.tag===19&&o.memoizedProps.revealOrder!==void 0){if((o.flags&128)!==0)return o}else if(o.child!==null){o.child.return=o,o=o.child;continue}if(o===t)break;for(;o.sibling===null;){if(o.return===null||o.return===t)return null;o=o.return}o.sibling.return=o.return,o=o.sibling}return null}var Ia=[];function Da(){for(var t=0;t<Ia.length;t++)Ia[t]._workInProgressVersionPrimary=null;Ia.length=0}var Ji=$.ReactCurrentDispatcher,Oa=$.ReactCurrentBatchConfig,ur=0,ze=null,Ke=null,Ze=null,es=!1,zo=!1,Ho=0,X0=0;function at(){throw Error(r(321))}function Ma(t,o){if(o===null)return!1;for(var s=0;s<o.length&&s<t.length;s++)if(!Kt(t[s],o[s]))return!1;return!0}function ka(t,o,s,u,f,h){if(ur=h,ze=o,o.memoizedState=null,o.updateQueue=null,o.lanes=0,Ji.current=t===null||t.memoizedState===null?Z0:J0,t=s(u,f),zo){h=0;do{if(zo=!1,Ho=0,25<=h)throw Error(r(301));h+=1,Ze=Ke=null,o.updateQueue=null,Ji.current=eS,t=s(u,f)}while(zo)}if(Ji.current=rs,o=Ke!==null&&Ke.next!==null,ur=0,Ze=Ke=ze=null,es=!1,o)throw Error(r(300));return t}function $a(){var t=Ho!==0;return Ho=0,t}function an(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ze===null?ze.memoizedState=Ze=t:Ze=Ze.next=t,Ze}function jt(){if(Ke===null){var t=ze.alternate;t=t!==null?t.memoizedState:null}else t=Ke.next;var o=Ze===null?ze.memoizedState:Ze.next;if(o!==null)Ze=o,Ke=t;else{if(t===null)throw Error(r(310));Ke=t,t={memoizedState:Ke.memoizedState,baseState:Ke.baseState,baseQueue:Ke.baseQueue,queue:Ke.queue,next:null},Ze===null?ze.memoizedState=Ze=t:Ze=Ze.next=t}return Ze}function Uo(t,o){return typeof o=="function"?o(t):o}function Na(t){var o=jt(),s=o.queue;if(s===null)throw Error(r(311));s.lastRenderedReducer=t;var u=Ke,f=u.baseQueue,h=s.pending;if(h!==null){if(f!==null){var C=f.next;f.next=h.next,h.next=C}u.baseQueue=f=h,s.pending=null}if(f!==null){h=f.next,u=u.baseState;var D=C=null,M=null,H=h;do{var re=H.lane;if((ur&re)===re)M!==null&&(M=M.next={lane:0,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null}),u=H.hasEagerState?H.eagerState:t(u,H.action);else{var ie={lane:re,action:H.action,hasEagerState:H.hasEagerState,eagerState:H.eagerState,next:null};M===null?(D=M=ie,C=u):M=M.next=ie,ze.lanes|=re,cr|=re}H=H.next}while(H!==null&&H!==h);M===null?C=u:M.next=D,Kt(u,o.memoizedState)||(Ct=!0),o.memoizedState=u,o.baseState=C,o.baseQueue=M,s.lastRenderedState=u}if(t=s.interleaved,t!==null){f=t;do h=f.lane,ze.lanes|=h,cr|=h,f=f.next;while(f!==t)}else f===null&&(s.lanes=0);return[o.memoizedState,s.dispatch]}function Aa(t){var o=jt(),s=o.queue;if(s===null)throw Error(r(311));s.lastRenderedReducer=t;var u=s.dispatch,f=s.pending,h=o.memoizedState;if(f!==null){s.pending=null;var C=f=f.next;do h=t(h,C.action),C=C.next;while(C!==f);Kt(h,o.memoizedState)||(Ct=!0),o.memoizedState=h,o.baseQueue===null&&(o.baseState=h),s.lastRenderedState=h}return[h,u]}function rp(){}function op(t,o){var s=ze,u=jt(),f=o(),h=!Kt(u.memoizedState,f);if(h&&(u.memoizedState=f,Ct=!0),u=u.queue,ba(lp.bind(null,s,u,t),[t]),u.getSnapshot!==o||h||Ze!==null&&Ze.memoizedState.tag&1){if(s.flags|=2048,Bo(9,sp.bind(null,s,u,f,o),void 0,null),Je===null)throw Error(r(349));(ur&30)!==0||ip(s,o,f)}return f}function ip(t,o,s){t.flags|=16384,t={getSnapshot:o,value:s},o=ze.updateQueue,o===null?(o={lastEffect:null,stores:null},ze.updateQueue=o,o.stores=[t]):(s=o.stores,s===null?o.stores=[t]:s.push(t))}function sp(t,o,s,u){o.value=s,o.getSnapshot=u,ap(o)&&up(t)}function lp(t,o,s){return s(function(){ap(o)&&up(t)})}function ap(t){var o=t.getSnapshot;t=t.value;try{var s=o();return!Kt(t,s)}catch{return!0}}function up(t){var o=yn(t,1);o!==null&&Zt(o,t,1,-1)}function cp(t){var o=an();return typeof t=="function"&&(t=t()),o.memoizedState=o.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Uo,lastRenderedState:t},o.queue=t,t=t.dispatch=Q0.bind(null,ze,t),[o.memoizedState,t]}function Bo(t,o,s,u){return t={tag:t,create:o,destroy:s,deps:u,next:null},o=ze.updateQueue,o===null?(o={lastEffect:null,stores:null},ze.updateQueue=o,o.lastEffect=t.next=t):(s=o.lastEffect,s===null?o.lastEffect=t.next=t:(u=s.next,s.next=t,t.next=u,o.lastEffect=t)),t}function dp(){return jt().memoizedState}function ts(t,o,s,u){var f=an();ze.flags|=t,f.memoizedState=Bo(1|o,s,void 0,u===void 0?null:u)}function ns(t,o,s,u){var f=jt();u=u===void 0?null:u;var h=void 0;if(Ke!==null){var C=Ke.memoizedState;if(h=C.destroy,u!==null&&Ma(u,C.deps)){f.memoizedState=Bo(o,s,h,u);return}}ze.flags|=t,f.memoizedState=Bo(1|o,s,h,u)}function fp(t,o){return ts(8390656,8,t,o)}function ba(t,o){return ns(2048,8,t,o)}function pp(t,o){return ns(4,2,t,o)}function gp(t,o){return ns(4,4,t,o)}function hp(t,o){if(typeof o=="function")return t=t(),o(t),function(){o(null)};if(o!=null)return t=t(),o.current=t,function(){o.current=null}}function mp(t,o,s){return s=s!=null?s.concat([t]):null,ns(4,4,hp.bind(null,o,t),s)}function La(){}function vp(t,o){var s=jt();o=o===void 0?null:o;var u=s.memoizedState;return u!==null&&o!==null&&Ma(o,u[1])?u[0]:(s.memoizedState=[t,o],t)}function yp(t,o){var s=jt();o=o===void 0?null:o;var u=s.memoizedState;return u!==null&&o!==null&&Ma(o,u[1])?u[0]:(t=t(),s.memoizedState=[t,o],t)}function wp(t,o,s){return(ur&21)===0?(t.baseState&&(t.baseState=!1,Ct=!0),t.memoizedState=s):(Kt(s,o)||(s=Xd(),ze.lanes|=s,cr|=s,t.baseState=!0),o)}function q0(t,o){var s=ke;ke=s!==0&&4>s?s:4,t(!0);var u=Oa.transition;Oa.transition={};try{t(!1),o()}finally{ke=s,Oa.transition=u}}function Sp(){return jt().memoizedState}function Y0(t,o,s){var u=Wn(t);if(s={lane:u,action:s,hasEagerState:!1,eagerState:null,next:null},xp(t))Cp(o,s);else if(s=Zf(t,o,s,u),s!==null){var f=mt();Zt(s,t,u,f),Ep(s,o,u)}}function Q0(t,o,s){var u=Wn(t),f={lane:u,action:s,hasEagerState:!1,eagerState:null,next:null};if(xp(t))Cp(o,f);else{var h=t.alternate;if(t.lanes===0&&(h===null||h.lanes===0)&&(h=o.lastRenderedReducer,h!==null))try{var C=o.lastRenderedState,D=h(C,s);if(f.hasEagerState=!0,f.eagerState=D,Kt(D,C)){var M=o.interleaved;M===null?(f.next=f,Ra(o)):(f.next=M.next,M.next=f),o.interleaved=f;return}}catch{}finally{}s=Zf(t,o,f,u),s!==null&&(f=mt(),Zt(s,t,u,f),Ep(s,o,u))}}function xp(t){var o=t.alternate;return t===ze||o!==null&&o===ze}function Cp(t,o){zo=es=!0;var s=t.pending;s===null?o.next=o:(o.next=s.next,s.next=o),t.pending=o}function Ep(t,o,s){if((s&4194240)!==0){var u=o.lanes;u&=t.pendingLanes,s|=u,o.lanes=s,jl(t,s)}}var rs={readContext:Ft,useCallback:at,useContext:at,useEffect:at,useImperativeHandle:at,useInsertionEffect:at,useLayoutEffect:at,useMemo:at,useReducer:at,useRef:at,useState:at,useDebugValue:at,useDeferredValue:at,useTransition:at,useMutableSource:at,useSyncExternalStore:at,useId:at,unstable_isNewReconciler:!1},Z0={readContext:Ft,useCallback:function(t,o){return an().memoizedState=[t,o===void 0?null:o],t},useContext:Ft,useEffect:fp,useImperativeHandle:function(t,o,s){return s=s!=null?s.concat([t]):null,ts(4194308,4,hp.bind(null,o,t),s)},useLayoutEffect:function(t,o){return ts(4194308,4,t,o)},useInsertionEffect:function(t,o){return ts(4,2,t,o)},useMemo:function(t,o){var s=an();return o=o===void 0?null:o,t=t(),s.memoizedState=[t,o],t},useReducer:function(t,o,s){var u=an();return o=s!==void 0?s(o):o,u.memoizedState=u.baseState=o,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:o},u.queue=t,t=t.dispatch=Y0.bind(null,ze,t),[u.memoizedState,t]},useRef:function(t){var o=an();return t={current:t},o.memoizedState=t},useState:cp,useDebugValue:La,useDeferredValue:function(t){return an().memoizedState=t},useTransition:function(){var t=cp(!1),o=t[0];return t=q0.bind(null,t[1]),an().memoizedState=t,[o,t]},useMutableSource:function(){},useSyncExternalStore:function(t,o,s){var u=ze,f=an();if(Fe){if(s===void 0)throw Error(r(407));s=s()}else{if(s=o(),Je===null)throw Error(r(349));(ur&30)!==0||ip(u,o,s)}f.memoizedState=s;var h={value:s,getSnapshot:o};return f.queue=h,fp(lp.bind(null,u,h,t),[t]),u.flags|=2048,Bo(9,sp.bind(null,u,h,s,o),void 0,null),s},useId:function(){var t=an(),o=Je.identifierPrefix;if(Fe){var s=vn,u=mn;s=(u&~(1<<32-Gt(u)-1)).toString(32)+s,o=":"+o+"R"+s,s=Ho++,0<s&&(o+="H"+s.toString(32)),o+=":"}else s=X0++,o=":"+o+"r"+s.toString(32)+":";return t.memoizedState=o},unstable_isNewReconciler:!1},J0={readContext:Ft,useCallback:vp,useContext:Ft,useEffect:ba,useImperativeHandle:mp,useInsertionEffect:pp,useLayoutEffect:gp,useMemo:yp,useReducer:Na,useRef:dp,useState:function(){return Na(Uo)},useDebugValue:La,useDeferredValue:function(t){var o=jt();return wp(o,Ke.memoizedState,t)},useTransition:function(){var t=Na(Uo)[0],o=jt().memoizedState;return[t,o]},useMutableSource:rp,useSyncExternalStore:op,useId:Sp,unstable_isNewReconciler:!1},eS={readContext:Ft,useCallback:vp,useContext:Ft,useEffect:ba,useImperativeHandle:mp,useInsertionEffect:pp,useLayoutEffect:gp,useMemo:yp,useReducer:Aa,useRef:dp,useState:function(){return Aa(Uo)},useDebugValue:La,useDeferredValue:function(t){var o=jt();return Ke===null?o.memoizedState=t:wp(o,Ke.memoizedState,t)},useTransition:function(){var t=Aa(Uo)[0],o=jt().memoizedState;return[t,o]},useMutableSource:rp,useSyncExternalStore:op,useId:Sp,unstable_isNewReconciler:!1};function qt(t,o){if(t&&t.defaultProps){o=U({},o),t=t.defaultProps;for(var s in t)o[s]===void 0&&(o[s]=t[s]);return o}return o}function Fa(t,o,s,u){o=t.memoizedState,s=s(u,o),s=s==null?o:U({},o,s),t.memoizedState=s,t.lanes===0&&(t.updateQueue.baseState=s)}var os={isMounted:function(t){return(t=t._reactInternals)?nr(t)===t:!1},enqueueSetState:function(t,o,s){t=t._reactInternals;var u=mt(),f=Wn(t),h=wn(u,f);h.payload=o,s!=null&&(h.callback=s),o=zn(t,h,f),o!==null&&(Zt(o,t,f,u),Yi(o,t,f))},enqueueReplaceState:function(t,o,s){t=t._reactInternals;var u=mt(),f=Wn(t),h=wn(u,f);h.tag=1,h.payload=o,s!=null&&(h.callback=s),o=zn(t,h,f),o!==null&&(Zt(o,t,f,u),Yi(o,t,f))},enqueueForceUpdate:function(t,o){t=t._reactInternals;var s=mt(),u=Wn(t),f=wn(s,u);f.tag=2,o!=null&&(f.callback=o),o=zn(t,f,u),o!==null&&(Zt(o,t,u,s),Yi(o,t,u))}};function Rp(t,o,s,u,f,h,C){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(u,h,C):o.prototype&&o.prototype.isPureReactComponent?!Oo(s,u)||!Oo(f,h):!0}function Pp(t,o,s){var u=!1,f=Fn,h=o.contextType;return typeof h=="object"&&h!==null?h=Ft(h):(f=xt(o)?or:lt.current,u=o.contextTypes,h=(u=u!=null)?br(t,f):Fn),o=new o(s,h),t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,o.updater=os,t.stateNode=o,o._reactInternals=t,u&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=f,t.__reactInternalMemoizedMaskedChildContext=h),o}function _p(t,o,s,u){t=o.state,typeof o.componentWillReceiveProps=="function"&&o.componentWillReceiveProps(s,u),typeof o.UNSAFE_componentWillReceiveProps=="function"&&o.UNSAFE_componentWillReceiveProps(s,u),o.state!==t&&os.enqueueReplaceState(o,o.state,null)}function ja(t,o,s,u){var f=t.stateNode;f.props=s,f.state=t.memoizedState,f.refs={},Pa(t);var h=o.contextType;typeof h=="object"&&h!==null?f.context=Ft(h):(h=xt(o)?or:lt.current,f.context=br(t,h)),f.state=t.memoizedState,h=o.getDerivedStateFromProps,typeof h=="function"&&(Fa(t,o,h,s),f.state=t.memoizedState),typeof o.getDerivedStateFromProps=="function"||typeof f.getSnapshotBeforeUpdate=="function"||typeof f.UNSAFE_componentWillMount!="function"&&typeof f.componentWillMount!="function"||(o=f.state,typeof f.componentWillMount=="function"&&f.componentWillMount(),typeof f.UNSAFE_componentWillMount=="function"&&f.UNSAFE_componentWillMount(),o!==f.state&&os.enqueueReplaceState(f,f.state,null),Qi(t,s,f,u),f.state=t.memoizedState),typeof f.componentDidMount=="function"&&(t.flags|=4194308)}function Br(t,o){try{var s="",u=o;do s+=X(u),u=u.return;while(u);var f=s}catch(h){f=`
Error generating stack: `+h.message+`
`+h.stack}return{value:t,source:o,stack:f,digest:null}}function Va(t,o,s){return{value:t,source:null,stack:s??null,digest:o??null}}function za(t,o){try{console.error(o.value)}catch(s){setTimeout(function(){throw s})}}var tS=typeof WeakMap=="function"?WeakMap:Map;function Tp(t,o,s){s=wn(-1,s),s.tag=3,s.payload={element:null};var u=o.value;return s.callback=function(){ds||(ds=!0,nu=u),za(t,o)},s}function Ip(t,o,s){s=wn(-1,s),s.tag=3;var u=t.type.getDerivedStateFromError;if(typeof u=="function"){var f=o.value;s.payload=function(){return u(f)},s.callback=function(){za(t,o)}}var h=t.stateNode;return h!==null&&typeof h.componentDidCatch=="function"&&(s.callback=function(){za(t,o),typeof u!="function"&&(Un===null?Un=new Set([this]):Un.add(this));var C=o.stack;this.componentDidCatch(o.value,{componentStack:C!==null?C:""})}),s}function Dp(t,o,s){var u=t.pingCache;if(u===null){u=t.pingCache=new tS;var f=new Set;u.set(o,f)}else f=u.get(o),f===void 0&&(f=new Set,u.set(o,f));f.has(s)||(f.add(s),t=hS.bind(null,t,o,s),o.then(t,t))}function Op(t){do{var o;if((o=t.tag===13)&&(o=t.memoizedState,o=o!==null?o.dehydrated!==null:!0),o)return t;t=t.return}while(t!==null);return null}function Mp(t,o,s,u,f){return(t.mode&1)===0?(t===o?t.flags|=65536:(t.flags|=128,s.flags|=131072,s.flags&=-52805,s.tag===1&&(s.alternate===null?s.tag=17:(o=wn(-1,1),o.tag=2,zn(s,o,1))),s.lanes|=1),t):(t.flags|=65536,t.lanes=f,t)}var nS=$.ReactCurrentOwner,Ct=!1;function ht(t,o,s,u){o.child=t===null?Qf(o,null,s,u):Vr(o,t.child,s,u)}function kp(t,o,s,u,f){s=s.render;var h=o.ref;return Hr(o,f),u=ka(t,o,s,u,h,f),s=$a(),t!==null&&!Ct?(o.updateQueue=t.updateQueue,o.flags&=-2053,t.lanes&=~f,Sn(t,o,f)):(Fe&&s&&ha(o),o.flags|=1,ht(t,o,u,f),o.child)}function $p(t,o,s,u,f){if(t===null){var h=s.type;return typeof h=="function"&&!uu(h)&&h.defaultProps===void 0&&s.compare===null&&s.defaultProps===void 0?(o.tag=15,o.type=h,Np(t,o,h,u,f)):(t=vs(s.type,null,u,o,o.mode,f),t.ref=o.ref,t.return=o,o.child=t)}if(h=t.child,(t.lanes&f)===0){var C=h.memoizedProps;if(s=s.compare,s=s!==null?s:Oo,s(C,u)&&t.ref===o.ref)return Sn(t,o,f)}return o.flags|=1,t=Kn(h,u),t.ref=o.ref,t.return=o,o.child=t}function Np(t,o,s,u,f){if(t!==null){var h=t.memoizedProps;if(Oo(h,u)&&t.ref===o.ref)if(Ct=!1,o.pendingProps=u=h,(t.lanes&f)!==0)(t.flags&131072)!==0&&(Ct=!0);else return o.lanes=t.lanes,Sn(t,o,f)}return Ha(t,o,s,u,f)}function Ap(t,o,s){var u=o.pendingProps,f=u.children,h=t!==null?t.memoizedState:null;if(u.mode==="hidden")if((o.mode&1)===0)o.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ae(Gr,kt),kt|=s;else{if((s&1073741824)===0)return t=h!==null?h.baseLanes|s:s,o.lanes=o.childLanes=1073741824,o.memoizedState={baseLanes:t,cachePool:null,transitions:null},o.updateQueue=null,Ae(Gr,kt),kt|=t,null;o.memoizedState={baseLanes:0,cachePool:null,transitions:null},u=h!==null?h.baseLanes:s,Ae(Gr,kt),kt|=u}else h!==null?(u=h.baseLanes|s,o.memoizedState=null):u=s,Ae(Gr,kt),kt|=u;return ht(t,o,f,s),o.child}function bp(t,o){var s=o.ref;(t===null&&s!==null||t!==null&&t.ref!==s)&&(o.flags|=512,o.flags|=2097152)}function Ha(t,o,s,u,f){var h=xt(s)?or:lt.current;return h=br(o,h),Hr(o,f),s=ka(t,o,s,u,h,f),u=$a(),t!==null&&!Ct?(o.updateQueue=t.updateQueue,o.flags&=-2053,t.lanes&=~f,Sn(t,o,f)):(Fe&&u&&ha(o),o.flags|=1,ht(t,o,s,f),o.child)}function Lp(t,o,s,u,f){if(xt(s)){var h=!0;Hi(o)}else h=!1;if(Hr(o,f),o.stateNode===null)ss(t,o),Pp(o,s,u),ja(o,s,u,f),u=!0;else if(t===null){var C=o.stateNode,D=o.memoizedProps;C.props=D;var M=C.context,H=s.contextType;typeof H=="object"&&H!==null?H=Ft(H):(H=xt(s)?or:lt.current,H=br(o,H));var re=s.getDerivedStateFromProps,ie=typeof re=="function"||typeof C.getSnapshotBeforeUpdate=="function";ie||typeof C.UNSAFE_componentWillReceiveProps!="function"&&typeof C.componentWillReceiveProps!="function"||(D!==u||M!==H)&&_p(o,C,u,H),Vn=!1;var ee=o.memoizedState;C.state=ee,Qi(o,u,C,f),M=o.memoizedState,D!==u||ee!==M||St.current||Vn?(typeof re=="function"&&(Fa(o,s,re,u),M=o.memoizedState),(D=Vn||Rp(o,s,D,u,ee,M,H))?(ie||typeof C.UNSAFE_componentWillMount!="function"&&typeof C.componentWillMount!="function"||(typeof C.componentWillMount=="function"&&C.componentWillMount(),typeof C.UNSAFE_componentWillMount=="function"&&C.UNSAFE_componentWillMount()),typeof C.componentDidMount=="function"&&(o.flags|=4194308)):(typeof C.componentDidMount=="function"&&(o.flags|=4194308),o.memoizedProps=u,o.memoizedState=M),C.props=u,C.state=M,C.context=H,u=D):(typeof C.componentDidMount=="function"&&(o.flags|=4194308),u=!1)}else{C=o.stateNode,Jf(t,o),D=o.memoizedProps,H=o.type===o.elementType?D:qt(o.type,D),C.props=H,ie=o.pendingProps,ee=C.context,M=s.contextType,typeof M=="object"&&M!==null?M=Ft(M):(M=xt(s)?or:lt.current,M=br(o,M));var ge=s.getDerivedStateFromProps;(re=typeof ge=="function"||typeof C.getSnapshotBeforeUpdate=="function")||typeof C.UNSAFE_componentWillReceiveProps!="function"&&typeof C.componentWillReceiveProps!="function"||(D!==ie||ee!==M)&&_p(o,C,u,M),Vn=!1,ee=o.memoizedState,C.state=ee,Qi(o,u,C,f);var me=o.memoizedState;D!==ie||ee!==me||St.current||Vn?(typeof ge=="function"&&(Fa(o,s,ge,u),me=o.memoizedState),(H=Vn||Rp(o,s,H,u,ee,me,M)||!1)?(re||typeof C.UNSAFE_componentWillUpdate!="function"&&typeof C.componentWillUpdate!="function"||(typeof C.componentWillUpdate=="function"&&C.componentWillUpdate(u,me,M),typeof C.UNSAFE_componentWillUpdate=="function"&&C.UNSAFE_componentWillUpdate(u,me,M)),typeof C.componentDidUpdate=="function"&&(o.flags|=4),typeof C.getSnapshotBeforeUpdate=="function"&&(o.flags|=1024)):(typeof C.componentDidUpdate!="function"||D===t.memoizedProps&&ee===t.memoizedState||(o.flags|=4),typeof C.getSnapshotBeforeUpdate!="function"||D===t.memoizedProps&&ee===t.memoizedState||(o.flags|=1024),o.memoizedProps=u,o.memoizedState=me),C.props=u,C.state=me,C.context=M,u=H):(typeof C.componentDidUpdate!="function"||D===t.memoizedProps&&ee===t.memoizedState||(o.flags|=4),typeof C.getSnapshotBeforeUpdate!="function"||D===t.memoizedProps&&ee===t.memoizedState||(o.flags|=1024),u=!1)}return Ua(t,o,s,u,h,f)}function Ua(t,o,s,u,f,h){bp(t,o);var C=(o.flags&128)!==0;if(!u&&!C)return f&&Hf(o,s,!1),Sn(t,o,h);u=o.stateNode,nS.current=o;var D=C&&typeof s.getDerivedStateFromError!="function"?null:u.render();return o.flags|=1,t!==null&&C?(o.child=Vr(o,t.child,null,h),o.child=Vr(o,null,D,h)):ht(t,o,D,h),o.memoizedState=u.state,f&&Hf(o,s,!0),o.child}function Fp(t){var o=t.stateNode;o.pendingContext?Vf(t,o.pendingContext,o.pendingContext!==o.context):o.context&&Vf(t,o.context,!1),_a(t,o.containerInfo)}function jp(t,o,s,u,f){return jr(),wa(f),o.flags|=256,ht(t,o,s,u),o.child}var Ba={dehydrated:null,treeContext:null,retryLane:0};function Wa(t){return{baseLanes:t,cachePool:null,transitions:null}}function Vp(t,o,s){var u=o.pendingProps,f=Ve.current,h=!1,C=(o.flags&128)!==0,D;if((D=C)||(D=t!==null&&t.memoizedState===null?!1:(f&2)!==0),D?(h=!0,o.flags&=-129):(t===null||t.memoizedState!==null)&&(f|=1),Ae(Ve,f&1),t===null)return ya(o),t=o.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?((o.mode&1)===0?o.lanes=1:t.data==="$!"?o.lanes=8:o.lanes=1073741824,null):(C=u.children,t=u.fallback,h?(u=o.mode,h=o.child,C={mode:"hidden",children:C},(u&1)===0&&h!==null?(h.childLanes=0,h.pendingProps=C):h=ys(C,u,0,null),t=gr(t,u,s,null),h.return=o,t.return=o,h.sibling=t,o.child=h,o.child.memoizedState=Wa(s),o.memoizedState=Ba,t):Ga(o,C));if(f=t.memoizedState,f!==null&&(D=f.dehydrated,D!==null))return rS(t,o,C,u,D,f,s);if(h){h=u.fallback,C=o.mode,f=t.child,D=f.sibling;var M={mode:"hidden",children:u.children};return(C&1)===0&&o.child!==f?(u=o.child,u.childLanes=0,u.pendingProps=M,o.deletions=null):(u=Kn(f,M),u.subtreeFlags=f.subtreeFlags&14680064),D!==null?h=Kn(D,h):(h=gr(h,C,s,null),h.flags|=2),h.return=o,u.return=o,u.sibling=h,o.child=u,u=h,h=o.child,C=t.child.memoizedState,C=C===null?Wa(s):{baseLanes:C.baseLanes|s,cachePool:null,transitions:C.transitions},h.memoizedState=C,h.childLanes=t.childLanes&~s,o.memoizedState=Ba,u}return h=t.child,t=h.sibling,u=Kn(h,{mode:"visible",children:u.children}),(o.mode&1)===0&&(u.lanes=s),u.return=o,u.sibling=null,t!==null&&(s=o.deletions,s===null?(o.deletions=[t],o.flags|=16):s.push(t)),o.child=u,o.memoizedState=null,u}function Ga(t,o){return o=ys({mode:"visible",children:o},t.mode,0,null),o.return=t,t.child=o}function is(t,o,s,u){return u!==null&&wa(u),Vr(o,t.child,null,s),t=Ga(o,o.pendingProps.children),t.flags|=2,o.memoizedState=null,t}function rS(t,o,s,u,f,h,C){if(s)return o.flags&256?(o.flags&=-257,u=Va(Error(r(422))),is(t,o,C,u)):o.memoizedState!==null?(o.child=t.child,o.flags|=128,null):(h=u.fallback,f=o.mode,u=ys({mode:"visible",children:u.children},f,0,null),h=gr(h,f,C,null),h.flags|=2,u.return=o,h.return=o,u.sibling=h,o.child=u,(o.mode&1)!==0&&Vr(o,t.child,null,C),o.child.memoizedState=Wa(C),o.memoizedState=Ba,h);if((o.mode&1)===0)return is(t,o,C,null);if(f.data==="$!"){if(u=f.nextSibling&&f.nextSibling.dataset,u)var D=u.dgst;return u=D,h=Error(r(419)),u=Va(h,u,void 0),is(t,o,C,u)}if(D=(C&t.childLanes)!==0,Ct||D){if(u=Je,u!==null){switch(C&-C){case 4:f=2;break;case 16:f=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:f=32;break;case 536870912:f=268435456;break;default:f=0}f=(f&(u.suspendedLanes|C))!==0?0:f,f!==0&&f!==h.retryLane&&(h.retryLane=f,yn(t,f),Zt(u,t,f,-1))}return au(),u=Va(Error(r(421))),is(t,o,C,u)}return f.data==="$?"?(o.flags|=128,o.child=t.child,o=mS.bind(null,t),f._reactRetry=o,null):(t=h.treeContext,Mt=bn(f.nextSibling),Ot=o,Fe=!0,Xt=null,t!==null&&(bt[Lt++]=mn,bt[Lt++]=vn,bt[Lt++]=ir,mn=t.id,vn=t.overflow,ir=o),o=Ga(o,u.children),o.flags|=4096,o)}function zp(t,o,s){t.lanes|=o;var u=t.alternate;u!==null&&(u.lanes|=o),Ea(t.return,o,s)}function Ka(t,o,s,u,f){var h=t.memoizedState;h===null?t.memoizedState={isBackwards:o,rendering:null,renderingStartTime:0,last:u,tail:s,tailMode:f}:(h.isBackwards=o,h.rendering=null,h.renderingStartTime=0,h.last=u,h.tail=s,h.tailMode=f)}function Hp(t,o,s){var u=o.pendingProps,f=u.revealOrder,h=u.tail;if(ht(t,o,u.children,s),u=Ve.current,(u&2)!==0)u=u&1|2,o.flags|=128;else{if(t!==null&&(t.flags&128)!==0)e:for(t=o.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&zp(t,s,o);else if(t.tag===19)zp(t,s,o);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===o)break e;for(;t.sibling===null;){if(t.return===null||t.return===o)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}u&=1}if(Ae(Ve,u),(o.mode&1)===0)o.memoizedState=null;else switch(f){case"forwards":for(s=o.child,f=null;s!==null;)t=s.alternate,t!==null&&Zi(t)===null&&(f=s),s=s.sibling;s=f,s===null?(f=o.child,o.child=null):(f=s.sibling,s.sibling=null),Ka(o,!1,f,s,h);break;case"backwards":for(s=null,f=o.child,o.child=null;f!==null;){if(t=f.alternate,t!==null&&Zi(t)===null){o.child=f;break}t=f.sibling,f.sibling=s,s=f,f=t}Ka(o,!0,s,null,h);break;case"together":Ka(o,!1,null,null,void 0);break;default:o.memoizedState=null}return o.child}function ss(t,o){(o.mode&1)===0&&t!==null&&(t.alternate=null,o.alternate=null,o.flags|=2)}function Sn(t,o,s){if(t!==null&&(o.dependencies=t.dependencies),cr|=o.lanes,(s&o.childLanes)===0)return null;if(t!==null&&o.child!==t.child)throw Error(r(153));if(o.child!==null){for(t=o.child,s=Kn(t,t.pendingProps),o.child=s,s.return=o;t.sibling!==null;)t=t.sibling,s=s.sibling=Kn(t,t.pendingProps),s.return=o;s.sibling=null}return o.child}function oS(t,o,s){switch(o.tag){case 3:Fp(o),jr();break;case 5:np(o);break;case 1:xt(o.type)&&Hi(o);break;case 4:_a(o,o.stateNode.containerInfo);break;case 10:var u=o.type._context,f=o.memoizedProps.value;Ae(Xi,u._currentValue),u._currentValue=f;break;case 13:if(u=o.memoizedState,u!==null)return u.dehydrated!==null?(Ae(Ve,Ve.current&1),o.flags|=128,null):(s&o.child.childLanes)!==0?Vp(t,o,s):(Ae(Ve,Ve.current&1),t=Sn(t,o,s),t!==null?t.sibling:null);Ae(Ve,Ve.current&1);break;case 19:if(u=(s&o.childLanes)!==0,(t.flags&128)!==0){if(u)return Hp(t,o,s);o.flags|=128}if(f=o.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),Ae(Ve,Ve.current),u)break;return null;case 22:case 23:return o.lanes=0,Ap(t,o,s)}return Sn(t,o,s)}var Up,Xa,Bp,Wp;Up=function(t,o){for(var s=o.child;s!==null;){if(s.tag===5||s.tag===6)t.appendChild(s.stateNode);else if(s.tag!==4&&s.child!==null){s.child.return=s,s=s.child;continue}if(s===o)break;for(;s.sibling===null;){if(s.return===null||s.return===o)return;s=s.return}s.sibling.return=s.return,s=s.sibling}},Xa=function(){},Bp=function(t,o,s,u){var f=t.memoizedProps;if(f!==u){t=o.stateNode,ar(ln.current);var h=null;switch(s){case"input":f=ce(t,f),u=ce(t,u),h=[];break;case"select":f=U({},f,{value:void 0}),u=U({},u,{value:void 0}),h=[];break;case"textarea":f=Rr(t,f),u=Rr(t,u),h=[];break;default:typeof f.onClick!="function"&&typeof u.onClick=="function"&&(t.onclick=ji)}Il(s,u);var C;s=null;for(H in f)if(!u.hasOwnProperty(H)&&f.hasOwnProperty(H)&&f[H]!=null)if(H==="style"){var D=f[H];for(C in D)D.hasOwnProperty(C)&&(s||(s={}),s[C]="")}else H!=="dangerouslySetInnerHTML"&&H!=="children"&&H!=="suppressContentEditableWarning"&&H!=="suppressHydrationWarning"&&H!=="autoFocus"&&(a.hasOwnProperty(H)?h||(h=[]):(h=h||[]).push(H,null));for(H in u){var M=u[H];if(D=f!=null?f[H]:void 0,u.hasOwnProperty(H)&&M!==D&&(M!=null||D!=null))if(H==="style")if(D){for(C in D)!D.hasOwnProperty(C)||M&&M.hasOwnProperty(C)||(s||(s={}),s[C]="");for(C in M)M.hasOwnProperty(C)&&D[C]!==M[C]&&(s||(s={}),s[C]=M[C])}else s||(h||(h=[]),h.push(H,s)),s=M;else H==="dangerouslySetInnerHTML"?(M=M?M.__html:void 0,D=D?D.__html:void 0,M!=null&&D!==M&&(h=h||[]).push(H,M)):H==="children"?typeof M!="string"&&typeof M!="number"||(h=h||[]).push(H,""+M):H!=="suppressContentEditableWarning"&&H!=="suppressHydrationWarning"&&(a.hasOwnProperty(H)?(M!=null&&H==="onScroll"&&be("scroll",t),h||D===M||(h=[])):(h=h||[]).push(H,M))}s&&(h=h||[]).push("style",s);var H=h;(o.updateQueue=H)&&(o.flags|=4)}},Wp=function(t,o,s,u){s!==u&&(o.flags|=4)};function Wo(t,o){if(!Fe)switch(t.tailMode){case"hidden":o=t.tail;for(var s=null;o!==null;)o.alternate!==null&&(s=o),o=o.sibling;s===null?t.tail=null:s.sibling=null;break;case"collapsed":s=t.tail;for(var u=null;s!==null;)s.alternate!==null&&(u=s),s=s.sibling;u===null?o||t.tail===null?t.tail=null:t.tail.sibling=null:u.sibling=null}}function ut(t){var o=t.alternate!==null&&t.alternate.child===t.child,s=0,u=0;if(o)for(var f=t.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags&14680064,u|=f.flags&14680064,f.return=t,f=f.sibling;else for(f=t.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags,u|=f.flags,f.return=t,f=f.sibling;return t.subtreeFlags|=u,t.childLanes=s,o}function iS(t,o,s){var u=o.pendingProps;switch(ma(o),o.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ut(o),null;case 1:return xt(o.type)&&zi(),ut(o),null;case 3:return u=o.stateNode,Ur(),Le(St),Le(lt),Da(),u.pendingContext&&(u.context=u.pendingContext,u.pendingContext=null),(t===null||t.child===null)&&(Gi(o)?o.flags|=4:t===null||t.memoizedState.isDehydrated&&(o.flags&256)===0||(o.flags|=1024,Xt!==null&&(iu(Xt),Xt=null))),Xa(t,o),ut(o),null;case 5:Ta(o);var f=ar(Vo.current);if(s=o.type,t!==null&&o.stateNode!=null)Bp(t,o,s,u,f),t.ref!==o.ref&&(o.flags|=512,o.flags|=2097152);else{if(!u){if(o.stateNode===null)throw Error(r(166));return ut(o),null}if(t=ar(ln.current),Gi(o)){u=o.stateNode,s=o.type;var h=o.memoizedProps;switch(u[sn]=o,u[Ao]=h,t=(o.mode&1)!==0,s){case"dialog":be("cancel",u),be("close",u);break;case"iframe":case"object":case"embed":be("load",u);break;case"video":case"audio":for(f=0;f<ko.length;f++)be(ko[f],u);break;case"source":be("error",u);break;case"img":case"image":case"link":be("error",u),be("load",u);break;case"details":be("toggle",u);break;case"input":Ne(u,h),be("invalid",u);break;case"select":u._wrapperState={wasMultiple:!!h.multiple},be("invalid",u);break;case"textarea":Ue(u,h),be("invalid",u)}Il(s,h),f=null;for(var C in h)if(h.hasOwnProperty(C)){var D=h[C];C==="children"?typeof D=="string"?u.textContent!==D&&(h.suppressHydrationWarning!==!0&&Fi(u.textContent,D,t),f=["children",D]):typeof D=="number"&&u.textContent!==""+D&&(h.suppressHydrationWarning!==!0&&Fi(u.textContent,D,t),f=["children",""+D]):a.hasOwnProperty(C)&&D!=null&&C==="onScroll"&&be("scroll",u)}switch(s){case"input":pe(u),rn(u,h,!0);break;case"textarea":pe(u),mi(u);break;case"select":case"option":break;default:typeof h.onClick=="function"&&(u.onclick=ji)}u=f,o.updateQueue=u,u!==null&&(o.flags|=4)}else{C=f.nodeType===9?f:f.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=Od(s)),t==="http://www.w3.org/1999/xhtml"?s==="script"?(t=C.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof u.is=="string"?t=C.createElement(s,{is:u.is}):(t=C.createElement(s),s==="select"&&(C=t,u.multiple?C.multiple=!0:u.size&&(C.size=u.size))):t=C.createElementNS(t,s),t[sn]=o,t[Ao]=u,Up(t,o,!1,!1),o.stateNode=t;e:{switch(C=Dl(s,u),s){case"dialog":be("cancel",t),be("close",t),f=u;break;case"iframe":case"object":case"embed":be("load",t),f=u;break;case"video":case"audio":for(f=0;f<ko.length;f++)be(ko[f],t);f=u;break;case"source":be("error",t),f=u;break;case"img":case"image":case"link":be("error",t),be("load",t),f=u;break;case"details":be("toggle",t),f=u;break;case"input":Ne(t,u),f=ce(t,u),be("invalid",t);break;case"option":f=u;break;case"select":t._wrapperState={wasMultiple:!!u.multiple},f=U({},u,{value:void 0}),be("invalid",t);break;case"textarea":Ue(t,u),f=Rr(t,u),be("invalid",t);break;default:f=u}Il(s,f),D=f;for(h in D)if(D.hasOwnProperty(h)){var M=D[h];h==="style"?$d(t,M):h==="dangerouslySetInnerHTML"?(M=M?M.__html:void 0,M!=null&&Md(t,M)):h==="children"?typeof M=="string"?(s!=="textarea"||M!=="")&&po(t,M):typeof M=="number"&&po(t,""+M):h!=="suppressContentEditableWarning"&&h!=="suppressHydrationWarning"&&h!=="autoFocus"&&(a.hasOwnProperty(h)?M!=null&&h==="onScroll"&&be("scroll",t):M!=null&&I(t,h,M,C))}switch(s){case"input":pe(t),rn(t,u,!1);break;case"textarea":pe(t),mi(t);break;case"option":u.value!=null&&t.setAttribute("value",""+oe(u.value));break;case"select":t.multiple=!!u.multiple,h=u.value,h!=null?pn(t,!!u.multiple,h,!1):u.defaultValue!=null&&pn(t,!!u.multiple,u.defaultValue,!0);break;default:typeof f.onClick=="function"&&(t.onclick=ji)}switch(s){case"button":case"input":case"select":case"textarea":u=!!u.autoFocus;break e;case"img":u=!0;break e;default:u=!1}}u&&(o.flags|=4)}o.ref!==null&&(o.flags|=512,o.flags|=2097152)}return ut(o),null;case 6:if(t&&o.stateNode!=null)Wp(t,o,t.memoizedProps,u);else{if(typeof u!="string"&&o.stateNode===null)throw Error(r(166));if(s=ar(Vo.current),ar(ln.current),Gi(o)){if(u=o.stateNode,s=o.memoizedProps,u[sn]=o,(h=u.nodeValue!==s)&&(t=Ot,t!==null))switch(t.tag){case 3:Fi(u.nodeValue,s,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Fi(u.nodeValue,s,(t.mode&1)!==0)}h&&(o.flags|=4)}else u=(s.nodeType===9?s:s.ownerDocument).createTextNode(u),u[sn]=o,o.stateNode=u}return ut(o),null;case 13:if(Le(Ve),u=o.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(Fe&&Mt!==null&&(o.mode&1)!==0&&(o.flags&128)===0)Xf(),jr(),o.flags|=98560,h=!1;else if(h=Gi(o),u!==null&&u.dehydrated!==null){if(t===null){if(!h)throw Error(r(318));if(h=o.memoizedState,h=h!==null?h.dehydrated:null,!h)throw Error(r(317));h[sn]=o}else jr(),(o.flags&128)===0&&(o.memoizedState=null),o.flags|=4;ut(o),h=!1}else Xt!==null&&(iu(Xt),Xt=null),h=!0;if(!h)return o.flags&65536?o:null}return(o.flags&128)!==0?(o.lanes=s,o):(u=u!==null,u!==(t!==null&&t.memoizedState!==null)&&u&&(o.child.flags|=8192,(o.mode&1)!==0&&(t===null||(Ve.current&1)!==0?Xe===0&&(Xe=3):au())),o.updateQueue!==null&&(o.flags|=4),ut(o),null);case 4:return Ur(),Xa(t,o),t===null&&$o(o.stateNode.containerInfo),ut(o),null;case 10:return Ca(o.type._context),ut(o),null;case 17:return xt(o.type)&&zi(),ut(o),null;case 19:if(Le(Ve),h=o.memoizedState,h===null)return ut(o),null;if(u=(o.flags&128)!==0,C=h.rendering,C===null)if(u)Wo(h,!1);else{if(Xe!==0||t!==null&&(t.flags&128)!==0)for(t=o.child;t!==null;){if(C=Zi(t),C!==null){for(o.flags|=128,Wo(h,!1),u=C.updateQueue,u!==null&&(o.updateQueue=u,o.flags|=4),o.subtreeFlags=0,u=s,s=o.child;s!==null;)h=s,t=u,h.flags&=14680066,C=h.alternate,C===null?(h.childLanes=0,h.lanes=t,h.child=null,h.subtreeFlags=0,h.memoizedProps=null,h.memoizedState=null,h.updateQueue=null,h.dependencies=null,h.stateNode=null):(h.childLanes=C.childLanes,h.lanes=C.lanes,h.child=C.child,h.subtreeFlags=0,h.deletions=null,h.memoizedProps=C.memoizedProps,h.memoizedState=C.memoizedState,h.updateQueue=C.updateQueue,h.type=C.type,t=C.dependencies,h.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),s=s.sibling;return Ae(Ve,Ve.current&1|2),o.child}t=t.sibling}h.tail!==null&&Be()>Kr&&(o.flags|=128,u=!0,Wo(h,!1),o.lanes=4194304)}else{if(!u)if(t=Zi(C),t!==null){if(o.flags|=128,u=!0,s=t.updateQueue,s!==null&&(o.updateQueue=s,o.flags|=4),Wo(h,!0),h.tail===null&&h.tailMode==="hidden"&&!C.alternate&&!Fe)return ut(o),null}else 2*Be()-h.renderingStartTime>Kr&&s!==1073741824&&(o.flags|=128,u=!0,Wo(h,!1),o.lanes=4194304);h.isBackwards?(C.sibling=o.child,o.child=C):(s=h.last,s!==null?s.sibling=C:o.child=C,h.last=C)}return h.tail!==null?(o=h.tail,h.rendering=o,h.tail=o.sibling,h.renderingStartTime=Be(),o.sibling=null,s=Ve.current,Ae(Ve,u?s&1|2:s&1),o):(ut(o),null);case 22:case 23:return lu(),u=o.memoizedState!==null,t!==null&&t.memoizedState!==null!==u&&(o.flags|=8192),u&&(o.mode&1)!==0?(kt&1073741824)!==0&&(ut(o),o.subtreeFlags&6&&(o.flags|=8192)):ut(o),null;case 24:return null;case 25:return null}throw Error(r(156,o.tag))}function sS(t,o){switch(ma(o),o.tag){case 1:return xt(o.type)&&zi(),t=o.flags,t&65536?(o.flags=t&-65537|128,o):null;case 3:return Ur(),Le(St),Le(lt),Da(),t=o.flags,(t&65536)!==0&&(t&128)===0?(o.flags=t&-65537|128,o):null;case 5:return Ta(o),null;case 13:if(Le(Ve),t=o.memoizedState,t!==null&&t.dehydrated!==null){if(o.alternate===null)throw Error(r(340));jr()}return t=o.flags,t&65536?(o.flags=t&-65537|128,o):null;case 19:return Le(Ve),null;case 4:return Ur(),null;case 10:return Ca(o.type._context),null;case 22:case 23:return lu(),null;case 24:return null;default:return null}}var ls=!1,ct=!1,lS=typeof WeakSet=="function"?WeakSet:Set,he=null;function Wr(t,o){var s=t.ref;if(s!==null)if(typeof s=="function")try{s(null)}catch(u){He(t,o,u)}else s.current=null}function qa(t,o,s){try{s()}catch(u){He(t,o,u)}}var Gp=!1;function aS(t,o){if(la=Ti,t=Rf(),Jl(t)){if("selectionStart"in t)var s={start:t.selectionStart,end:t.selectionEnd};else e:{s=(s=t.ownerDocument)&&s.defaultView||window;var u=s.getSelection&&s.getSelection();if(u&&u.rangeCount!==0){s=u.anchorNode;var f=u.anchorOffset,h=u.focusNode;u=u.focusOffset;try{s.nodeType,h.nodeType}catch{s=null;break e}var C=0,D=-1,M=-1,H=0,re=0,ie=t,ee=null;t:for(;;){for(var ge;ie!==s||f!==0&&ie.nodeType!==3||(D=C+f),ie!==h||u!==0&&ie.nodeType!==3||(M=C+u),ie.nodeType===3&&(C+=ie.nodeValue.length),(ge=ie.firstChild)!==null;)ee=ie,ie=ge;for(;;){if(ie===t)break t;if(ee===s&&++H===f&&(D=C),ee===h&&++re===u&&(M=C),(ge=ie.nextSibling)!==null)break;ie=ee,ee=ie.parentNode}ie=ge}s=D===-1||M===-1?null:{start:D,end:M}}else s=null}s=s||{start:0,end:0}}else s=null;for(aa={focusedElem:t,selectionRange:s},Ti=!1,he=o;he!==null;)if(o=he,t=o.child,(o.subtreeFlags&1028)!==0&&t!==null)t.return=o,he=t;else for(;he!==null;){o=he;try{var me=o.alternate;if((o.flags&1024)!==0)switch(o.tag){case 0:case 11:case 15:break;case 1:if(me!==null){var ve=me.memoizedProps,We=me.memoizedState,j=o.stateNode,k=j.getSnapshotBeforeUpdate(o.elementType===o.type?ve:qt(o.type,ve),We);j.__reactInternalSnapshotBeforeUpdate=k}break;case 3:var V=o.stateNode.containerInfo;V.nodeType===1?V.textContent="":V.nodeType===9&&V.documentElement&&V.removeChild(V.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(r(163))}}catch(le){He(o,o.return,le)}if(t=o.sibling,t!==null){t.return=o.return,he=t;break}he=o.return}return me=Gp,Gp=!1,me}function Go(t,o,s){var u=o.updateQueue;if(u=u!==null?u.lastEffect:null,u!==null){var f=u=u.next;do{if((f.tag&t)===t){var h=f.destroy;f.destroy=void 0,h!==void 0&&qa(o,s,h)}f=f.next}while(f!==u)}}function as(t,o){if(o=o.updateQueue,o=o!==null?o.lastEffect:null,o!==null){var s=o=o.next;do{if((s.tag&t)===t){var u=s.create;s.destroy=u()}s=s.next}while(s!==o)}}function Ya(t){var o=t.ref;if(o!==null){var s=t.stateNode;switch(t.tag){case 5:t=s;break;default:t=s}typeof o=="function"?o(t):o.current=t}}function Kp(t){var o=t.alternate;o!==null&&(t.alternate=null,Kp(o)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(o=t.stateNode,o!==null&&(delete o[sn],delete o[Ao],delete o[fa],delete o[B0],delete o[W0])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function Xp(t){return t.tag===5||t.tag===3||t.tag===4}function qp(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||Xp(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function Qa(t,o,s){var u=t.tag;if(u===5||u===6)t=t.stateNode,o?s.nodeType===8?s.parentNode.insertBefore(t,o):s.insertBefore(t,o):(s.nodeType===8?(o=s.parentNode,o.insertBefore(t,s)):(o=s,o.appendChild(t)),s=s._reactRootContainer,s!=null||o.onclick!==null||(o.onclick=ji));else if(u!==4&&(t=t.child,t!==null))for(Qa(t,o,s),t=t.sibling;t!==null;)Qa(t,o,s),t=t.sibling}function Za(t,o,s){var u=t.tag;if(u===5||u===6)t=t.stateNode,o?s.insertBefore(t,o):s.appendChild(t);else if(u!==4&&(t=t.child,t!==null))for(Za(t,o,s),t=t.sibling;t!==null;)Za(t,o,s),t=t.sibling}var ot=null,Yt=!1;function Hn(t,o,s){for(s=s.child;s!==null;)Yp(t,o,s),s=s.sibling}function Yp(t,o,s){if(on&&typeof on.onCommitFiberUnmount=="function")try{on.onCommitFiberUnmount(xi,s)}catch{}switch(s.tag){case 5:ct||Wr(s,o);case 6:var u=ot,f=Yt;ot=null,Hn(t,o,s),ot=u,Yt=f,ot!==null&&(Yt?(t=ot,s=s.stateNode,t.nodeType===8?t.parentNode.removeChild(s):t.removeChild(s)):ot.removeChild(s.stateNode));break;case 18:ot!==null&&(Yt?(t=ot,s=s.stateNode,t.nodeType===8?da(t.parentNode,s):t.nodeType===1&&da(t,s),Ro(t)):da(ot,s.stateNode));break;case 4:u=ot,f=Yt,ot=s.stateNode.containerInfo,Yt=!0,Hn(t,o,s),ot=u,Yt=f;break;case 0:case 11:case 14:case 15:if(!ct&&(u=s.updateQueue,u!==null&&(u=u.lastEffect,u!==null))){f=u=u.next;do{var h=f,C=h.destroy;h=h.tag,C!==void 0&&((h&2)!==0||(h&4)!==0)&&qa(s,o,C),f=f.next}while(f!==u)}Hn(t,o,s);break;case 1:if(!ct&&(Wr(s,o),u=s.stateNode,typeof u.componentWillUnmount=="function"))try{u.props=s.memoizedProps,u.state=s.memoizedState,u.componentWillUnmount()}catch(D){He(s,o,D)}Hn(t,o,s);break;case 21:Hn(t,o,s);break;case 22:s.mode&1?(ct=(u=ct)||s.memoizedState!==null,Hn(t,o,s),ct=u):Hn(t,o,s);break;default:Hn(t,o,s)}}function Qp(t){var o=t.updateQueue;if(o!==null){t.updateQueue=null;var s=t.stateNode;s===null&&(s=t.stateNode=new lS),o.forEach(function(u){var f=vS.bind(null,t,u);s.has(u)||(s.add(u),u.then(f,f))})}}function Qt(t,o){var s=o.deletions;if(s!==null)for(var u=0;u<s.length;u++){var f=s[u];try{var h=t,C=o,D=C;e:for(;D!==null;){switch(D.tag){case 5:ot=D.stateNode,Yt=!1;break e;case 3:ot=D.stateNode.containerInfo,Yt=!0;break e;case 4:ot=D.stateNode.containerInfo,Yt=!0;break e}D=D.return}if(ot===null)throw Error(r(160));Yp(h,C,f),ot=null,Yt=!1;var M=f.alternate;M!==null&&(M.return=null),f.return=null}catch(H){He(f,o,H)}}if(o.subtreeFlags&12854)for(o=o.child;o!==null;)Zp(o,t),o=o.sibling}function Zp(t,o){var s=t.alternate,u=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(Qt(o,t),un(t),u&4){try{Go(3,t,t.return),as(3,t)}catch(ve){He(t,t.return,ve)}try{Go(5,t,t.return)}catch(ve){He(t,t.return,ve)}}break;case 1:Qt(o,t),un(t),u&512&&s!==null&&Wr(s,s.return);break;case 5:if(Qt(o,t),un(t),u&512&&s!==null&&Wr(s,s.return),t.flags&32){var f=t.stateNode;try{po(f,"")}catch(ve){He(t,t.return,ve)}}if(u&4&&(f=t.stateNode,f!=null)){var h=t.memoizedProps,C=s!==null?s.memoizedProps:h,D=t.type,M=t.updateQueue;if(t.updateQueue=null,M!==null)try{D==="input"&&h.type==="radio"&&h.name!=null&&nt(f,h),Dl(D,C);var H=Dl(D,h);for(C=0;C<M.length;C+=2){var re=M[C],ie=M[C+1];re==="style"?$d(f,ie):re==="dangerouslySetInnerHTML"?Md(f,ie):re==="children"?po(f,ie):I(f,re,ie,H)}switch(D){case"input":rt(f,h);break;case"textarea":Qe(f,h);break;case"select":var ee=f._wrapperState.wasMultiple;f._wrapperState.wasMultiple=!!h.multiple;var ge=h.value;ge!=null?pn(f,!!h.multiple,ge,!1):ee!==!!h.multiple&&(h.defaultValue!=null?pn(f,!!h.multiple,h.defaultValue,!0):pn(f,!!h.multiple,h.multiple?[]:"",!1))}f[Ao]=h}catch(ve){He(t,t.return,ve)}}break;case 6:if(Qt(o,t),un(t),u&4){if(t.stateNode===null)throw Error(r(162));f=t.stateNode,h=t.memoizedProps;try{f.nodeValue=h}catch(ve){He(t,t.return,ve)}}break;case 3:if(Qt(o,t),un(t),u&4&&s!==null&&s.memoizedState.isDehydrated)try{Ro(o.containerInfo)}catch(ve){He(t,t.return,ve)}break;case 4:Qt(o,t),un(t);break;case 13:Qt(o,t),un(t),f=t.child,f.flags&8192&&(h=f.memoizedState!==null,f.stateNode.isHidden=h,!h||f.alternate!==null&&f.alternate.memoizedState!==null||(tu=Be())),u&4&&Qp(t);break;case 22:if(re=s!==null&&s.memoizedState!==null,t.mode&1?(ct=(H=ct)||re,Qt(o,t),ct=H):Qt(o,t),un(t),u&8192){if(H=t.memoizedState!==null,(t.stateNode.isHidden=H)&&!re&&(t.mode&1)!==0)for(he=t,re=t.child;re!==null;){for(ie=he=re;he!==null;){switch(ee=he,ge=ee.child,ee.tag){case 0:case 11:case 14:case 15:Go(4,ee,ee.return);break;case 1:Wr(ee,ee.return);var me=ee.stateNode;if(typeof me.componentWillUnmount=="function"){u=ee,s=ee.return;try{o=u,me.props=o.memoizedProps,me.state=o.memoizedState,me.componentWillUnmount()}catch(ve){He(u,s,ve)}}break;case 5:Wr(ee,ee.return);break;case 22:if(ee.memoizedState!==null){tg(ie);continue}}ge!==null?(ge.return=ee,he=ge):tg(ie)}re=re.sibling}e:for(re=null,ie=t;;){if(ie.tag===5){if(re===null){re=ie;try{f=ie.stateNode,H?(h=f.style,typeof h.setProperty=="function"?h.setProperty("display","none","important"):h.display="none"):(D=ie.stateNode,M=ie.memoizedProps.style,C=M!=null&&M.hasOwnProperty("display")?M.display:null,D.style.display=kd("display",C))}catch(ve){He(t,t.return,ve)}}}else if(ie.tag===6){if(re===null)try{ie.stateNode.nodeValue=H?"":ie.memoizedProps}catch(ve){He(t,t.return,ve)}}else if((ie.tag!==22&&ie.tag!==23||ie.memoizedState===null||ie===t)&&ie.child!==null){ie.child.return=ie,ie=ie.child;continue}if(ie===t)break e;for(;ie.sibling===null;){if(ie.return===null||ie.return===t)break e;re===ie&&(re=null),ie=ie.return}re===ie&&(re=null),ie.sibling.return=ie.return,ie=ie.sibling}}break;case 19:Qt(o,t),un(t),u&4&&Qp(t);break;case 21:break;default:Qt(o,t),un(t)}}function un(t){var o=t.flags;if(o&2){try{e:{for(var s=t.return;s!==null;){if(Xp(s)){var u=s;break e}s=s.return}throw Error(r(160))}switch(u.tag){case 5:var f=u.stateNode;u.flags&32&&(po(f,""),u.flags&=-33);var h=qp(t);Za(t,h,f);break;case 3:case 4:var C=u.stateNode.containerInfo,D=qp(t);Qa(t,D,C);break;default:throw Error(r(161))}}catch(M){He(t,t.return,M)}t.flags&=-3}o&4096&&(t.flags&=-4097)}function uS(t,o,s){he=t,Jp(t)}function Jp(t,o,s){for(var u=(t.mode&1)!==0;he!==null;){var f=he,h=f.child;if(f.tag===22&&u){var C=f.memoizedState!==null||ls;if(!C){var D=f.alternate,M=D!==null&&D.memoizedState!==null||ct;D=ls;var H=ct;if(ls=C,(ct=M)&&!H)for(he=f;he!==null;)C=he,M=C.child,C.tag===22&&C.memoizedState!==null?ng(f):M!==null?(M.return=C,he=M):ng(f);for(;h!==null;)he=h,Jp(h),h=h.sibling;he=f,ls=D,ct=H}eg(t)}else(f.subtreeFlags&8772)!==0&&h!==null?(h.return=f,he=h):eg(t)}}function eg(t){for(;he!==null;){var o=he;if((o.flags&8772)!==0){var s=o.alternate;try{if((o.flags&8772)!==0)switch(o.tag){case 0:case 11:case 15:ct||as(5,o);break;case 1:var u=o.stateNode;if(o.flags&4&&!ct)if(s===null)u.componentDidMount();else{var f=o.elementType===o.type?s.memoizedProps:qt(o.type,s.memoizedProps);u.componentDidUpdate(f,s.memoizedState,u.__reactInternalSnapshotBeforeUpdate)}var h=o.updateQueue;h!==null&&tp(o,h,u);break;case 3:var C=o.updateQueue;if(C!==null){if(s=null,o.child!==null)switch(o.child.tag){case 5:s=o.child.stateNode;break;case 1:s=o.child.stateNode}tp(o,C,s)}break;case 5:var D=o.stateNode;if(s===null&&o.flags&4){s=D;var M=o.memoizedProps;switch(o.type){case"button":case"input":case"select":case"textarea":M.autoFocus&&s.focus();break;case"img":M.src&&(s.src=M.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(o.memoizedState===null){var H=o.alternate;if(H!==null){var re=H.memoizedState;if(re!==null){var ie=re.dehydrated;ie!==null&&Ro(ie)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(r(163))}ct||o.flags&512&&Ya(o)}catch(ee){He(o,o.return,ee)}}if(o===t){he=null;break}if(s=o.sibling,s!==null){s.return=o.return,he=s;break}he=o.return}}function tg(t){for(;he!==null;){var o=he;if(o===t){he=null;break}var s=o.sibling;if(s!==null){s.return=o.return,he=s;break}he=o.return}}function ng(t){for(;he!==null;){var o=he;try{switch(o.tag){case 0:case 11:case 15:var s=o.return;try{as(4,o)}catch(M){He(o,s,M)}break;case 1:var u=o.stateNode;if(typeof u.componentDidMount=="function"){var f=o.return;try{u.componentDidMount()}catch(M){He(o,f,M)}}var h=o.return;try{Ya(o)}catch(M){He(o,h,M)}break;case 5:var C=o.return;try{Ya(o)}catch(M){He(o,C,M)}}}catch(M){He(o,o.return,M)}if(o===t){he=null;break}var D=o.sibling;if(D!==null){D.return=o.return,he=D;break}he=o.return}}var cS=Math.ceil,us=$.ReactCurrentDispatcher,Ja=$.ReactCurrentOwner,Vt=$.ReactCurrentBatchConfig,De=0,Je=null,Ge=null,it=0,kt=0,Gr=Ln(0),Xe=0,Ko=null,cr=0,cs=0,eu=0,Xo=null,Et=null,tu=0,Kr=1/0,xn=null,ds=!1,nu=null,Un=null,fs=!1,Bn=null,ps=0,qo=0,ru=null,gs=-1,hs=0;function mt(){return(De&6)!==0?Be():gs!==-1?gs:gs=Be()}function Wn(t){return(t.mode&1)===0?1:(De&2)!==0&&it!==0?it&-it:K0.transition!==null?(hs===0&&(hs=Xd()),hs):(t=ke,t!==0||(t=window.event,t=t===void 0?16:rf(t.type)),t)}function Zt(t,o,s,u){if(50<qo)throw qo=0,ru=null,Error(r(185));wo(t,s,u),((De&2)===0||t!==Je)&&(t===Je&&((De&2)===0&&(cs|=s),Xe===4&&Gn(t,it)),Rt(t,u),s===1&&De===0&&(o.mode&1)===0&&(Kr=Be()+500,Ui&&jn()))}function Rt(t,o){var s=t.callbackNode;Kw(t,o);var u=Ri(t,t===Je?it:0);if(u===0)s!==null&&Wd(s),t.callbackNode=null,t.callbackPriority=0;else if(o=u&-u,t.callbackPriority!==o){if(s!=null&&Wd(s),o===1)t.tag===0?G0(og.bind(null,t)):Uf(og.bind(null,t)),H0(function(){(De&6)===0&&jn()}),s=null;else{switch(qd(u)){case 1:s=bl;break;case 4:s=Gd;break;case 16:s=Si;break;case 536870912:s=Kd;break;default:s=Si}s=fg(s,rg.bind(null,t))}t.callbackPriority=o,t.callbackNode=s}}function rg(t,o){if(gs=-1,hs=0,(De&6)!==0)throw Error(r(327));var s=t.callbackNode;if(Xr()&&t.callbackNode!==s)return null;var u=Ri(t,t===Je?it:0);if(u===0)return null;if((u&30)!==0||(u&t.expiredLanes)!==0||o)o=ms(t,u);else{o=u;var f=De;De|=2;var h=sg();(Je!==t||it!==o)&&(xn=null,Kr=Be()+500,fr(t,o));do try{pS();break}catch(D){ig(t,D)}while(!0);xa(),us.current=h,De=f,Ge!==null?o=0:(Je=null,it=0,o=Xe)}if(o!==0){if(o===2&&(f=Ll(t),f!==0&&(u=f,o=ou(t,f))),o===1)throw s=Ko,fr(t,0),Gn(t,u),Rt(t,Be()),s;if(o===6)Gn(t,u);else{if(f=t.current.alternate,(u&30)===0&&!dS(f)&&(o=ms(t,u),o===2&&(h=Ll(t),h!==0&&(u=h,o=ou(t,h))),o===1))throw s=Ko,fr(t,0),Gn(t,u),Rt(t,Be()),s;switch(t.finishedWork=f,t.finishedLanes=u,o){case 0:case 1:throw Error(r(345));case 2:pr(t,Et,xn);break;case 3:if(Gn(t,u),(u&130023424)===u&&(o=tu+500-Be(),10<o)){if(Ri(t,0)!==0)break;if(f=t.suspendedLanes,(f&u)!==u){mt(),t.pingedLanes|=t.suspendedLanes&f;break}t.timeoutHandle=ca(pr.bind(null,t,Et,xn),o);break}pr(t,Et,xn);break;case 4:if(Gn(t,u),(u&4194240)===u)break;for(o=t.eventTimes,f=-1;0<u;){var C=31-Gt(u);h=1<<C,C=o[C],C>f&&(f=C),u&=~h}if(u=f,u=Be()-u,u=(120>u?120:480>u?480:1080>u?1080:1920>u?1920:3e3>u?3e3:4320>u?4320:1960*cS(u/1960))-u,10<u){t.timeoutHandle=ca(pr.bind(null,t,Et,xn),u);break}pr(t,Et,xn);break;case 5:pr(t,Et,xn);break;default:throw Error(r(329))}}}return Rt(t,Be()),t.callbackNode===s?rg.bind(null,t):null}function ou(t,o){var s=Xo;return t.current.memoizedState.isDehydrated&&(fr(t,o).flags|=256),t=ms(t,o),t!==2&&(o=Et,Et=s,o!==null&&iu(o)),t}function iu(t){Et===null?Et=t:Et.push.apply(Et,t)}function dS(t){for(var o=t;;){if(o.flags&16384){var s=o.updateQueue;if(s!==null&&(s=s.stores,s!==null))for(var u=0;u<s.length;u++){var f=s[u],h=f.getSnapshot;f=f.value;try{if(!Kt(h(),f))return!1}catch{return!1}}}if(s=o.child,o.subtreeFlags&16384&&s!==null)s.return=o,o=s;else{if(o===t)break;for(;o.sibling===null;){if(o.return===null||o.return===t)return!0;o=o.return}o.sibling.return=o.return,o=o.sibling}}return!0}function Gn(t,o){for(o&=~eu,o&=~cs,t.suspendedLanes|=o,t.pingedLanes&=~o,t=t.expirationTimes;0<o;){var s=31-Gt(o),u=1<<s;t[s]=-1,o&=~u}}function og(t){if((De&6)!==0)throw Error(r(327));Xr();var o=Ri(t,0);if((o&1)===0)return Rt(t,Be()),null;var s=ms(t,o);if(t.tag!==0&&s===2){var u=Ll(t);u!==0&&(o=u,s=ou(t,u))}if(s===1)throw s=Ko,fr(t,0),Gn(t,o),Rt(t,Be()),s;if(s===6)throw Error(r(345));return t.finishedWork=t.current.alternate,t.finishedLanes=o,pr(t,Et,xn),Rt(t,Be()),null}function su(t,o){var s=De;De|=1;try{return t(o)}finally{De=s,De===0&&(Kr=Be()+500,Ui&&jn())}}function dr(t){Bn!==null&&Bn.tag===0&&(De&6)===0&&Xr();var o=De;De|=1;var s=Vt.transition,u=ke;try{if(Vt.transition=null,ke=1,t)return t()}finally{ke=u,Vt.transition=s,De=o,(De&6)===0&&jn()}}function lu(){kt=Gr.current,Le(Gr)}function fr(t,o){t.finishedWork=null,t.finishedLanes=0;var s=t.timeoutHandle;if(s!==-1&&(t.timeoutHandle=-1,z0(s)),Ge!==null)for(s=Ge.return;s!==null;){var u=s;switch(ma(u),u.tag){case 1:u=u.type.childContextTypes,u!=null&&zi();break;case 3:Ur(),Le(St),Le(lt),Da();break;case 5:Ta(u);break;case 4:Ur();break;case 13:Le(Ve);break;case 19:Le(Ve);break;case 10:Ca(u.type._context);break;case 22:case 23:lu()}s=s.return}if(Je=t,Ge=t=Kn(t.current,null),it=kt=o,Xe=0,Ko=null,eu=cs=cr=0,Et=Xo=null,lr!==null){for(o=0;o<lr.length;o++)if(s=lr[o],u=s.interleaved,u!==null){s.interleaved=null;var f=u.next,h=s.pending;if(h!==null){var C=h.next;h.next=f,u.next=C}s.pending=u}lr=null}return t}function ig(t,o){do{var s=Ge;try{if(xa(),Ji.current=rs,es){for(var u=ze.memoizedState;u!==null;){var f=u.queue;f!==null&&(f.pending=null),u=u.next}es=!1}if(ur=0,Ze=Ke=ze=null,zo=!1,Ho=0,Ja.current=null,s===null||s.return===null){Xe=1,Ko=o,Ge=null;break}e:{var h=t,C=s.return,D=s,M=o;if(o=it,D.flags|=32768,M!==null&&typeof M=="object"&&typeof M.then=="function"){var H=M,re=D,ie=re.tag;if((re.mode&1)===0&&(ie===0||ie===11||ie===15)){var ee=re.alternate;ee?(re.updateQueue=ee.updateQueue,re.memoizedState=ee.memoizedState,re.lanes=ee.lanes):(re.updateQueue=null,re.memoizedState=null)}var ge=Op(C);if(ge!==null){ge.flags&=-257,Mp(ge,C,D,h,o),ge.mode&1&&Dp(h,H,o),o=ge,M=H;var me=o.updateQueue;if(me===null){var ve=new Set;ve.add(M),o.updateQueue=ve}else me.add(M);break e}else{if((o&1)===0){Dp(h,H,o),au();break e}M=Error(r(426))}}else if(Fe&&D.mode&1){var We=Op(C);if(We!==null){(We.flags&65536)===0&&(We.flags|=256),Mp(We,C,D,h,o),wa(Br(M,D));break e}}h=M=Br(M,D),Xe!==4&&(Xe=2),Xo===null?Xo=[h]:Xo.push(h),h=C;do{switch(h.tag){case 3:h.flags|=65536,o&=-o,h.lanes|=o;var j=Tp(h,M,o);ep(h,j);break e;case 1:D=M;var k=h.type,V=h.stateNode;if((h.flags&128)===0&&(typeof k.getDerivedStateFromError=="function"||V!==null&&typeof V.componentDidCatch=="function"&&(Un===null||!Un.has(V)))){h.flags|=65536,o&=-o,h.lanes|=o;var le=Ip(h,D,o);ep(h,le);break e}}h=h.return}while(h!==null)}ag(s)}catch(ye){o=ye,Ge===s&&s!==null&&(Ge=s=s.return);continue}break}while(!0)}function sg(){var t=us.current;return us.current=rs,t===null?rs:t}function au(){(Xe===0||Xe===3||Xe===2)&&(Xe=4),Je===null||(cr&268435455)===0&&(cs&268435455)===0||Gn(Je,it)}function ms(t,o){var s=De;De|=2;var u=sg();(Je!==t||it!==o)&&(xn=null,fr(t,o));do try{fS();break}catch(f){ig(t,f)}while(!0);if(xa(),De=s,us.current=u,Ge!==null)throw Error(r(261));return Je=null,it=0,Xe}function fS(){for(;Ge!==null;)lg(Ge)}function pS(){for(;Ge!==null&&!Fw();)lg(Ge)}function lg(t){var o=dg(t.alternate,t,kt);t.memoizedProps=t.pendingProps,o===null?ag(t):Ge=o,Ja.current=null}function ag(t){var o=t;do{var s=o.alternate;if(t=o.return,(o.flags&32768)===0){if(s=iS(s,o,kt),s!==null){Ge=s;return}}else{if(s=sS(s,o),s!==null){s.flags&=32767,Ge=s;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{Xe=6,Ge=null;return}}if(o=o.sibling,o!==null){Ge=o;return}Ge=o=t}while(o!==null);Xe===0&&(Xe=5)}function pr(t,o,s){var u=ke,f=Vt.transition;try{Vt.transition=null,ke=1,gS(t,o,s,u)}finally{Vt.transition=f,ke=u}return null}function gS(t,o,s,u){do Xr();while(Bn!==null);if((De&6)!==0)throw Error(r(327));s=t.finishedWork;var f=t.finishedLanes;if(s===null)return null;if(t.finishedWork=null,t.finishedLanes=0,s===t.current)throw Error(r(177));t.callbackNode=null,t.callbackPriority=0;var h=s.lanes|s.childLanes;if(Xw(t,h),t===Je&&(Ge=Je=null,it=0),(s.subtreeFlags&2064)===0&&(s.flags&2064)===0||fs||(fs=!0,fg(Si,function(){return Xr(),null})),h=(s.flags&15990)!==0,(s.subtreeFlags&15990)!==0||h){h=Vt.transition,Vt.transition=null;var C=ke;ke=1;var D=De;De|=4,Ja.current=null,aS(t,s),Zp(s,t),N0(aa),Ti=!!la,aa=la=null,t.current=s,uS(s),jw(),De=D,ke=C,Vt.transition=h}else t.current=s;if(fs&&(fs=!1,Bn=t,ps=f),h=t.pendingLanes,h===0&&(Un=null),Hw(s.stateNode),Rt(t,Be()),o!==null)for(u=t.onRecoverableError,s=0;s<o.length;s++)f=o[s],u(f.value,{componentStack:f.stack,digest:f.digest});if(ds)throw ds=!1,t=nu,nu=null,t;return(ps&1)!==0&&t.tag!==0&&Xr(),h=t.pendingLanes,(h&1)!==0?t===ru?qo++:(qo=0,ru=t):qo=0,jn(),null}function Xr(){if(Bn!==null){var t=qd(ps),o=Vt.transition,s=ke;try{if(Vt.transition=null,ke=16>t?16:t,Bn===null)var u=!1;else{if(t=Bn,Bn=null,ps=0,(De&6)!==0)throw Error(r(331));var f=De;for(De|=4,he=t.current;he!==null;){var h=he,C=h.child;if((he.flags&16)!==0){var D=h.deletions;if(D!==null){for(var M=0;M<D.length;M++){var H=D[M];for(he=H;he!==null;){var re=he;switch(re.tag){case 0:case 11:case 15:Go(8,re,h)}var ie=re.child;if(ie!==null)ie.return=re,he=ie;else for(;he!==null;){re=he;var ee=re.sibling,ge=re.return;if(Kp(re),re===H){he=null;break}if(ee!==null){ee.return=ge,he=ee;break}he=ge}}}var me=h.alternate;if(me!==null){var ve=me.child;if(ve!==null){me.child=null;do{var We=ve.sibling;ve.sibling=null,ve=We}while(ve!==null)}}he=h}}if((h.subtreeFlags&2064)!==0&&C!==null)C.return=h,he=C;else e:for(;he!==null;){if(h=he,(h.flags&2048)!==0)switch(h.tag){case 0:case 11:case 15:Go(9,h,h.return)}var j=h.sibling;if(j!==null){j.return=h.return,he=j;break e}he=h.return}}var k=t.current;for(he=k;he!==null;){C=he;var V=C.child;if((C.subtreeFlags&2064)!==0&&V!==null)V.return=C,he=V;else e:for(C=k;he!==null;){if(D=he,(D.flags&2048)!==0)try{switch(D.tag){case 0:case 11:case 15:as(9,D)}}catch(ye){He(D,D.return,ye)}if(D===C){he=null;break e}var le=D.sibling;if(le!==null){le.return=D.return,he=le;break e}he=D.return}}if(De=f,jn(),on&&typeof on.onPostCommitFiberRoot=="function")try{on.onPostCommitFiberRoot(xi,t)}catch{}u=!0}return u}finally{ke=s,Vt.transition=o}}return!1}function ug(t,o,s){o=Br(s,o),o=Tp(t,o,1),t=zn(t,o,1),o=mt(),t!==null&&(wo(t,1,o),Rt(t,o))}function He(t,o,s){if(t.tag===3)ug(t,t,s);else for(;o!==null;){if(o.tag===3){ug(o,t,s);break}else if(o.tag===1){var u=o.stateNode;if(typeof o.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(Un===null||!Un.has(u))){t=Br(s,t),t=Ip(o,t,1),o=zn(o,t,1),t=mt(),o!==null&&(wo(o,1,t),Rt(o,t));break}}o=o.return}}function hS(t,o,s){var u=t.pingCache;u!==null&&u.delete(o),o=mt(),t.pingedLanes|=t.suspendedLanes&s,Je===t&&(it&s)===s&&(Xe===4||Xe===3&&(it&130023424)===it&&500>Be()-tu?fr(t,0):eu|=s),Rt(t,o)}function cg(t,o){o===0&&((t.mode&1)===0?o=1:(o=Ei,Ei<<=1,(Ei&130023424)===0&&(Ei=4194304)));var s=mt();t=yn(t,o),t!==null&&(wo(t,o,s),Rt(t,s))}function mS(t){var o=t.memoizedState,s=0;o!==null&&(s=o.retryLane),cg(t,s)}function vS(t,o){var s=0;switch(t.tag){case 13:var u=t.stateNode,f=t.memoizedState;f!==null&&(s=f.retryLane);break;case 19:u=t.stateNode;break;default:throw Error(r(314))}u!==null&&u.delete(o),cg(t,s)}var dg;dg=function(t,o,s){if(t!==null)if(t.memoizedProps!==o.pendingProps||St.current)Ct=!0;else{if((t.lanes&s)===0&&(o.flags&128)===0)return Ct=!1,oS(t,o,s);Ct=(t.flags&131072)!==0}else Ct=!1,Fe&&(o.flags&1048576)!==0&&Bf(o,Wi,o.index);switch(o.lanes=0,o.tag){case 2:var u=o.type;ss(t,o),t=o.pendingProps;var f=br(o,lt.current);Hr(o,s),f=ka(null,o,u,t,f,s);var h=$a();return o.flags|=1,typeof f=="object"&&f!==null&&typeof f.render=="function"&&f.$$typeof===void 0?(o.tag=1,o.memoizedState=null,o.updateQueue=null,xt(u)?(h=!0,Hi(o)):h=!1,o.memoizedState=f.state!==null&&f.state!==void 0?f.state:null,Pa(o),f.updater=os,o.stateNode=f,f._reactInternals=o,ja(o,u,t,s),o=Ua(null,o,u,!0,h,s)):(o.tag=0,Fe&&h&&ha(o),ht(null,o,f,s),o=o.child),o;case 16:u=o.elementType;e:{switch(ss(t,o),t=o.pendingProps,f=u._init,u=f(u._payload),o.type=u,f=o.tag=wS(u),t=qt(u,t),f){case 0:o=Ha(null,o,u,t,s);break e;case 1:o=Lp(null,o,u,t,s);break e;case 11:o=kp(null,o,u,t,s);break e;case 14:o=$p(null,o,u,qt(u.type,t),s);break e}throw Error(r(306,u,""))}return o;case 0:return u=o.type,f=o.pendingProps,f=o.elementType===u?f:qt(u,f),Ha(t,o,u,f,s);case 1:return u=o.type,f=o.pendingProps,f=o.elementType===u?f:qt(u,f),Lp(t,o,u,f,s);case 3:e:{if(Fp(o),t===null)throw Error(r(387));u=o.pendingProps,h=o.memoizedState,f=h.element,Jf(t,o),Qi(o,u,null,s);var C=o.memoizedState;if(u=C.element,h.isDehydrated)if(h={element:u,isDehydrated:!1,cache:C.cache,pendingSuspenseBoundaries:C.pendingSuspenseBoundaries,transitions:C.transitions},o.updateQueue.baseState=h,o.memoizedState=h,o.flags&256){f=Br(Error(r(423)),o),o=jp(t,o,u,s,f);break e}else if(u!==f){f=Br(Error(r(424)),o),o=jp(t,o,u,s,f);break e}else for(Mt=bn(o.stateNode.containerInfo.firstChild),Ot=o,Fe=!0,Xt=null,s=Qf(o,null,u,s),o.child=s;s;)s.flags=s.flags&-3|4096,s=s.sibling;else{if(jr(),u===f){o=Sn(t,o,s);break e}ht(t,o,u,s)}o=o.child}return o;case 5:return np(o),t===null&&ya(o),u=o.type,f=o.pendingProps,h=t!==null?t.memoizedProps:null,C=f.children,ua(u,f)?C=null:h!==null&&ua(u,h)&&(o.flags|=32),bp(t,o),ht(t,o,C,s),o.child;case 6:return t===null&&ya(o),null;case 13:return Vp(t,o,s);case 4:return _a(o,o.stateNode.containerInfo),u=o.pendingProps,t===null?o.child=Vr(o,null,u,s):ht(t,o,u,s),o.child;case 11:return u=o.type,f=o.pendingProps,f=o.elementType===u?f:qt(u,f),kp(t,o,u,f,s);case 7:return ht(t,o,o.pendingProps,s),o.child;case 8:return ht(t,o,o.pendingProps.children,s),o.child;case 12:return ht(t,o,o.pendingProps.children,s),o.child;case 10:e:{if(u=o.type._context,f=o.pendingProps,h=o.memoizedProps,C=f.value,Ae(Xi,u._currentValue),u._currentValue=C,h!==null)if(Kt(h.value,C)){if(h.children===f.children&&!St.current){o=Sn(t,o,s);break e}}else for(h=o.child,h!==null&&(h.return=o);h!==null;){var D=h.dependencies;if(D!==null){C=h.child;for(var M=D.firstContext;M!==null;){if(M.context===u){if(h.tag===1){M=wn(-1,s&-s),M.tag=2;var H=h.updateQueue;if(H!==null){H=H.shared;var re=H.pending;re===null?M.next=M:(M.next=re.next,re.next=M),H.pending=M}}h.lanes|=s,M=h.alternate,M!==null&&(M.lanes|=s),Ea(h.return,s,o),D.lanes|=s;break}M=M.next}}else if(h.tag===10)C=h.type===o.type?null:h.child;else if(h.tag===18){if(C=h.return,C===null)throw Error(r(341));C.lanes|=s,D=C.alternate,D!==null&&(D.lanes|=s),Ea(C,s,o),C=h.sibling}else C=h.child;if(C!==null)C.return=h;else for(C=h;C!==null;){if(C===o){C=null;break}if(h=C.sibling,h!==null){h.return=C.return,C=h;break}C=C.return}h=C}ht(t,o,f.children,s),o=o.child}return o;case 9:return f=o.type,u=o.pendingProps.children,Hr(o,s),f=Ft(f),u=u(f),o.flags|=1,ht(t,o,u,s),o.child;case 14:return u=o.type,f=qt(u,o.pendingProps),f=qt(u.type,f),$p(t,o,u,f,s);case 15:return Np(t,o,o.type,o.pendingProps,s);case 17:return u=o.type,f=o.pendingProps,f=o.elementType===u?f:qt(u,f),ss(t,o),o.tag=1,xt(u)?(t=!0,Hi(o)):t=!1,Hr(o,s),Pp(o,u,f),ja(o,u,f,s),Ua(null,o,u,!0,t,s);case 19:return Hp(t,o,s);case 22:return Ap(t,o,s)}throw Error(r(156,o.tag))};function fg(t,o){return Bd(t,o)}function yS(t,o,s,u){this.tag=t,this.key=s,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=o,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function zt(t,o,s,u){return new yS(t,o,s,u)}function uu(t){return t=t.prototype,!(!t||!t.isReactComponent)}function wS(t){if(typeof t=="function")return uu(t)?1:0;if(t!=null){if(t=t.$$typeof,t===te)return 11;if(t===ae)return 14}return 2}function Kn(t,o){var s=t.alternate;return s===null?(s=zt(t.tag,o,t.key,t.mode),s.elementType=t.elementType,s.type=t.type,s.stateNode=t.stateNode,s.alternate=t,t.alternate=s):(s.pendingProps=o,s.type=t.type,s.flags=0,s.subtreeFlags=0,s.deletions=null),s.flags=t.flags&14680064,s.childLanes=t.childLanes,s.lanes=t.lanes,s.child=t.child,s.memoizedProps=t.memoizedProps,s.memoizedState=t.memoizedState,s.updateQueue=t.updateQueue,o=t.dependencies,s.dependencies=o===null?null:{lanes:o.lanes,firstContext:o.firstContext},s.sibling=t.sibling,s.index=t.index,s.ref=t.ref,s}function vs(t,o,s,u,f,h){var C=2;if(u=t,typeof t=="function")uu(t)&&(C=1);else if(typeof t=="string")C=5;else e:switch(t){case N:return gr(s.children,f,h,o);case b:C=8,f|=8;break;case K:return t=zt(12,s,o,f|2),t.elementType=K,t.lanes=h,t;case Z:return t=zt(13,s,o,f),t.elementType=Z,t.lanes=h,t;case G:return t=zt(19,s,o,f),t.elementType=G,t.lanes=h,t;case Y:return ys(s,f,h,o);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case J:C=10;break e;case ne:C=9;break e;case te:C=11;break e;case ae:C=14;break e;case W:C=16,u=null;break e}throw Error(r(130,t==null?t:typeof t,""))}return o=zt(C,s,o,f),o.elementType=t,o.type=u,o.lanes=h,o}function gr(t,o,s,u){return t=zt(7,t,u,o),t.lanes=s,t}function ys(t,o,s,u){return t=zt(22,t,u,o),t.elementType=Y,t.lanes=s,t.stateNode={isHidden:!1},t}function cu(t,o,s){return t=zt(6,t,null,o),t.lanes=s,t}function du(t,o,s){return o=zt(4,t.children!==null?t.children:[],t.key,o),o.lanes=s,o.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},o}function SS(t,o,s,u,f){this.tag=o,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Fl(0),this.expirationTimes=Fl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Fl(0),this.identifierPrefix=u,this.onRecoverableError=f,this.mutableSourceEagerHydrationData=null}function fu(t,o,s,u,f,h,C,D,M){return t=new SS(t,o,s,D,M),o===1?(o=1,h===!0&&(o|=8)):o=0,h=zt(3,null,null,o),t.current=h,h.stateNode=t,h.memoizedState={element:u,isDehydrated:s,cache:null,transitions:null,pendingSuspenseBoundaries:null},Pa(h),t}function xS(t,o,s){var u=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:B,key:u==null?null:""+u,children:t,containerInfo:o,implementation:s}}function pg(t){if(!t)return Fn;t=t._reactInternals;e:{if(nr(t)!==t||t.tag!==1)throw Error(r(170));var o=t;do{switch(o.tag){case 3:o=o.stateNode.context;break e;case 1:if(xt(o.type)){o=o.stateNode.__reactInternalMemoizedMergedChildContext;break e}}o=o.return}while(o!==null);throw Error(r(171))}if(t.tag===1){var s=t.type;if(xt(s))return zf(t,s,o)}return o}function gg(t,o,s,u,f,h,C,D,M){return t=fu(s,u,!0,t,f,h,C,D,M),t.context=pg(null),s=t.current,u=mt(),f=Wn(s),h=wn(u,f),h.callback=o??null,zn(s,h,f),t.current.lanes=f,wo(t,f,u),Rt(t,u),t}function ws(t,o,s,u){var f=o.current,h=mt(),C=Wn(f);return s=pg(s),o.context===null?o.context=s:o.pendingContext=s,o=wn(h,C),o.payload={element:t},u=u===void 0?null:u,u!==null&&(o.callback=u),t=zn(f,o,C),t!==null&&(Zt(t,f,C,h),Yi(t,f,C)),C}function Ss(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function hg(t,o){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var s=t.retryLane;t.retryLane=s!==0&&s<o?s:o}}function pu(t,o){hg(t,o),(t=t.alternate)&&hg(t,o)}function CS(){return null}var mg=typeof reportError=="function"?reportError:function(t){console.error(t)};function gu(t){this._internalRoot=t}xs.prototype.render=gu.prototype.render=function(t){var o=this._internalRoot;if(o===null)throw Error(r(409));ws(t,o,null,null)},xs.prototype.unmount=gu.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var o=t.containerInfo;dr(function(){ws(null,t,null,null)}),o[gn]=null}};function xs(t){this._internalRoot=t}xs.prototype.unstable_scheduleHydration=function(t){if(t){var o=Zd();t={blockedOn:null,target:t,priority:o};for(var s=0;s<$n.length&&o!==0&&o<$n[s].priority;s++);$n.splice(s,0,t),s===0&&tf(t)}};function hu(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Cs(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function vg(){}function ES(t,o,s,u,f){if(f){if(typeof u=="function"){var h=u;u=function(){var H=Ss(C);h.call(H)}}var C=gg(o,u,t,0,null,!1,!1,"",vg);return t._reactRootContainer=C,t[gn]=C.current,$o(t.nodeType===8?t.parentNode:t),dr(),C}for(;f=t.lastChild;)t.removeChild(f);if(typeof u=="function"){var D=u;u=function(){var H=Ss(M);D.call(H)}}var M=fu(t,0,!1,null,null,!1,!1,"",vg);return t._reactRootContainer=M,t[gn]=M.current,$o(t.nodeType===8?t.parentNode:t),dr(function(){ws(o,M,s,u)}),M}function Es(t,o,s,u,f){var h=s._reactRootContainer;if(h){var C=h;if(typeof f=="function"){var D=f;f=function(){var M=Ss(C);D.call(M)}}ws(o,C,t,f)}else C=ES(s,o,t,f,u);return Ss(C)}Yd=function(t){switch(t.tag){case 3:var o=t.stateNode;if(o.current.memoizedState.isDehydrated){var s=yo(o.pendingLanes);s!==0&&(jl(o,s|1),Rt(o,Be()),(De&6)===0&&(Kr=Be()+500,jn()))}break;case 13:dr(function(){var u=yn(t,1);if(u!==null){var f=mt();Zt(u,t,1,f)}}),pu(t,1)}},Vl=function(t){if(t.tag===13){var o=yn(t,134217728);if(o!==null){var s=mt();Zt(o,t,134217728,s)}pu(t,134217728)}},Qd=function(t){if(t.tag===13){var o=Wn(t),s=yn(t,o);if(s!==null){var u=mt();Zt(s,t,o,u)}pu(t,o)}},Zd=function(){return ke},Jd=function(t,o){var s=ke;try{return ke=t,o()}finally{ke=s}},kl=function(t,o,s){switch(o){case"input":if(rt(t,s),o=s.name,s.type==="radio"&&o!=null){for(s=t;s.parentNode;)s=s.parentNode;for(s=s.querySelectorAll("input[name="+JSON.stringify(""+o)+'][type="radio"]'),o=0;o<s.length;o++){var u=s[o];if(u!==t&&u.form===t.form){var f=Vi(u);if(!f)throw Error(r(90));Pe(u),rt(u,f)}}}break;case"textarea":Qe(t,s);break;case"select":o=s.value,o!=null&&pn(t,!!s.multiple,o,!1)}},Ld=su,Fd=dr;var RS={usingClientEntryPoint:!1,Events:[bo,Nr,Vi,Ad,bd,su]},Yo={findFiberByHostInstance:rr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},PS={bundleType:Yo.bundleType,version:Yo.version,rendererPackageName:Yo.rendererPackageName,rendererConfig:Yo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:$.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=Hd(t),t===null?null:t.stateNode},findFiberByHostInstance:Yo.findFiberByHostInstance||CS,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Rs=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Rs.isDisabled&&Rs.supportsFiber)try{xi=Rs.inject(PS),on=Rs}catch{}}return Pt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=RS,Pt.createPortal=function(t,o){var s=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!hu(o))throw Error(r(200));return xS(t,o,null,s)},Pt.createRoot=function(t,o){if(!hu(t))throw Error(r(299));var s=!1,u="",f=mg;return o!=null&&(o.unstable_strictMode===!0&&(s=!0),o.identifierPrefix!==void 0&&(u=o.identifierPrefix),o.onRecoverableError!==void 0&&(f=o.onRecoverableError)),o=fu(t,1,!1,null,null,s,!1,u,f),t[gn]=o.current,$o(t.nodeType===8?t.parentNode:t),new gu(o)},Pt.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var o=t._reactInternals;if(o===void 0)throw typeof t.render=="function"?Error(r(188)):(t=Object.keys(t).join(","),Error(r(268,t)));return t=Hd(o),t=t===null?null:t.stateNode,t},Pt.flushSync=function(t){return dr(t)},Pt.hydrate=function(t,o,s){if(!Cs(o))throw Error(r(200));return Es(null,t,o,!0,s)},Pt.hydrateRoot=function(t,o,s){if(!hu(t))throw Error(r(405));var u=s!=null&&s.hydratedSources||null,f=!1,h="",C=mg;if(s!=null&&(s.unstable_strictMode===!0&&(f=!0),s.identifierPrefix!==void 0&&(h=s.identifierPrefix),s.onRecoverableError!==void 0&&(C=s.onRecoverableError)),o=gg(o,null,t,1,s??null,f,!1,h,C),t[gn]=o.current,$o(t),u)for(t=0;t<u.length;t++)s=u[t],f=s._getVersion,f=f(s._source),o.mutableSourceEagerHydrationData==null?o.mutableSourceEagerHydrationData=[s,f]:o.mutableSourceEagerHydrationData.push(s,f);return new xs(o)},Pt.render=function(t,o,s){if(!Cs(o))throw Error(r(200));return Es(null,t,o,!1,s)},Pt.unmountComponentAtNode=function(t){if(!Cs(t))throw Error(r(40));return t._reactRootContainer?(dr(function(){Es(null,null,t,!1,function(){t._reactRootContainer=null,t[gn]=null})}),!0):!1},Pt.unstable_batchedUpdates=su,Pt.unstable_renderSubtreeIntoContainer=function(t,o,s,u){if(!Cs(s))throw Error(r(200));if(t==null||t._reactInternals===void 0)throw Error(r(38));return Es(t,o,s,!1,u)},Pt.version="18.3.1-next-f1338f8080-20240426",Pt}var Pg;function Gh(){if(Pg)return yu.exports;Pg=1;function e(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(n){console.error(n)}}return e(),yu.exports=$S(),yu.exports}var _g;function NS(){if(_g)return Ps;_g=1;var e=Gh();return Ps.createRoot=e.createRoot,Ps.hydrateRoot=e.hydrateRoot,Ps}var AS=NS();const bS=lo(AS);function Se(e,n,{checkForDefaultPrevented:r=!0}={}){return function(a){if(e==null||e(a),r===!1||!a.defaultPrevented)return n==null?void 0:n(a)}}function Tg(e,n){if(typeof e=="function")return e(n);e!=null&&(e.current=n)}function rl(...e){return n=>{let r=!1;const i=e.map(a=>{const l=Tg(a,n);return!r&&typeof l=="function"&&(r=!0),l});if(r)return()=>{for(let a=0;a<i.length;a++){const l=i[a];typeof l=="function"?l():Tg(e[a],null)}}}}function Ye(...e){return p.useCallback(rl(...e),e)}function LS(e,n){const r=p.createContext(n),i=l=>{const{children:c,...d}=l,g=p.useMemo(()=>d,Object.values(d));return R.jsx(r.Provider,{value:g,children:c})};i.displayName=e+"Provider";function a(l){const c=p.useContext(r);if(c)return c;if(n!==void 0)return n;throw new Error(`\`${l}\` must be used within \`${e}\``)}return[i,a]}function tr(e,n=[]){let r=[];function i(l,c){const d=p.createContext(c),g=r.length;r=[...r,c];const v=m=>{var _;const{scope:S,children:y,...P}=m,x=((_=S==null?void 0:S[e])==null?void 0:_[g])||d,E=p.useMemo(()=>P,Object.values(P));return R.jsx(x.Provider,{value:E,children:y})};v.displayName=l+"Provider";function w(m,S){var x;const y=((x=S==null?void 0:S[e])==null?void 0:x[g])||d,P=p.useContext(y);if(P)return P;if(c!==void 0)return c;throw new Error(`\`${m}\` must be used within \`${l}\``)}return[v,w]}const a=()=>{const l=r.map(c=>p.createContext(c));return function(d){const g=(d==null?void 0:d[e])||l;return p.useMemo(()=>({[`__scope${e}`]:{...d,[e]:g}}),[d,g])}};return a.scopeName=e,[i,FS(a,...n)]}function FS(...e){const n=e[0];if(e.length===1)return n;const r=()=>{const i=e.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(l){const c=i.reduce((d,{useScope:g,scopeName:v})=>{const m=g(l)[`__scope${v}`];return{...d,...m}},{});return p.useMemo(()=>({[`__scope${n.scopeName}`]:c}),[c])}};return r.scopeName=n.scopeName,r}var Pn=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},jS=bc[" useId ".trim().toString()]||(()=>{}),VS=0;function no(e){const[n,r]=p.useState(jS());return Pn(()=>{r(i=>i??String(VS++))},[e]),e||(n?`radix-${n}`:"")}var zS=bc[" useInsertionEffect ".trim().toString()]||Pn;function ol({prop:e,defaultProp:n,onChange:r=()=>{},caller:i}){const[a,l,c]=HS({defaultProp:n,onChange:r}),d=e!==void 0,g=d?e:a;{const w=p.useRef(e!==void 0);p.useEffect(()=>{const m=w.current;m!==d&&console.warn(`${i} is changing from ${m?"controlled":"uncontrolled"} to ${d?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),w.current=d},[d,i])}const v=p.useCallback(w=>{var m;if(d){const S=US(w)?w(e):w;S!==e&&((m=c.current)==null||m.call(c,S))}else l(w)},[d,e,l,c]);return[g,v]}function HS({defaultProp:e,onChange:n}){const[r,i]=p.useState(e),a=p.useRef(r),l=p.useRef(n);return zS(()=>{l.current=n},[n]),p.useEffect(()=>{var c;a.current!==r&&((c=l.current)==null||c.call(l,r),a.current=r)},[r,a]),[r,i,l]}function US(e){return typeof e=="function"}var wr=Gh();const Kh=lo(wr);function ii(e){const n=BS(e),r=p.forwardRef((i,a)=>{const{children:l,...c}=i,d=p.Children.toArray(l),g=d.find(GS);if(g){const v=g.props.children,w=d.map(m=>m===g?p.Children.count(v)>1?p.Children.only(null):p.isValidElement(v)?v.props.children:null:m);return R.jsx(n,{...c,ref:a,children:p.isValidElement(v)?p.cloneElement(v,void 0,w):null})}return R.jsx(n,{...c,ref:a,children:l})});return r.displayName=`${e}.Slot`,r}function BS(e){const n=p.forwardRef((r,i)=>{const{children:a,...l}=r;if(p.isValidElement(a)){const c=XS(a),d=KS(l,a.props);return a.type!==p.Fragment&&(d.ref=i?rl(i,c):c),p.cloneElement(a,d)}return p.Children.count(a)>1?p.Children.only(null):null});return n.displayName=`${e}.SlotClone`,n}var WS=Symbol("radix.slottable");function GS(e){return p.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===WS}function KS(e,n){const r={...n};for(const i in n){const a=e[i],l=n[i];/^on[A-Z]/.test(i)?a&&l?r[i]=(...d)=>{const g=l(...d);return a(...d),g}:a&&(r[i]=a):i==="style"?r[i]={...a,...l}:i==="className"&&(r[i]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}function XS(e){var i,a;let n=(i=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:i.get,r=n&&"isReactWarning"in n&&n.isReactWarning;return r?e.ref:(n=(a=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:a.get,r=n&&"isReactWarning"in n&&n.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var qS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],$e=qS.reduce((e,n)=>{const r=ii(`Primitive.${n}`),i=p.forwardRef((a,l)=>{const{asChild:c,...d}=a,g=c?r:n;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),R.jsx(g,{...d,ref:l})});return i.displayName=`Primitive.${n}`,{...e,[n]:i}},{});function Lc(e,n){e&&wr.flushSync(()=>e.dispatchEvent(n))}function Tt(e){const n=p.useRef(e);return p.useEffect(()=>{n.current=e}),p.useMemo(()=>(...r)=>{var i;return(i=n.current)==null?void 0:i.call(n,...r)},[])}function YS(e,n=globalThis==null?void 0:globalThis.document){const r=Tt(e);p.useEffect(()=>{const i=a=>{a.key==="Escape"&&r(a)};return n.addEventListener("keydown",i,{capture:!0}),()=>n.removeEventListener("keydown",i,{capture:!0})},[r,n])}var QS="DismissableLayer",rc="dismissableLayer.update",ZS="dismissableLayer.pointerDownOutside",JS="dismissableLayer.focusOutside",Ig,Xh=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),il=p.forwardRef((e,n)=>{const{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:i,onPointerDownOutside:a,onFocusOutside:l,onInteractOutside:c,onDismiss:d,...g}=e,v=p.useContext(Xh),[w,m]=p.useState(null),S=(w==null?void 0:w.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,y]=p.useState({}),P=Ye(n,N=>m(N)),x=Array.from(v.layers),[E]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),_=x.indexOf(E),T=w?x.indexOf(w):-1,I=v.layersWithOutsidePointerEventsDisabled.size>0,$=T>=_,L=tx(N=>{const b=N.target,K=[...v.branches].some(J=>J.contains(b));!$||K||(a==null||a(N),c==null||c(N),N.defaultPrevented||d==null||d())},S),B=nx(N=>{const b=N.target;[...v.branches].some(J=>J.contains(b))||(l==null||l(N),c==null||c(N),N.defaultPrevented||d==null||d())},S);return YS(N=>{T===v.layers.size-1&&(i==null||i(N),!N.defaultPrevented&&d&&(N.preventDefault(),d()))},S),p.useEffect(()=>{if(w)return r&&(v.layersWithOutsidePointerEventsDisabled.size===0&&(Ig=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(w)),v.layers.add(w),Dg(),()=>{r&&v.layersWithOutsidePointerEventsDisabled.size===1&&(S.body.style.pointerEvents=Ig)}},[w,S,r,v]),p.useEffect(()=>()=>{w&&(v.layers.delete(w),v.layersWithOutsidePointerEventsDisabled.delete(w),Dg())},[w,v]),p.useEffect(()=>{const N=()=>y({});return document.addEventListener(rc,N),()=>document.removeEventListener(rc,N)},[]),R.jsx($e.div,{...g,ref:P,style:{pointerEvents:I?$?"auto":"none":void 0,...e.style},onFocusCapture:Se(e.onFocusCapture,B.onFocusCapture),onBlurCapture:Se(e.onBlurCapture,B.onBlurCapture),onPointerDownCapture:Se(e.onPointerDownCapture,L.onPointerDownCapture)})});il.displayName=QS;var ex="DismissableLayerBranch",qh=p.forwardRef((e,n)=>{const r=p.useContext(Xh),i=p.useRef(null),a=Ye(n,i);return p.useEffect(()=>{const l=i.current;if(l)return r.branches.add(l),()=>{r.branches.delete(l)}},[r.branches]),R.jsx($e.div,{...e,ref:a})});qh.displayName=ex;function tx(e,n=globalThis==null?void 0:globalThis.document){const r=Tt(e),i=p.useRef(!1),a=p.useRef(()=>{});return p.useEffect(()=>{const l=d=>{if(d.target&&!i.current){let g=function(){Yh(ZS,r,v,{discrete:!0})};const v={originalEvent:d};d.pointerType==="touch"?(n.removeEventListener("click",a.current),a.current=g,n.addEventListener("click",a.current,{once:!0})):g()}else n.removeEventListener("click",a.current);i.current=!1},c=window.setTimeout(()=>{n.addEventListener("pointerdown",l)},0);return()=>{window.clearTimeout(c),n.removeEventListener("pointerdown",l),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}function nx(e,n=globalThis==null?void 0:globalThis.document){const r=Tt(e),i=p.useRef(!1);return p.useEffect(()=>{const a=l=>{l.target&&!i.current&&Yh(JS,r,{originalEvent:l},{discrete:!1})};return n.addEventListener("focusin",a),()=>n.removeEventListener("focusin",a)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}function Dg(){const e=new CustomEvent(rc);document.dispatchEvent(e)}function Yh(e,n,r,{discrete:i}){const a=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});n&&a.addEventListener(e,n,{once:!0}),i?Lc(a,l):a.dispatchEvent(l)}var rx=il,ox=qh,xu="focusScope.autoFocusOnMount",Cu="focusScope.autoFocusOnUnmount",Og={bubbles:!1,cancelable:!0},ix="FocusScope",Fc=p.forwardRef((e,n)=>{const{loop:r=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:l,...c}=e,[d,g]=p.useState(null),v=Tt(a),w=Tt(l),m=p.useRef(null),S=Ye(n,x=>g(x)),y=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(i){let x=function(I){if(y.paused||!d)return;const $=I.target;d.contains($)?m.current=$:qn(m.current,{select:!0})},E=function(I){if(y.paused||!d)return;const $=I.relatedTarget;$!==null&&(d.contains($)||qn(m.current,{select:!0}))},_=function(I){if(document.activeElement===document.body)for(const L of I)L.removedNodes.length>0&&qn(d)};document.addEventListener("focusin",x),document.addEventListener("focusout",E);const T=new MutationObserver(_);return d&&T.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",x),document.removeEventListener("focusout",E),T.disconnect()}}},[i,d,y.paused]),p.useEffect(()=>{if(d){kg.add(y);const x=document.activeElement;if(!d.contains(x)){const _=new CustomEvent(xu,Og);d.addEventListener(xu,v),d.dispatchEvent(_),_.defaultPrevented||(sx(dx(Qh(d)),{select:!0}),document.activeElement===x&&qn(d))}return()=>{d.removeEventListener(xu,v),setTimeout(()=>{const _=new CustomEvent(Cu,Og);d.addEventListener(Cu,w),d.dispatchEvent(_),_.defaultPrevented||qn(x??document.body,{select:!0}),d.removeEventListener(Cu,w),kg.remove(y)},0)}}},[d,v,w,y]);const P=p.useCallback(x=>{if(!r&&!i||y.paused)return;const E=x.key==="Tab"&&!x.altKey&&!x.ctrlKey&&!x.metaKey,_=document.activeElement;if(E&&_){const T=x.currentTarget,[I,$]=lx(T);I&&$?!x.shiftKey&&_===$?(x.preventDefault(),r&&qn(I,{select:!0})):x.shiftKey&&_===I&&(x.preventDefault(),r&&qn($,{select:!0})):_===T&&x.preventDefault()}},[r,i,y.paused]);return R.jsx($e.div,{tabIndex:-1,...c,ref:S,onKeyDown:P})});Fc.displayName=ix;function sx(e,{select:n=!1}={}){const r=document.activeElement;for(const i of e)if(qn(i,{select:n}),document.activeElement!==r)return}function lx(e){const n=Qh(e),r=Mg(n,e),i=Mg(n.reverse(),e);return[r,i]}function Qh(e){const n=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const a=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||a?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)n.push(r.currentNode);return n}function Mg(e,n){for(const r of e)if(!ax(r,{upTo:n}))return r}function ax(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function ux(e){return e instanceof HTMLInputElement&&"select"in e}function qn(e,{select:n=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&ux(e)&&n&&e.select()}}var kg=cx();function cx(){let e=[];return{add(n){const r=e[0];n!==r&&(r==null||r.pause()),e=$g(e,n),e.unshift(n)},remove(n){var r;e=$g(e,n),(r=e[0])==null||r.resume()}}}function $g(e,n){const r=[...e],i=r.indexOf(n);return i!==-1&&r.splice(i,1),r}function dx(e){return e.filter(n=>n.tagName!=="A")}var fx="Portal",sl=p.forwardRef((e,n)=>{var d;const{container:r,...i}=e,[a,l]=p.useState(!1);Pn(()=>l(!0),[]);const c=r||a&&((d=globalThis==null?void 0:globalThis.document)==null?void 0:d.body);return c?Kh.createPortal(R.jsx($e.div,{...i,ref:n}),c):null});sl.displayName=fx;function px(e,n){return p.useReducer((r,i)=>n[r][i]??r,e)}var Dn=e=>{const{present:n,children:r}=e,i=gx(n),a=typeof r=="function"?r({present:i.isPresent}):p.Children.only(r),l=Ye(i.ref,hx(a));return typeof r=="function"||i.isPresent?p.cloneElement(a,{ref:l}):null};Dn.displayName="Presence";function gx(e){const[n,r]=p.useState(),i=p.useRef(null),a=p.useRef(e),l=p.useRef("none"),c=e?"mounted":"unmounted",[d,g]=px(c,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return p.useEffect(()=>{const v=_s(i.current);l.current=d==="mounted"?v:"none"},[d]),Pn(()=>{const v=i.current,w=a.current;if(w!==e){const S=l.current,y=_s(v);e?g("MOUNT"):y==="none"||(v==null?void 0:v.display)==="none"?g("UNMOUNT"):g(w&&S!==y?"ANIMATION_OUT":"UNMOUNT"),a.current=e}},[e,g]),Pn(()=>{if(n){let v;const w=n.ownerDocument.defaultView??window,m=y=>{const x=_s(i.current).includes(y.animationName);if(y.target===n&&x&&(g("ANIMATION_END"),!a.current)){const E=n.style.animationFillMode;n.style.animationFillMode="forwards",v=w.setTimeout(()=>{n.style.animationFillMode==="forwards"&&(n.style.animationFillMode=E)})}},S=y=>{y.target===n&&(l.current=_s(i.current))};return n.addEventListener("animationstart",S),n.addEventListener("animationcancel",m),n.addEventListener("animationend",m),()=>{w.clearTimeout(v),n.removeEventListener("animationstart",S),n.removeEventListener("animationcancel",m),n.removeEventListener("animationend",m)}}else g("ANIMATION_END")},[n,g]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:p.useCallback(v=>{i.current=v?getComputedStyle(v):null,r(v)},[])}}function _s(e){return(e==null?void 0:e.animationName)||"none"}function hx(e){var i,a;let n=(i=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:i.get,r=n&&"isReactWarning"in n&&n.isReactWarning;return r?e.ref:(n=(a=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:a.get,r=n&&"isReactWarning"in n&&n.isReactWarning,r?e.props.ref:e.props.ref||e.ref)}var Eu=0;function Zh(){p.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ng()),document.body.insertAdjacentElement("beforeend",e[1]??Ng()),Eu++,()=>{Eu===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(n=>n.remove()),Eu--}},[])}function Ng(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ft=function(){return ft=Object.assign||function(n){for(var r,i=1,a=arguments.length;i<a;i++){r=arguments[i];for(var l in r)Object.prototype.hasOwnProperty.call(r,l)&&(n[l]=r[l])}return n},ft.apply(this,arguments)};function jc(e,n){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&n.indexOf(i)<0&&(r[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,i=Object.getOwnPropertySymbols(e);a<i.length;a++)n.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(r[i[a]]=e[i[a]]);return r}function Jh(e,n,r){if(r||arguments.length===2)for(var i=0,a=n.length,l;i<a;i++)(l||!(i in n))&&(l||(l=Array.prototype.slice.call(n,0,i)),l[i]=n[i]);return e.concat(l||Array.prototype.slice.call(n))}var ti="right-scroll-bar-position",ni="width-before-scroll-bar",mx="with-scroll-bars-hidden",vx="--removed-body-scroll-bar-size";function Ru(e,n){return typeof e=="function"?e(n):e&&(e.current=n),e}function yx(e,n){var r=p.useState(function(){return{value:e,callback:n,facade:{get current(){return r.value},set current(i){var a=r.value;a!==i&&(r.value=i,r.callback(i,a))}}}})[0];return r.callback=n,r.facade}var wx=typeof window<"u"?p.useLayoutEffect:p.useEffect,Ag=new WeakMap;function em(e,n){var r=yx(null,function(i){return e.forEach(function(a){return Ru(a,i)})});return wx(function(){var i=Ag.get(r);if(i){var a=new Set(i),l=new Set(e),c=r.current;a.forEach(function(d){l.has(d)||Ru(d,null)}),l.forEach(function(d){a.has(d)||Ru(d,c)})}Ag.set(r,e)},[e]),r}function Sx(e){return e}function xx(e,n){n===void 0&&(n=Sx);var r=[],i=!1,a={read:function(){if(i)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:e},useMedium:function(l){var c=n(l,i);return r.push(c),function(){r=r.filter(function(d){return d!==c})}},assignSyncMedium:function(l){for(i=!0;r.length;){var c=r;r=[],c.forEach(l)}r={push:function(d){return l(d)},filter:function(){return r}}},assignMedium:function(l){i=!0;var c=[];if(r.length){var d=r;r=[],d.forEach(l),c=r}var g=function(){var w=c;c=[],w.forEach(l)},v=function(){return Promise.resolve().then(g)};v(),r={push:function(w){c.push(w),v()},filter:function(w){return c=c.filter(w),r}}}};return a}function tm(e){e===void 0&&(e={});var n=xx(null);return n.options=ft({async:!0,ssr:!1},e),n}var nm=function(e){var n=e.sideCar,r=jc(e,["sideCar"]);if(!n)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var i=n.read();if(!i)throw new Error("Sidecar medium not found");return p.createElement(i,ft({},r))};nm.isSideCarExport=!0;function rm(e,n){return e.useMedium(n),nm}var om=tm(),Pu=function(){},ll=p.forwardRef(function(e,n){var r=p.useRef(null),i=p.useState({onScrollCapture:Pu,onWheelCapture:Pu,onTouchMoveCapture:Pu}),a=i[0],l=i[1],c=e.forwardProps,d=e.children,g=e.className,v=e.removeScrollBar,w=e.enabled,m=e.shards,S=e.sideCar,y=e.noRelative,P=e.noIsolation,x=e.inert,E=e.allowPinchZoom,_=e.as,T=_===void 0?"div":_,I=e.gapMode,$=jc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=S,B=em([r,n]),N=ft(ft({},$),a);return p.createElement(p.Fragment,null,w&&p.createElement(L,{sideCar:om,removeScrollBar:v,shards:m,noRelative:y,noIsolation:P,inert:x,setCallbacks:l,allowPinchZoom:!!E,lockRef:r,gapMode:I}),c?p.cloneElement(p.Children.only(d),ft(ft({},N),{ref:B})):p.createElement(T,ft({},N,{className:g,ref:B}),d))});ll.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};ll.classNames={fullWidth:ni,zeroRight:ti};var Cx=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function Ex(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=Cx();return n&&e.setAttribute("nonce",n),e}function Rx(e,n){e.styleSheet?e.styleSheet.cssText=n:e.appendChild(document.createTextNode(n))}function Px(e){var n=document.head||document.getElementsByTagName("head")[0];n.appendChild(e)}var _x=function(){var e=0,n=null;return{add:function(r){e==0&&(n=Ex())&&(Rx(n,r),Px(n)),e++},remove:function(){e--,!e&&n&&(n.parentNode&&n.parentNode.removeChild(n),n=null)}}},Tx=function(){var e=_x();return function(n,r){p.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&r])}},Vc=function(){var e=Tx(),n=function(r){var i=r.styles,a=r.dynamic;return e(i,a),null};return n},Ix={left:0,top:0,right:0,gap:0},_u=function(e){return parseInt(e||"",10)||0},Dx=function(e){var n=window.getComputedStyle(document.body),r=n[e==="padding"?"paddingLeft":"marginLeft"],i=n[e==="padding"?"paddingTop":"marginTop"],a=n[e==="padding"?"paddingRight":"marginRight"];return[_u(r),_u(i),_u(a)]},Ox=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return Ix;var n=Dx(e),r=document.documentElement.clientWidth,i=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,i-r+n[2]-n[0])}},Mx=Vc(),ro="data-scroll-locked",kx=function(e,n,r,i){var a=e.left,l=e.top,c=e.right,d=e.gap;return r===void 0&&(r="margin"),`
  .`.concat(mx,` {
   overflow: hidden `).concat(i,`;
   padding-right: `).concat(d,"px ").concat(i,`;
  }
  body[`).concat(ro,`] {
    overflow: hidden `).concat(i,`;
    overscroll-behavior: contain;
    `).concat([n&&"position: relative ".concat(i,";"),r==="margin"&&`
    padding-left: `.concat(a,`px;
    padding-top: `).concat(l,`px;
    padding-right: `).concat(c,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(d,"px ").concat(i,`;
    `),r==="padding"&&"padding-right: ".concat(d,"px ").concat(i,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ti,` {
    right: `).concat(d,"px ").concat(i,`;
  }
  
  .`).concat(ni,` {
    margin-right: `).concat(d,"px ").concat(i,`;
  }
  
  .`).concat(ti," .").concat(ti,` {
    right: 0 `).concat(i,`;
  }
  
  .`).concat(ni," .").concat(ni,` {
    margin-right: 0 `).concat(i,`;
  }
  
  body[`).concat(ro,`] {
    `).concat(vx,": ").concat(d,`px;
  }
`)},bg=function(){var e=parseInt(document.body.getAttribute(ro)||"0",10);return isFinite(e)?e:0},$x=function(){p.useEffect(function(){return document.body.setAttribute(ro,(bg()+1).toString()),function(){var e=bg()-1;e<=0?document.body.removeAttribute(ro):document.body.setAttribute(ro,e.toString())}},[])},im=function(e){var n=e.noRelative,r=e.noImportant,i=e.gapMode,a=i===void 0?"margin":i;$x();var l=p.useMemo(function(){return Ox(a)},[a]);return p.createElement(Mx,{styles:kx(l,!n,a,r?"":"!important")})},oc=!1;if(typeof window<"u")try{var Ts=Object.defineProperty({},"passive",{get:function(){return oc=!0,!0}});window.addEventListener("test",Ts,Ts),window.removeEventListener("test",Ts,Ts)}catch{oc=!1}var qr=oc?{passive:!1}:!1,Nx=function(e){return e.tagName==="TEXTAREA"},sm=function(e,n){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return r[n]!=="hidden"&&!(r.overflowY===r.overflowX&&!Nx(e)&&r[n]==="visible")},Ax=function(e){return sm(e,"overflowY")},bx=function(e){return sm(e,"overflowX")},Lg=function(e,n){var r=n.ownerDocument,i=n;do{typeof ShadowRoot<"u"&&i instanceof ShadowRoot&&(i=i.host);var a=lm(e,i);if(a){var l=am(e,i),c=l[1],d=l[2];if(c>d)return!0}i=i.parentNode}while(i&&i!==r.body);return!1},Lx=function(e){var n=e.scrollTop,r=e.scrollHeight,i=e.clientHeight;return[n,r,i]},Fx=function(e){var n=e.scrollLeft,r=e.scrollWidth,i=e.clientWidth;return[n,r,i]},lm=function(e,n){return e==="v"?Ax(n):bx(n)},am=function(e,n){return e==="v"?Lx(n):Fx(n)},jx=function(e,n){return e==="h"&&n==="rtl"?-1:1},Vx=function(e,n,r,i,a){var l=jx(e,window.getComputedStyle(n).direction),c=l*i,d=r.target,g=n.contains(d),v=!1,w=c>0,m=0,S=0;do{var y=am(e,d),P=y[0],x=y[1],E=y[2],_=x-E-l*P;(P||_)&&lm(e,d)&&(m+=_,S+=P),d=d.parentNode.host||d.parentNode}while(!g&&d!==document.body||g&&(n.contains(d)||n===d));return(w&&Math.abs(m)<1||!w&&Math.abs(S)<1)&&(v=!0),v},Is=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Fg=function(e){return[e.deltaX,e.deltaY]},jg=function(e){return e&&"current"in e?e.current:e},zx=function(e,n){return e[0]===n[0]&&e[1]===n[1]},Hx=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Ux=0,Yr=[];function Bx(e){var n=p.useRef([]),r=p.useRef([0,0]),i=p.useRef(),a=p.useState(Ux++)[0],l=p.useState(Vc)[0],c=p.useRef(e);p.useEffect(function(){c.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var x=Jh([e.lockRef.current],(e.shards||[]).map(jg),!0).filter(Boolean);return x.forEach(function(E){return E.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),x.forEach(function(E){return E.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var d=p.useCallback(function(x,E){if("touches"in x&&x.touches.length===2||x.type==="wheel"&&x.ctrlKey)return!c.current.allowPinchZoom;var _=Is(x),T=r.current,I="deltaX"in x?x.deltaX:T[0]-_[0],$="deltaY"in x?x.deltaY:T[1]-_[1],L,B=x.target,N=Math.abs(I)>Math.abs($)?"h":"v";if("touches"in x&&N==="h"&&B.type==="range")return!1;var b=Lg(N,B);if(!b)return!0;if(b?L=N:(L=N==="v"?"h":"v",b=Lg(N,B)),!b)return!1;if(!i.current&&"changedTouches"in x&&(I||$)&&(i.current=L),!L)return!0;var K=i.current||L;return Vx(K,E,x,K==="h"?I:$)},[]),g=p.useCallback(function(x){var E=x;if(!(!Yr.length||Yr[Yr.length-1]!==l)){var _="deltaY"in E?Fg(E):Is(E),T=n.current.filter(function(L){return L.name===E.type&&(L.target===E.target||E.target===L.shadowParent)&&zx(L.delta,_)})[0];if(T&&T.should){E.cancelable&&E.preventDefault();return}if(!T){var I=(c.current.shards||[]).map(jg).filter(Boolean).filter(function(L){return L.contains(E.target)}),$=I.length>0?d(E,I[0]):!c.current.noIsolation;$&&E.cancelable&&E.preventDefault()}}},[]),v=p.useCallback(function(x,E,_,T){var I={name:x,delta:E,target:_,should:T,shadowParent:Wx(_)};n.current.push(I),setTimeout(function(){n.current=n.current.filter(function($){return $!==I})},1)},[]),w=p.useCallback(function(x){r.current=Is(x),i.current=void 0},[]),m=p.useCallback(function(x){v(x.type,Fg(x),x.target,d(x,e.lockRef.current))},[]),S=p.useCallback(function(x){v(x.type,Is(x),x.target,d(x,e.lockRef.current))},[]);p.useEffect(function(){return Yr.push(l),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:S}),document.addEventListener("wheel",g,qr),document.addEventListener("touchmove",g,qr),document.addEventListener("touchstart",w,qr),function(){Yr=Yr.filter(function(x){return x!==l}),document.removeEventListener("wheel",g,qr),document.removeEventListener("touchmove",g,qr),document.removeEventListener("touchstart",w,qr)}},[]);var y=e.removeScrollBar,P=e.inert;return p.createElement(p.Fragment,null,P?p.createElement(l,{styles:Hx(a)}):null,y?p.createElement(im,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}function Wx(e){for(var n=null;e!==null;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}const Gx=rm(om,Bx);var zc=p.forwardRef(function(e,n){return p.createElement(ll,ft({},e,{ref:n,sideCar:Gx}))});zc.classNames=ll.classNames;var Kx=function(e){if(typeof document>"u")return null;var n=Array.isArray(e)?e[0]:e;return n.ownerDocument.body},Qr=new WeakMap,Ds=new WeakMap,Os={},Tu=0,um=function(e){return e&&(e.host||um(e.parentNode))},Xx=function(e,n){return n.map(function(r){if(e.contains(r))return r;var i=um(r);return i&&e.contains(i)?i:(console.error("aria-hidden",r,"in not contained inside",e,". Doing nothing"),null)}).filter(function(r){return!!r})},qx=function(e,n,r,i){var a=Xx(n,Array.isArray(e)?e:[e]);Os[r]||(Os[r]=new WeakMap);var l=Os[r],c=[],d=new Set,g=new Set(a),v=function(m){!m||d.has(m)||(d.add(m),v(m.parentNode))};a.forEach(v);var w=function(m){!m||g.has(m)||Array.prototype.forEach.call(m.children,function(S){if(d.has(S))w(S);else try{var y=S.getAttribute(i),P=y!==null&&y!=="false",x=(Qr.get(S)||0)+1,E=(l.get(S)||0)+1;Qr.set(S,x),l.set(S,E),c.push(S),x===1&&P&&Ds.set(S,!0),E===1&&S.setAttribute(r,"true"),P||S.setAttribute(i,"true")}catch(_){console.error("aria-hidden: cannot operate on ",S,_)}})};return w(n),d.clear(),Tu++,function(){c.forEach(function(m){var S=Qr.get(m)-1,y=l.get(m)-1;Qr.set(m,S),l.set(m,y),S||(Ds.has(m)||m.removeAttribute(i),Ds.delete(m)),y||m.removeAttribute(r)}),Tu--,Tu||(Qr=new WeakMap,Qr=new WeakMap,Ds=new WeakMap,Os={})}},Hc=function(e,n,r){r===void 0&&(r="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),a=Kx(e);return a?(i.push.apply(i,Array.from(a.querySelectorAll("[aria-live], script"))),qx(i,a,r,"aria-hidden")):function(){return null}},al="Dialog",[cm,_O]=tr(al),[Yx,nn]=cm(al),dm=e=>{const{__scopeDialog:n,children:r,open:i,defaultOpen:a,onOpenChange:l,modal:c=!0}=e,d=p.useRef(null),g=p.useRef(null),[v,w]=ol({prop:i,defaultProp:a??!1,onChange:l,caller:al});return R.jsx(Yx,{scope:n,triggerRef:d,contentRef:g,contentId:no(),titleId:no(),descriptionId:no(),open:v,onOpenChange:w,onOpenToggle:p.useCallback(()=>w(m=>!m),[w]),modal:c,children:r})};dm.displayName=al;var fm="DialogTrigger",pm=p.forwardRef((e,n)=>{const{__scopeDialog:r,...i}=e,a=nn(fm,r),l=Ye(n,a.triggerRef);return R.jsx($e.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Wc(a.open),...i,ref:l,onClick:Se(e.onClick,a.onOpenToggle)})});pm.displayName=fm;var Uc="DialogPortal",[Qx,gm]=cm(Uc,{forceMount:void 0}),hm=e=>{const{__scopeDialog:n,forceMount:r,children:i,container:a}=e,l=nn(Uc,n);return R.jsx(Qx,{scope:n,forceMount:r,children:p.Children.map(i,c=>R.jsx(Dn,{present:r||l.open,children:R.jsx(sl,{asChild:!0,container:a,children:c})}))})};hm.displayName=Uc;var Gs="DialogOverlay",mm=p.forwardRef((e,n)=>{const r=gm(Gs,e.__scopeDialog),{forceMount:i=r.forceMount,...a}=e,l=nn(Gs,e.__scopeDialog);return l.modal?R.jsx(Dn,{present:i||l.open,children:R.jsx(Jx,{...a,ref:n})}):null});mm.displayName=Gs;var Zx=ii("DialogOverlay.RemoveScroll"),Jx=p.forwardRef((e,n)=>{const{__scopeDialog:r,...i}=e,a=nn(Gs,r);return R.jsx(zc,{as:Zx,allowPinchZoom:!0,shards:[a.contentRef],children:R.jsx($e.div,{"data-state":Wc(a.open),...i,ref:n,style:{pointerEvents:"auto",...i.style}})})}),mr="DialogContent",vm=p.forwardRef((e,n)=>{const r=gm(mr,e.__scopeDialog),{forceMount:i=r.forceMount,...a}=e,l=nn(mr,e.__scopeDialog);return R.jsx(Dn,{present:i||l.open,children:l.modal?R.jsx(eC,{...a,ref:n}):R.jsx(tC,{...a,ref:n})})});vm.displayName=mr;var eC=p.forwardRef((e,n)=>{const r=nn(mr,e.__scopeDialog),i=p.useRef(null),a=Ye(n,r.contentRef,i);return p.useEffect(()=>{const l=i.current;if(l)return Hc(l)},[]),R.jsx(ym,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:Se(e.onCloseAutoFocus,l=>{var c;l.preventDefault(),(c=r.triggerRef.current)==null||c.focus()}),onPointerDownOutside:Se(e.onPointerDownOutside,l=>{const c=l.detail.originalEvent,d=c.button===0&&c.ctrlKey===!0;(c.button===2||d)&&l.preventDefault()}),onFocusOutside:Se(e.onFocusOutside,l=>l.preventDefault())})}),tC=p.forwardRef((e,n)=>{const r=nn(mr,e.__scopeDialog),i=p.useRef(!1),a=p.useRef(!1);return R.jsx(ym,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:l=>{var c,d;(c=e.onCloseAutoFocus)==null||c.call(e,l),l.defaultPrevented||(i.current||(d=r.triggerRef.current)==null||d.focus(),l.preventDefault()),i.current=!1,a.current=!1},onInteractOutside:l=>{var g,v;(g=e.onInteractOutside)==null||g.call(e,l),l.defaultPrevented||(i.current=!0,l.detail.originalEvent.type==="pointerdown"&&(a.current=!0));const c=l.target;((v=r.triggerRef.current)==null?void 0:v.contains(c))&&l.preventDefault(),l.detail.originalEvent.type==="focusin"&&a.current&&l.preventDefault()}})}),ym=p.forwardRef((e,n)=>{const{__scopeDialog:r,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:l,...c}=e,d=nn(mr,r),g=p.useRef(null),v=Ye(n,g);return Zh(),R.jsxs(R.Fragment,{children:[R.jsx(Fc,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:l,children:R.jsx(il,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Wc(d.open),...c,ref:v,onDismiss:()=>d.onOpenChange(!1)})}),R.jsxs(R.Fragment,{children:[R.jsx(rC,{titleId:d.titleId}),R.jsx(iC,{contentRef:g,descriptionId:d.descriptionId})]})]})}),Bc="DialogTitle",wm=p.forwardRef((e,n)=>{const{__scopeDialog:r,...i}=e,a=nn(Bc,r);return R.jsx($e.h2,{id:a.titleId,...i,ref:n})});wm.displayName=Bc;var Sm="DialogDescription",nC=p.forwardRef((e,n)=>{const{__scopeDialog:r,...i}=e,a=nn(Sm,r);return R.jsx($e.p,{id:a.descriptionId,...i,ref:n})});nC.displayName=Sm;var xm="DialogClose",Cm=p.forwardRef((e,n)=>{const{__scopeDialog:r,...i}=e,a=nn(xm,r);return R.jsx($e.button,{type:"button",...i,ref:n,onClick:Se(e.onClick,()=>a.onOpenChange(!1))})});Cm.displayName=xm;function Wc(e){return e?"open":"closed"}var Em="DialogTitleWarning",[TO,Rm]=LS(Em,{contentName:mr,titleName:Bc,docsSlug:"dialog"}),rC=({titleId:e})=>{const n=Rm(Em),r=`\`${n.contentName}\` requires a \`${n.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${n.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${n.docsSlug}`;return p.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},oC="DialogDescriptionWarning",iC=({contentRef:e,descriptionId:n})=>{const i=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Rm(oC).contentName}}.`;return p.useEffect(()=>{var l;const a=(l=e.current)==null?void 0:l.getAttribute("aria-describedby");n&&a&&(document.getElementById(n)||console.warn(i))},[i,e,n]),null},Pm=dm,sC=pm,lC=hm,_m=mm,Tm=vm,Im=wm,ic=Cm;/**
   * table-core
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function Qn(e,n){return typeof e=="function"?e(n):e}function At(e,n){return r=>{n.setState(i=>({...i,[e]:Qn(r,i[e])}))}}function ul(e){return e instanceof Function}function aC(e){return Array.isArray(e)&&e.every(n=>typeof n=="number")}function uC(e,n){const r=[],i=a=>{a.forEach(l=>{r.push(l);const c=n(l);c!=null&&c.length&&i(c)})};return i(e),r}function Ee(e,n,r){let i=[],a;return l=>{let c;r.key&&r.debug&&(c=Date.now());const d=e(l);if(!(d.length!==i.length||d.some((w,m)=>i[m]!==w)))return a;i=d;let v;if(r.key&&r.debug&&(v=Date.now()),a=n(...d),r==null||r.onChange==null||r.onChange(a),r.key&&r.debug&&r!=null&&r.debug()){const w=Math.round((Date.now()-c)*100)/100,m=Math.round((Date.now()-v)*100)/100,S=m/16,y=(P,x)=>{for(P=String(P);P.length<x;)P=" "+P;return P};console.info(`%c⏱ ${y(m,5)} /${y(w,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*S,120))}deg 100% 31%);`,r==null?void 0:r.key)}return a}}function Re(e,n,r,i){return{debug:()=>{var a;return(a=e==null?void 0:e.debugAll)!=null?a:e[n]},key:!1,onChange:i}}function cC(e,n,r,i){const a=()=>{var c;return(c=l.getValue())!=null?c:e.options.renderFallbackValue},l={id:`${n.id}_${r.id}`,row:n,column:r,getValue:()=>n.getValue(i),renderValue:a,getContext:Ee(()=>[e,r,n,l],(c,d,g,v)=>({table:c,column:d,row:g,cell:v,getValue:v.getValue,renderValue:v.renderValue}),Re(e.options,"debugCells"))};return e._features.forEach(c=>{c.createCell==null||c.createCell(l,r,n,e)},{}),l}function dC(e,n,r,i){var a,l;const d={...e._getDefaultColumnDef(),...n},g=d.accessorKey;let v=(a=(l=d.id)!=null?l:g?typeof String.prototype.replaceAll=="function"?g.replaceAll(".","_"):g.replace(/\./g,"_"):void 0)!=null?a:typeof d.header=="string"?d.header:void 0,w;if(d.accessorFn?w=d.accessorFn:g&&(g.includes(".")?w=S=>{let y=S;for(const x of g.split(".")){var P;y=(P=y)==null?void 0:P[x]}return y}:w=S=>S[d.accessorKey]),!v)throw new Error;let m={id:`${String(v)}`,accessorFn:w,parent:i,depth:r,columnDef:d,columns:[],getFlatColumns:Ee(()=>[!0],()=>{var S;return[m,...(S=m.columns)==null?void 0:S.flatMap(y=>y.getFlatColumns())]},Re(e.options,"debugColumns")),getLeafColumns:Ee(()=>[e._getOrderColumnsFn()],S=>{var y;if((y=m.columns)!=null&&y.length){let P=m.columns.flatMap(x=>x.getLeafColumns());return S(P)}return[m]},Re(e.options,"debugColumns"))};for(const S of e._features)S.createColumn==null||S.createColumn(m,e);return m}const dt="debugHeaders";function Vg(e,n,r){var i;let l={id:(i=r.id)!=null?i:n.id,column:n,index:r.index,isPlaceholder:!!r.isPlaceholder,placeholderId:r.placeholderId,depth:r.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const c=[],d=g=>{g.subHeaders&&g.subHeaders.length&&g.subHeaders.map(d),c.push(g)};return d(l),c},getContext:()=>({table:e,header:l,column:n})};return e._features.forEach(c=>{c.createHeader==null||c.createHeader(l,e)}),l}const fC={createTable:e=>{e.getHeaderGroups=Ee(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,r,i,a)=>{var l,c;const d=(l=i==null?void 0:i.map(m=>r.find(S=>S.id===m)).filter(Boolean))!=null?l:[],g=(c=a==null?void 0:a.map(m=>r.find(S=>S.id===m)).filter(Boolean))!=null?c:[],v=r.filter(m=>!(i!=null&&i.includes(m.id))&&!(a!=null&&a.includes(m.id)));return Ms(n,[...d,...v,...g],e)},Re(e.options,dt)),e.getCenterHeaderGroups=Ee(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,r,i,a)=>(r=r.filter(l=>!(i!=null&&i.includes(l.id))&&!(a!=null&&a.includes(l.id))),Ms(n,r,e,"center")),Re(e.options,dt)),e.getLeftHeaderGroups=Ee(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(n,r,i)=>{var a;const l=(a=i==null?void 0:i.map(c=>r.find(d=>d.id===c)).filter(Boolean))!=null?a:[];return Ms(n,l,e,"left")},Re(e.options,dt)),e.getRightHeaderGroups=Ee(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(n,r,i)=>{var a;const l=(a=i==null?void 0:i.map(c=>r.find(d=>d.id===c)).filter(Boolean))!=null?a:[];return Ms(n,l,e,"right")},Re(e.options,dt)),e.getFooterGroups=Ee(()=>[e.getHeaderGroups()],n=>[...n].reverse(),Re(e.options,dt)),e.getLeftFooterGroups=Ee(()=>[e.getLeftHeaderGroups()],n=>[...n].reverse(),Re(e.options,dt)),e.getCenterFooterGroups=Ee(()=>[e.getCenterHeaderGroups()],n=>[...n].reverse(),Re(e.options,dt)),e.getRightFooterGroups=Ee(()=>[e.getRightHeaderGroups()],n=>[...n].reverse(),Re(e.options,dt)),e.getFlatHeaders=Ee(()=>[e.getHeaderGroups()],n=>n.map(r=>r.headers).flat(),Re(e.options,dt)),e.getLeftFlatHeaders=Ee(()=>[e.getLeftHeaderGroups()],n=>n.map(r=>r.headers).flat(),Re(e.options,dt)),e.getCenterFlatHeaders=Ee(()=>[e.getCenterHeaderGroups()],n=>n.map(r=>r.headers).flat(),Re(e.options,dt)),e.getRightFlatHeaders=Ee(()=>[e.getRightHeaderGroups()],n=>n.map(r=>r.headers).flat(),Re(e.options,dt)),e.getCenterLeafHeaders=Ee(()=>[e.getCenterFlatHeaders()],n=>n.filter(r=>{var i;return!((i=r.subHeaders)!=null&&i.length)}),Re(e.options,dt)),e.getLeftLeafHeaders=Ee(()=>[e.getLeftFlatHeaders()],n=>n.filter(r=>{var i;return!((i=r.subHeaders)!=null&&i.length)}),Re(e.options,dt)),e.getRightLeafHeaders=Ee(()=>[e.getRightFlatHeaders()],n=>n.filter(r=>{var i;return!((i=r.subHeaders)!=null&&i.length)}),Re(e.options,dt)),e.getLeafHeaders=Ee(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(n,r,i)=>{var a,l,c,d,g,v;return[...(a=(l=n[0])==null?void 0:l.headers)!=null?a:[],...(c=(d=r[0])==null?void 0:d.headers)!=null?c:[],...(g=(v=i[0])==null?void 0:v.headers)!=null?g:[]].map(w=>w.getLeafHeaders()).flat()},Re(e.options,dt))}};function Ms(e,n,r,i){var a,l;let c=0;const d=function(S,y){y===void 0&&(y=1),c=Math.max(c,y),S.filter(P=>P.getIsVisible()).forEach(P=>{var x;(x=P.columns)!=null&&x.length&&d(P.columns,y+1)},0)};d(e);let g=[];const v=(S,y)=>{const P={depth:y,id:[i,`${y}`].filter(Boolean).join("_"),headers:[]},x=[];S.forEach(E=>{const _=[...x].reverse()[0],T=E.column.depth===P.depth;let I,$=!1;if(T&&E.column.parent?I=E.column.parent:(I=E.column,$=!0),_&&(_==null?void 0:_.column)===I)_.subHeaders.push(E);else{const L=Vg(r,I,{id:[i,y,I.id,E==null?void 0:E.id].filter(Boolean).join("_"),isPlaceholder:$,placeholderId:$?`${x.filter(B=>B.column===I).length}`:void 0,depth:y,index:x.length});L.subHeaders.push(E),x.push(L)}P.headers.push(E),E.headerGroup=P}),g.push(P),y>0&&v(x,y-1)},w=n.map((S,y)=>Vg(r,S,{depth:c,index:y}));v(w,c-1),g.reverse();const m=S=>S.filter(P=>P.column.getIsVisible()).map(P=>{let x=0,E=0,_=[0];P.subHeaders&&P.subHeaders.length?(_=[],m(P.subHeaders).forEach(I=>{let{colSpan:$,rowSpan:L}=I;x+=$,_.push(L)})):x=1;const T=Math.min(..._);return E=E+T,P.colSpan=x,P.rowSpan=E,{colSpan:x,rowSpan:E}});return m((a=(l=g[0])==null?void 0:l.headers)!=null?a:[]),g}const Gc=(e,n,r,i,a,l,c)=>{let d={id:n,index:i,original:r,depth:a,parentId:c,_valuesCache:{},_uniqueValuesCache:{},getValue:g=>{if(d._valuesCache.hasOwnProperty(g))return d._valuesCache[g];const v=e.getColumn(g);if(v!=null&&v.accessorFn)return d._valuesCache[g]=v.accessorFn(d.original,i),d._valuesCache[g]},getUniqueValues:g=>{if(d._uniqueValuesCache.hasOwnProperty(g))return d._uniqueValuesCache[g];const v=e.getColumn(g);if(v!=null&&v.accessorFn)return v.columnDef.getUniqueValues?(d._uniqueValuesCache[g]=v.columnDef.getUniqueValues(d.original,i),d._uniqueValuesCache[g]):(d._uniqueValuesCache[g]=[d.getValue(g)],d._uniqueValuesCache[g])},renderValue:g=>{var v;return(v=d.getValue(g))!=null?v:e.options.renderFallbackValue},subRows:[],getLeafRows:()=>uC(d.subRows,g=>g.subRows),getParentRow:()=>d.parentId?e.getRow(d.parentId,!0):void 0,getParentRows:()=>{let g=[],v=d;for(;;){const w=v.getParentRow();if(!w)break;g.push(w),v=w}return g.reverse()},getAllCells:Ee(()=>[e.getAllLeafColumns()],g=>g.map(v=>cC(e,d,v,v.id)),Re(e.options,"debugRows")),_getAllCellsByColumnId:Ee(()=>[d.getAllCells()],g=>g.reduce((v,w)=>(v[w.column.id]=w,v),{}),Re(e.options,"debugRows"))};for(let g=0;g<e._features.length;g++){const v=e._features[g];v==null||v.createRow==null||v.createRow(d,e)}return d},pC={createColumn:(e,n)=>{e._getFacetedRowModel=n.options.getFacetedRowModel&&n.options.getFacetedRowModel(n,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():n.getPreFilteredRowModel(),e._getFacetedUniqueValues=n.options.getFacetedUniqueValues&&n.options.getFacetedUniqueValues(n,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=n.options.getFacetedMinMaxValues&&n.options.getFacetedMinMaxValues(n,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},Dm=(e,n,r)=>{var i,a;const l=r==null||(i=r.toString())==null?void 0:i.toLowerCase();return!!(!((a=e.getValue(n))==null||(a=a.toString())==null||(a=a.toLowerCase())==null)&&a.includes(l))};Dm.autoRemove=e=>Jt(e);const Om=(e,n,r)=>{var i;return!!(!((i=e.getValue(n))==null||(i=i.toString())==null)&&i.includes(r))};Om.autoRemove=e=>Jt(e);const Mm=(e,n,r)=>{var i;return((i=e.getValue(n))==null||(i=i.toString())==null?void 0:i.toLowerCase())===(r==null?void 0:r.toLowerCase())};Mm.autoRemove=e=>Jt(e);const km=(e,n,r)=>{var i;return(i=e.getValue(n))==null?void 0:i.includes(r)};km.autoRemove=e=>Jt(e);const $m=(e,n,r)=>!r.some(i=>{var a;return!((a=e.getValue(n))!=null&&a.includes(i))});$m.autoRemove=e=>Jt(e)||!(e!=null&&e.length);const Nm=(e,n,r)=>r.some(i=>{var a;return(a=e.getValue(n))==null?void 0:a.includes(i)});Nm.autoRemove=e=>Jt(e)||!(e!=null&&e.length);const Am=(e,n,r)=>e.getValue(n)===r;Am.autoRemove=e=>Jt(e);const bm=(e,n,r)=>e.getValue(n)==r;bm.autoRemove=e=>Jt(e);const Kc=(e,n,r)=>{let[i,a]=r;const l=e.getValue(n);return l>=i&&l<=a};Kc.resolveFilterValue=e=>{let[n,r]=e,i=typeof n!="number"?parseFloat(n):n,a=typeof r!="number"?parseFloat(r):r,l=n===null||Number.isNaN(i)?-1/0:i,c=r===null||Number.isNaN(a)?1/0:a;if(l>c){const d=l;l=c,c=d}return[l,c]};Kc.autoRemove=e=>Jt(e)||Jt(e[0])&&Jt(e[1]);const Cn={includesString:Dm,includesStringSensitive:Om,equalsString:Mm,arrIncludes:km,arrIncludesAll:$m,arrIncludesSome:Nm,equals:Am,weakEquals:bm,inNumberRange:Kc};function Jt(e){return e==null||e===""}const gC={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:At("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,n)=>{e.getAutoFilterFn=()=>{const r=n.getCoreRowModel().flatRows[0],i=r==null?void 0:r.getValue(e.id);return typeof i=="string"?Cn.includesString:typeof i=="number"?Cn.inNumberRange:typeof i=="boolean"||i!==null&&typeof i=="object"?Cn.equals:Array.isArray(i)?Cn.arrIncludes:Cn.weakEquals},e.getFilterFn=()=>{var r,i;return ul(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(r=(i=n.options.filterFns)==null?void 0:i[e.columnDef.filterFn])!=null?r:Cn[e.columnDef.filterFn]},e.getCanFilter=()=>{var r,i,a;return((r=e.columnDef.enableColumnFilter)!=null?r:!0)&&((i=n.options.enableColumnFilters)!=null?i:!0)&&((a=n.options.enableFilters)!=null?a:!0)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var r;return(r=n.getState().columnFilters)==null||(r=r.find(i=>i.id===e.id))==null?void 0:r.value},e.getFilterIndex=()=>{var r,i;return(r=(i=n.getState().columnFilters)==null?void 0:i.findIndex(a=>a.id===e.id))!=null?r:-1},e.setFilterValue=r=>{n.setColumnFilters(i=>{const a=e.getFilterFn(),l=i==null?void 0:i.find(w=>w.id===e.id),c=Qn(r,l?l.value:void 0);if(zg(a,c,e)){var d;return(d=i==null?void 0:i.filter(w=>w.id!==e.id))!=null?d:[]}const g={id:e.id,value:c};if(l){var v;return(v=i==null?void 0:i.map(w=>w.id===e.id?g:w))!=null?v:[]}return i!=null&&i.length?[...i,g]:[g]})}},createRow:(e,n)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=n=>{const r=e.getAllLeafColumns(),i=a=>{var l;return(l=Qn(n,a))==null?void 0:l.filter(c=>{const d=r.find(g=>g.id===c.id);if(d){const g=d.getFilterFn();if(zg(g,c.value,d))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(i)},e.resetColumnFilters=n=>{var r,i;e.setColumnFilters(n?[]:(r=(i=e.initialState)==null?void 0:i.columnFilters)!=null?r:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel())}};function zg(e,n,r){return(e&&e.autoRemove?e.autoRemove(n,r):!1)||typeof n>"u"||typeof n=="string"&&!n}const hC=(e,n,r)=>r.reduce((i,a)=>{const l=a.getValue(e);return i+(typeof l=="number"?l:0)},0),mC=(e,n,r)=>{let i;return r.forEach(a=>{const l=a.getValue(e);l!=null&&(i>l||i===void 0&&l>=l)&&(i=l)}),i},vC=(e,n,r)=>{let i;return r.forEach(a=>{const l=a.getValue(e);l!=null&&(i<l||i===void 0&&l>=l)&&(i=l)}),i},yC=(e,n,r)=>{let i,a;return r.forEach(l=>{const c=l.getValue(e);c!=null&&(i===void 0?c>=c&&(i=a=c):(i>c&&(i=c),a<c&&(a=c)))}),[i,a]},wC=(e,n)=>{let r=0,i=0;if(n.forEach(a=>{let l=a.getValue(e);l!=null&&(l=+l)>=l&&(++r,i+=l)}),r)return i/r},SC=(e,n)=>{if(!n.length)return;const r=n.map(l=>l.getValue(e));if(!aC(r))return;if(r.length===1)return r[0];const i=Math.floor(r.length/2),a=r.sort((l,c)=>l-c);return r.length%2!==0?a[i]:(a[i-1]+a[i])/2},xC=(e,n)=>Array.from(new Set(n.map(r=>r.getValue(e))).values()),CC=(e,n)=>new Set(n.map(r=>r.getValue(e))).size,EC=(e,n)=>n.length,Iu={sum:hC,min:mC,max:vC,extent:yC,mean:wC,median:SC,unique:xC,uniqueCount:CC,count:EC},RC={getDefaultColumnDef:()=>({aggregatedCell:e=>{var n,r;return(n=(r=e.getValue())==null||r.toString==null?void 0:r.toString())!=null?n:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:At("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,n)=>{e.toggleGrouping=()=>{n.setGrouping(r=>r!=null&&r.includes(e.id)?r.filter(i=>i!==e.id):[...r??[],e.id])},e.getCanGroup=()=>{var r,i;return((r=e.columnDef.enableGrouping)!=null?r:!0)&&((i=n.options.enableGrouping)!=null?i:!0)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var r;return(r=n.getState().grouping)==null?void 0:r.includes(e.id)},e.getGroupedIndex=()=>{var r;return(r=n.getState().grouping)==null?void 0:r.indexOf(e.id)},e.getToggleGroupingHandler=()=>{const r=e.getCanGroup();return()=>{r&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{const r=n.getCoreRowModel().flatRows[0],i=r==null?void 0:r.getValue(e.id);if(typeof i=="number")return Iu.sum;if(Object.prototype.toString.call(i)==="[object Date]")return Iu.extent},e.getAggregationFn=()=>{var r,i;if(!e)throw new Error;return ul(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(r=(i=n.options.aggregationFns)==null?void 0:i[e.columnDef.aggregationFn])!=null?r:Iu[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=n=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(n),e.resetGrouping=n=>{var r,i;e.setGrouping(n?[]:(r=(i=e.initialState)==null?void 0:i.grouping)!=null?r:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())},createRow:(e,n)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=r=>{if(e._groupingValuesCache.hasOwnProperty(r))return e._groupingValuesCache[r];const i=n.getColumn(r);return i!=null&&i.columnDef.getGroupingValue?(e._groupingValuesCache[r]=i.columnDef.getGroupingValue(e.original),e._groupingValuesCache[r]):e.getValue(r)},e._groupingValuesCache={}},createCell:(e,n,r,i)=>{e.getIsGrouped=()=>n.getIsGrouped()&&n.id===r.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&n.getIsGrouped(),e.getIsAggregated=()=>{var a;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((a=r.subRows)!=null&&a.length)}}};function PC(e,n,r){if(!(n!=null&&n.length)||!r)return e;const i=e.filter(l=>!n.includes(l.id));return r==="remove"?i:[...n.map(l=>e.find(c=>c.id===l)).filter(Boolean),...i]}const _C={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:At("columnOrder",e)}),createColumn:(e,n)=>{e.getIndex=Ee(r=>[ri(n,r)],r=>r.findIndex(i=>i.id===e.id),Re(n.options,"debugColumns")),e.getIsFirstColumn=r=>{var i;return((i=ri(n,r)[0])==null?void 0:i.id)===e.id},e.getIsLastColumn=r=>{var i;const a=ri(n,r);return((i=a[a.length-1])==null?void 0:i.id)===e.id}},createTable:e=>{e.setColumnOrder=n=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(n),e.resetColumnOrder=n=>{var r;e.setColumnOrder(n?[]:(r=e.initialState.columnOrder)!=null?r:[])},e._getOrderColumnsFn=Ee(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(n,r,i)=>a=>{let l=[];if(!(n!=null&&n.length))l=a;else{const c=[...n],d=[...a];for(;d.length&&c.length;){const g=c.shift(),v=d.findIndex(w=>w.id===g);v>-1&&l.push(d.splice(v,1)[0])}l=[...l,...d]}return PC(l,r,i)},Re(e.options,"debugTable"))}},Du=()=>({left:[],right:[]}),TC={getInitialState:e=>({columnPinning:Du(),...e}),getDefaultOptions:e=>({onColumnPinningChange:At("columnPinning",e)}),createColumn:(e,n)=>{e.pin=r=>{const i=e.getLeafColumns().map(a=>a.id).filter(Boolean);n.setColumnPinning(a=>{var l,c;if(r==="right"){var d,g;return{left:((d=a==null?void 0:a.left)!=null?d:[]).filter(m=>!(i!=null&&i.includes(m))),right:[...((g=a==null?void 0:a.right)!=null?g:[]).filter(m=>!(i!=null&&i.includes(m))),...i]}}if(r==="left"){var v,w;return{left:[...((v=a==null?void 0:a.left)!=null?v:[]).filter(m=>!(i!=null&&i.includes(m))),...i],right:((w=a==null?void 0:a.right)!=null?w:[]).filter(m=>!(i!=null&&i.includes(m)))}}return{left:((l=a==null?void 0:a.left)!=null?l:[]).filter(m=>!(i!=null&&i.includes(m))),right:((c=a==null?void 0:a.right)!=null?c:[]).filter(m=>!(i!=null&&i.includes(m)))}})},e.getCanPin=()=>e.getLeafColumns().some(i=>{var a,l,c;return((a=i.columnDef.enablePinning)!=null?a:!0)&&((l=(c=n.options.enableColumnPinning)!=null?c:n.options.enablePinning)!=null?l:!0)}),e.getIsPinned=()=>{const r=e.getLeafColumns().map(d=>d.id),{left:i,right:a}=n.getState().columnPinning,l=r.some(d=>i==null?void 0:i.includes(d)),c=r.some(d=>a==null?void 0:a.includes(d));return l?"left":c?"right":!1},e.getPinnedIndex=()=>{var r,i;const a=e.getIsPinned();return a?(r=(i=n.getState().columnPinning)==null||(i=i[a])==null?void 0:i.indexOf(e.id))!=null?r:-1:0}},createRow:(e,n)=>{e.getCenterVisibleCells=Ee(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left,n.getState().columnPinning.right],(r,i,a)=>{const l=[...i??[],...a??[]];return r.filter(c=>!l.includes(c.column.id))},Re(n.options,"debugRows")),e.getLeftVisibleCells=Ee(()=>[e._getAllVisibleCells(),n.getState().columnPinning.left],(r,i)=>(i??[]).map(l=>r.find(c=>c.column.id===l)).filter(Boolean).map(l=>({...l,position:"left"})),Re(n.options,"debugRows")),e.getRightVisibleCells=Ee(()=>[e._getAllVisibleCells(),n.getState().columnPinning.right],(r,i)=>(i??[]).map(l=>r.find(c=>c.column.id===l)).filter(Boolean).map(l=>({...l,position:"right"})),Re(n.options,"debugRows"))},createTable:e=>{e.setColumnPinning=n=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(n),e.resetColumnPinning=n=>{var r,i;return e.setColumnPinning(n?Du():(r=(i=e.initialState)==null?void 0:i.columnPinning)!=null?r:Du())},e.getIsSomeColumnsPinned=n=>{var r;const i=e.getState().columnPinning;if(!n){var a,l;return!!((a=i.left)!=null&&a.length||(l=i.right)!=null&&l.length)}return!!((r=i[n])!=null&&r.length)},e.getLeftLeafColumns=Ee(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(n,r)=>(r??[]).map(i=>n.find(a=>a.id===i)).filter(Boolean),Re(e.options,"debugColumns")),e.getRightLeafColumns=Ee(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(n,r)=>(r??[]).map(i=>n.find(a=>a.id===i)).filter(Boolean),Re(e.options,"debugColumns")),e.getCenterLeafColumns=Ee(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(n,r,i)=>{const a=[...r??[],...i??[]];return n.filter(l=>!a.includes(l.id))},Re(e.options,"debugColumns"))}};function IC(e){return e||(typeof document<"u"?document:null)}const ks={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Ou=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),DC={getDefaultColumnDef:()=>ks,getInitialState:e=>({columnSizing:{},columnSizingInfo:Ou(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:At("columnSizing",e),onColumnSizingInfoChange:At("columnSizingInfo",e)}),createColumn:(e,n)=>{e.getSize=()=>{var r,i,a;const l=n.getState().columnSizing[e.id];return Math.min(Math.max((r=e.columnDef.minSize)!=null?r:ks.minSize,(i=l??e.columnDef.size)!=null?i:ks.size),(a=e.columnDef.maxSize)!=null?a:ks.maxSize)},e.getStart=Ee(r=>[r,ri(n,r),n.getState().columnSizing],(r,i)=>i.slice(0,e.getIndex(r)).reduce((a,l)=>a+l.getSize(),0),Re(n.options,"debugColumns")),e.getAfter=Ee(r=>[r,ri(n,r),n.getState().columnSizing],(r,i)=>i.slice(e.getIndex(r)+1).reduce((a,l)=>a+l.getSize(),0),Re(n.options,"debugColumns")),e.resetSize=()=>{n.setColumnSizing(r=>{let{[e.id]:i,...a}=r;return a})},e.getCanResize=()=>{var r,i;return((r=e.columnDef.enableResizing)!=null?r:!0)&&((i=n.options.enableColumnResizing)!=null?i:!0)},e.getIsResizing=()=>n.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,n)=>{e.getSize=()=>{let r=0;const i=a=>{if(a.subHeaders.length)a.subHeaders.forEach(i);else{var l;r+=(l=a.column.getSize())!=null?l:0}};return i(e),r},e.getStart=()=>{if(e.index>0){const r=e.headerGroup.headers[e.index-1];return r.getStart()+r.getSize()}return 0},e.getResizeHandler=r=>{const i=n.getColumn(e.column.id),a=i==null?void 0:i.getCanResize();return l=>{if(!i||!a||(l.persist==null||l.persist(),Mu(l)&&l.touches&&l.touches.length>1))return;const c=e.getSize(),d=e?e.getLeafHeaders().map(_=>[_.column.id,_.column.getSize()]):[[i.id,i.getSize()]],g=Mu(l)?Math.round(l.touches[0].clientX):l.clientX,v={},w=(_,T)=>{typeof T=="number"&&(n.setColumnSizingInfo(I=>{var $,L;const B=n.options.columnResizeDirection==="rtl"?-1:1,N=(T-(($=I==null?void 0:I.startOffset)!=null?$:0))*B,b=Math.max(N/((L=I==null?void 0:I.startSize)!=null?L:0),-.999999);return I.columnSizingStart.forEach(K=>{let[J,ne]=K;v[J]=Math.round(Math.max(ne+ne*b,0)*100)/100}),{...I,deltaOffset:N,deltaPercentage:b}}),(n.options.columnResizeMode==="onChange"||_==="end")&&n.setColumnSizing(I=>({...I,...v})))},m=_=>w("move",_),S=_=>{w("end",_),n.setColumnSizingInfo(T=>({...T,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},y=IC(r),P={moveHandler:_=>m(_.clientX),upHandler:_=>{y==null||y.removeEventListener("mousemove",P.moveHandler),y==null||y.removeEventListener("mouseup",P.upHandler),S(_.clientX)}},x={moveHandler:_=>(_.cancelable&&(_.preventDefault(),_.stopPropagation()),m(_.touches[0].clientX),!1),upHandler:_=>{var T;y==null||y.removeEventListener("touchmove",x.moveHandler),y==null||y.removeEventListener("touchend",x.upHandler),_.cancelable&&(_.preventDefault(),_.stopPropagation()),S((T=_.touches[0])==null?void 0:T.clientX)}},E=OC()?{passive:!1}:!1;Mu(l)?(y==null||y.addEventListener("touchmove",x.moveHandler,E),y==null||y.addEventListener("touchend",x.upHandler,E)):(y==null||y.addEventListener("mousemove",P.moveHandler,E),y==null||y.addEventListener("mouseup",P.upHandler,E)),n.setColumnSizingInfo(_=>({..._,startOffset:g,startSize:c,deltaOffset:0,deltaPercentage:0,columnSizingStart:d,isResizingColumn:i.id}))}}},createTable:e=>{e.setColumnSizing=n=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(n),e.setColumnSizingInfo=n=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(n),e.resetColumnSizing=n=>{var r;e.setColumnSizing(n?{}:(r=e.initialState.columnSizing)!=null?r:{})},e.resetHeaderSizeInfo=n=>{var r;e.setColumnSizingInfo(n?Ou():(r=e.initialState.columnSizingInfo)!=null?r:Ou())},e.getTotalSize=()=>{var n,r;return(n=(r=e.getHeaderGroups()[0])==null?void 0:r.headers.reduce((i,a)=>i+a.getSize(),0))!=null?n:0},e.getLeftTotalSize=()=>{var n,r;return(n=(r=e.getLeftHeaderGroups()[0])==null?void 0:r.headers.reduce((i,a)=>i+a.getSize(),0))!=null?n:0},e.getCenterTotalSize=()=>{var n,r;return(n=(r=e.getCenterHeaderGroups()[0])==null?void 0:r.headers.reduce((i,a)=>i+a.getSize(),0))!=null?n:0},e.getRightTotalSize=()=>{var n,r;return(n=(r=e.getRightHeaderGroups()[0])==null?void 0:r.headers.reduce((i,a)=>i+a.getSize(),0))!=null?n:0}}};let $s=null;function OC(){if(typeof $s=="boolean")return $s;let e=!1;try{const n={get passive(){return e=!0,!1}},r=()=>{};window.addEventListener("test",r,n),window.removeEventListener("test",r)}catch{e=!1}return $s=e,$s}function Mu(e){return e.type==="touchstart"}const MC={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:At("columnVisibility",e)}),createColumn:(e,n)=>{e.toggleVisibility=r=>{e.getCanHide()&&n.setColumnVisibility(i=>({...i,[e.id]:r??!e.getIsVisible()}))},e.getIsVisible=()=>{var r,i;const a=e.columns;return(r=a.length?a.some(l=>l.getIsVisible()):(i=n.getState().columnVisibility)==null?void 0:i[e.id])!=null?r:!0},e.getCanHide=()=>{var r,i;return((r=e.columnDef.enableHiding)!=null?r:!0)&&((i=n.options.enableHiding)!=null?i:!0)},e.getToggleVisibilityHandler=()=>r=>{e.toggleVisibility==null||e.toggleVisibility(r.target.checked)}},createRow:(e,n)=>{e._getAllVisibleCells=Ee(()=>[e.getAllCells(),n.getState().columnVisibility],r=>r.filter(i=>i.column.getIsVisible()),Re(n.options,"debugRows")),e.getVisibleCells=Ee(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(r,i,a)=>[...r,...i,...a],Re(n.options,"debugRows"))},createTable:e=>{const n=(r,i)=>Ee(()=>[i(),i().filter(a=>a.getIsVisible()).map(a=>a.id).join("_")],a=>a.filter(l=>l.getIsVisible==null?void 0:l.getIsVisible()),Re(e.options,"debugColumns"));e.getVisibleFlatColumns=n("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=n("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=n("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=n("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=n("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=r=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(r),e.resetColumnVisibility=r=>{var i;e.setColumnVisibility(r?{}:(i=e.initialState.columnVisibility)!=null?i:{})},e.toggleAllColumnsVisible=r=>{var i;r=(i=r)!=null?i:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((a,l)=>({...a,[l.id]:r||!(l.getCanHide!=null&&l.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(r=>!(r.getIsVisible!=null&&r.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(r=>r.getIsVisible==null?void 0:r.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>r=>{var i;e.toggleAllColumnsVisible((i=r.target)==null?void 0:i.checked)}}};function ri(e,n){return n?n==="center"?e.getCenterVisibleLeafColumns():n==="left"?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}const kC={createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},$C={getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:At("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:n=>{var r;const i=(r=e.getCoreRowModel().flatRows[0])==null||(r=r._getAllCellsByColumnId()[n.id])==null?void 0:r.getValue();return typeof i=="string"||typeof i=="number"}}),createColumn:(e,n)=>{e.getCanGlobalFilter=()=>{var r,i,a,l;return((r=e.columnDef.enableGlobalFilter)!=null?r:!0)&&((i=n.options.enableGlobalFilter)!=null?i:!0)&&((a=n.options.enableFilters)!=null?a:!0)&&((l=n.options.getColumnCanGlobalFilter==null?void 0:n.options.getColumnCanGlobalFilter(e))!=null?l:!0)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>Cn.includesString,e.getGlobalFilterFn=()=>{var n,r;const{globalFilterFn:i}=e.options;return ul(i)?i:i==="auto"?e.getGlobalAutoFilterFn():(n=(r=e.options.filterFns)==null?void 0:r[i])!=null?n:Cn[i]},e.setGlobalFilter=n=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(n)},e.resetGlobalFilter=n=>{e.setGlobalFilter(n?void 0:e.initialState.globalFilter)}}},NC={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:At("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let n=!1,r=!1;e._autoResetExpanded=()=>{var i,a;if(!n){e._queue(()=>{n=!0});return}if((i=(a=e.options.autoResetAll)!=null?a:e.options.autoResetExpanded)!=null?i:!e.options.manualExpanding){if(r)return;r=!0,e._queue(()=>{e.resetExpanded(),r=!1})}},e.setExpanded=i=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(i),e.toggleAllRowsExpanded=i=>{i??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=i=>{var a,l;e.setExpanded(i?{}:(a=(l=e.initialState)==null?void 0:l.expanded)!=null?a:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(i=>i.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>i=>{i.persist==null||i.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{const i=e.getState().expanded;return i===!0||Object.values(i).some(Boolean)},e.getIsAllRowsExpanded=()=>{const i=e.getState().expanded;return typeof i=="boolean"?i===!0:!(!Object.keys(i).length||e.getRowModel().flatRows.some(a=>!a.getIsExpanded()))},e.getExpandedDepth=()=>{let i=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(l=>{const c=l.split(".");i=Math.max(i,c.length)}),i},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())},createRow:(e,n)=>{e.toggleExpanded=r=>{n.setExpanded(i=>{var a;const l=i===!0?!0:!!(i!=null&&i[e.id]);let c={};if(i===!0?Object.keys(n.getRowModel().rowsById).forEach(d=>{c[d]=!0}):c=i,r=(a=r)!=null?a:!l,!l&&r)return{...c,[e.id]:!0};if(l&&!r){const{[e.id]:d,...g}=c;return g}return i})},e.getIsExpanded=()=>{var r;const i=n.getState().expanded;return!!((r=n.options.getIsRowExpanded==null?void 0:n.options.getIsRowExpanded(e))!=null?r:i===!0||i!=null&&i[e.id])},e.getCanExpand=()=>{var r,i,a;return(r=n.options.getRowCanExpand==null?void 0:n.options.getRowCanExpand(e))!=null?r:((i=n.options.enableExpanding)!=null?i:!0)&&!!((a=e.subRows)!=null&&a.length)},e.getIsAllParentsExpanded=()=>{let r=!0,i=e;for(;r&&i.parentId;)i=n.getRow(i.parentId,!0),r=i.getIsExpanded();return r},e.getToggleExpandedHandler=()=>{const r=e.getCanExpand();return()=>{r&&e.toggleExpanded()}}}},sc=0,lc=10,ku=()=>({pageIndex:sc,pageSize:lc}),AC={getInitialState:e=>({...e,pagination:{...ku(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:At("pagination",e)}),createTable:e=>{let n=!1,r=!1;e._autoResetPageIndex=()=>{var i,a;if(!n){e._queue(()=>{n=!0});return}if((i=(a=e.options.autoResetAll)!=null?a:e.options.autoResetPageIndex)!=null?i:!e.options.manualPagination){if(r)return;r=!0,e._queue(()=>{e.resetPageIndex(),r=!1})}},e.setPagination=i=>{const a=l=>Qn(i,l);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(a)},e.resetPagination=i=>{var a;e.setPagination(i?ku():(a=e.initialState.pagination)!=null?a:ku())},e.setPageIndex=i=>{e.setPagination(a=>{let l=Qn(i,a.pageIndex);const c=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return l=Math.max(0,Math.min(l,c)),{...a,pageIndex:l}})},e.resetPageIndex=i=>{var a,l;e.setPageIndex(i?sc:(a=(l=e.initialState)==null||(l=l.pagination)==null?void 0:l.pageIndex)!=null?a:sc)},e.resetPageSize=i=>{var a,l;e.setPageSize(i?lc:(a=(l=e.initialState)==null||(l=l.pagination)==null?void 0:l.pageSize)!=null?a:lc)},e.setPageSize=i=>{e.setPagination(a=>{const l=Math.max(1,Qn(i,a.pageSize)),c=a.pageSize*a.pageIndex,d=Math.floor(c/l);return{...a,pageIndex:d,pageSize:l}})},e.setPageCount=i=>e.setPagination(a=>{var l;let c=Qn(i,(l=e.options.pageCount)!=null?l:-1);return typeof c=="number"&&(c=Math.max(-1,c)),{...a,pageCount:c}}),e.getPageOptions=Ee(()=>[e.getPageCount()],i=>{let a=[];return i&&i>0&&(a=[...new Array(i)].fill(null).map((l,c)=>c)),a},Re(e.options,"debugTable")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{const{pageIndex:i}=e.getState().pagination,a=e.getPageCount();return a===-1?!0:a===0?!1:i<a-1},e.previousPage=()=>e.setPageIndex(i=>i-1),e.nextPage=()=>e.setPageIndex(i=>i+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),e.getPageCount=()=>{var i;return(i=e.options.pageCount)!=null?i:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var i;return(i=e.options.rowCount)!=null?i:e.getPrePaginationRowModel().rows.length}}},$u=()=>({top:[],bottom:[]}),bC={getInitialState:e=>({rowPinning:$u(),...e}),getDefaultOptions:e=>({onRowPinningChange:At("rowPinning",e)}),createRow:(e,n)=>{e.pin=(r,i,a)=>{const l=i?e.getLeafRows().map(g=>{let{id:v}=g;return v}):[],c=a?e.getParentRows().map(g=>{let{id:v}=g;return v}):[],d=new Set([...c,e.id,...l]);n.setRowPinning(g=>{var v,w;if(r==="bottom"){var m,S;return{top:((m=g==null?void 0:g.top)!=null?m:[]).filter(x=>!(d!=null&&d.has(x))),bottom:[...((S=g==null?void 0:g.bottom)!=null?S:[]).filter(x=>!(d!=null&&d.has(x))),...Array.from(d)]}}if(r==="top"){var y,P;return{top:[...((y=g==null?void 0:g.top)!=null?y:[]).filter(x=>!(d!=null&&d.has(x))),...Array.from(d)],bottom:((P=g==null?void 0:g.bottom)!=null?P:[]).filter(x=>!(d!=null&&d.has(x)))}}return{top:((v=g==null?void 0:g.top)!=null?v:[]).filter(x=>!(d!=null&&d.has(x))),bottom:((w=g==null?void 0:g.bottom)!=null?w:[]).filter(x=>!(d!=null&&d.has(x)))}})},e.getCanPin=()=>{var r;const{enableRowPinning:i,enablePinning:a}=n.options;return typeof i=="function"?i(e):(r=i??a)!=null?r:!0},e.getIsPinned=()=>{const r=[e.id],{top:i,bottom:a}=n.getState().rowPinning,l=r.some(d=>i==null?void 0:i.includes(d)),c=r.some(d=>a==null?void 0:a.includes(d));return l?"top":c?"bottom":!1},e.getPinnedIndex=()=>{var r,i;const a=e.getIsPinned();if(!a)return-1;const l=(r=a==="top"?n.getTopRows():n.getBottomRows())==null?void 0:r.map(c=>{let{id:d}=c;return d});return(i=l==null?void 0:l.indexOf(e.id))!=null?i:-1}},createTable:e=>{e.setRowPinning=n=>e.options.onRowPinningChange==null?void 0:e.options.onRowPinningChange(n),e.resetRowPinning=n=>{var r,i;return e.setRowPinning(n?$u():(r=(i=e.initialState)==null?void 0:i.rowPinning)!=null?r:$u())},e.getIsSomeRowsPinned=n=>{var r;const i=e.getState().rowPinning;if(!n){var a,l;return!!((a=i.top)!=null&&a.length||(l=i.bottom)!=null&&l.length)}return!!((r=i[n])!=null&&r.length)},e._getPinnedRows=(n,r,i)=>{var a;return((a=e.options.keepPinnedRows)==null||a?(r??[]).map(c=>{const d=e.getRow(c,!0);return d.getIsAllParentsExpanded()?d:null}):(r??[]).map(c=>n.find(d=>d.id===c))).filter(Boolean).map(c=>({...c,position:i}))},e.getTopRows=Ee(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(n,r)=>e._getPinnedRows(n,r,"top"),Re(e.options,"debugRows")),e.getBottomRows=Ee(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(n,r)=>e._getPinnedRows(n,r,"bottom"),Re(e.options,"debugRows")),e.getCenterRows=Ee(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(n,r,i)=>{const a=new Set([...r??[],...i??[]]);return n.filter(l=>!a.has(l.id))},Re(e.options,"debugRows"))}},LC={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:At("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=n=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(n),e.resetRowSelection=n=>{var r;return e.setRowSelection(n?{}:(r=e.initialState.rowSelection)!=null?r:{})},e.toggleAllRowsSelected=n=>{e.setRowSelection(r=>{n=typeof n<"u"?n:!e.getIsAllRowsSelected();const i={...r},a=e.getPreGroupedRowModel().flatRows;return n?a.forEach(l=>{l.getCanSelect()&&(i[l.id]=!0)}):a.forEach(l=>{delete i[l.id]}),i})},e.toggleAllPageRowsSelected=n=>e.setRowSelection(r=>{const i=typeof n<"u"?n:!e.getIsAllPageRowsSelected(),a={...r};return e.getRowModel().rows.forEach(l=>{ac(a,l.id,i,!0,e)}),a}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=Ee(()=>[e.getState().rowSelection,e.getCoreRowModel()],(n,r)=>Object.keys(n).length?Nu(e,r):{rows:[],flatRows:[],rowsById:{}},Re(e.options,"debugTable")),e.getFilteredSelectedRowModel=Ee(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(n,r)=>Object.keys(n).length?Nu(e,r):{rows:[],flatRows:[],rowsById:{}},Re(e.options,"debugTable")),e.getGroupedSelectedRowModel=Ee(()=>[e.getState().rowSelection,e.getSortedRowModel()],(n,r)=>Object.keys(n).length?Nu(e,r):{rows:[],flatRows:[],rowsById:{}},Re(e.options,"debugTable")),e.getIsAllRowsSelected=()=>{const n=e.getFilteredRowModel().flatRows,{rowSelection:r}=e.getState();let i=!!(n.length&&Object.keys(r).length);return i&&n.some(a=>a.getCanSelect()&&!r[a.id])&&(i=!1),i},e.getIsAllPageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows.filter(a=>a.getCanSelect()),{rowSelection:r}=e.getState();let i=!!n.length;return i&&n.some(a=>!r[a.id])&&(i=!1),i},e.getIsSomeRowsSelected=()=>{var n;const r=Object.keys((n=e.getState().rowSelection)!=null?n:{}).length;return r>0&&r<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{const n=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:n.filter(r=>r.getCanSelect()).some(r=>r.getIsSelected()||r.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>n=>{e.toggleAllRowsSelected(n.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>n=>{e.toggleAllPageRowsSelected(n.target.checked)}},createRow:(e,n)=>{e.toggleSelected=(r,i)=>{const a=e.getIsSelected();n.setRowSelection(l=>{var c;if(r=typeof r<"u"?r:!a,e.getCanSelect()&&a===r)return l;const d={...l};return ac(d,e.id,r,(c=i==null?void 0:i.selectChildren)!=null?c:!0,n),d})},e.getIsSelected=()=>{const{rowSelection:r}=n.getState();return Xc(e,r)},e.getIsSomeSelected=()=>{const{rowSelection:r}=n.getState();return uc(e,r)==="some"},e.getIsAllSubRowsSelected=()=>{const{rowSelection:r}=n.getState();return uc(e,r)==="all"},e.getCanSelect=()=>{var r;return typeof n.options.enableRowSelection=="function"?n.options.enableRowSelection(e):(r=n.options.enableRowSelection)!=null?r:!0},e.getCanSelectSubRows=()=>{var r;return typeof n.options.enableSubRowSelection=="function"?n.options.enableSubRowSelection(e):(r=n.options.enableSubRowSelection)!=null?r:!0},e.getCanMultiSelect=()=>{var r;return typeof n.options.enableMultiRowSelection=="function"?n.options.enableMultiRowSelection(e):(r=n.options.enableMultiRowSelection)!=null?r:!0},e.getToggleSelectedHandler=()=>{const r=e.getCanSelect();return i=>{var a;r&&e.toggleSelected((a=i.target)==null?void 0:a.checked)}}}},ac=(e,n,r,i,a)=>{var l;const c=a.getRow(n,!0);r?(c.getCanMultiSelect()||Object.keys(e).forEach(d=>delete e[d]),c.getCanSelect()&&(e[n]=!0)):delete e[n],i&&(l=c.subRows)!=null&&l.length&&c.getCanSelectSubRows()&&c.subRows.forEach(d=>ac(e,d.id,r,i,a))};function Nu(e,n){const r=e.getState().rowSelection,i=[],a={},l=function(c,d){return c.map(g=>{var v;const w=Xc(g,r);if(w&&(i.push(g),a[g.id]=g),(v=g.subRows)!=null&&v.length&&(g={...g,subRows:l(g.subRows)}),w)return g}).filter(Boolean)};return{rows:l(n.rows),flatRows:i,rowsById:a}}function Xc(e,n){var r;return(r=n[e.id])!=null?r:!1}function uc(e,n,r){var i;if(!((i=e.subRows)!=null&&i.length))return!1;let a=!0,l=!1;return e.subRows.forEach(c=>{if(!(l&&!a)&&(c.getCanSelect()&&(Xc(c,n)?l=!0:a=!1),c.subRows&&c.subRows.length)){const d=uc(c,n);d==="all"?l=!0:(d==="some"&&(l=!0),a=!1)}}),a?"all":l?"some":!1}const cc=/([0-9]+)/gm,FC=(e,n,r)=>Lm(Zn(e.getValue(r)).toLowerCase(),Zn(n.getValue(r)).toLowerCase()),jC=(e,n,r)=>Lm(Zn(e.getValue(r)),Zn(n.getValue(r))),VC=(e,n,r)=>qc(Zn(e.getValue(r)).toLowerCase(),Zn(n.getValue(r)).toLowerCase()),zC=(e,n,r)=>qc(Zn(e.getValue(r)),Zn(n.getValue(r))),HC=(e,n,r)=>{const i=e.getValue(r),a=n.getValue(r);return i>a?1:i<a?-1:0},UC=(e,n,r)=>qc(e.getValue(r),n.getValue(r));function qc(e,n){return e===n?0:e>n?1:-1}function Zn(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Lm(e,n){const r=e.split(cc).filter(Boolean),i=n.split(cc).filter(Boolean);for(;r.length&&i.length;){const a=r.shift(),l=i.shift(),c=parseInt(a,10),d=parseInt(l,10),g=[c,d].sort();if(isNaN(g[0])){if(a>l)return 1;if(l>a)return-1;continue}if(isNaN(g[1]))return isNaN(c)?-1:1;if(c>d)return 1;if(d>c)return-1}return r.length-i.length}const Zo={alphanumeric:FC,alphanumericCaseSensitive:jC,text:VC,textCaseSensitive:zC,datetime:HC,basic:UC},BC={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:At("sorting",e),isMultiSortEvent:n=>n.shiftKey}),createColumn:(e,n)=>{e.getAutoSortingFn=()=>{const r=n.getFilteredRowModel().flatRows.slice(10);let i=!1;for(const a of r){const l=a==null?void 0:a.getValue(e.id);if(Object.prototype.toString.call(l)==="[object Date]")return Zo.datetime;if(typeof l=="string"&&(i=!0,l.split(cc).length>1))return Zo.alphanumeric}return i?Zo.text:Zo.basic},e.getAutoSortDir=()=>{const r=n.getFilteredRowModel().flatRows[0];return typeof(r==null?void 0:r.getValue(e.id))=="string"?"asc":"desc"},e.getSortingFn=()=>{var r,i;if(!e)throw new Error;return ul(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(r=(i=n.options.sortingFns)==null?void 0:i[e.columnDef.sortingFn])!=null?r:Zo[e.columnDef.sortingFn]},e.toggleSorting=(r,i)=>{const a=e.getNextSortingOrder(),l=typeof r<"u"&&r!==null;n.setSorting(c=>{const d=c==null?void 0:c.find(y=>y.id===e.id),g=c==null?void 0:c.findIndex(y=>y.id===e.id);let v=[],w,m=l?r:a==="desc";if(c!=null&&c.length&&e.getCanMultiSort()&&i?d?w="toggle":w="add":c!=null&&c.length&&g!==c.length-1?w="replace":d?w="toggle":w="replace",w==="toggle"&&(l||a||(w="remove")),w==="add"){var S;v=[...c,{id:e.id,desc:m}],v.splice(0,v.length-((S=n.options.maxMultiSortColCount)!=null?S:Number.MAX_SAFE_INTEGER))}else w==="toggle"?v=c.map(y=>y.id===e.id?{...y,desc:m}:y):w==="remove"?v=c.filter(y=>y.id!==e.id):v=[{id:e.id,desc:m}];return v})},e.getFirstSortDir=()=>{var r,i;return((r=(i=e.columnDef.sortDescFirst)!=null?i:n.options.sortDescFirst)!=null?r:e.getAutoSortDir()==="desc")?"desc":"asc"},e.getNextSortingOrder=r=>{var i,a;const l=e.getFirstSortDir(),c=e.getIsSorted();return c?c!==l&&((i=n.options.enableSortingRemoval)==null||i)&&(!(r&&(a=n.options.enableMultiRemove)!=null)||a)?!1:c==="desc"?"asc":"desc":l},e.getCanSort=()=>{var r,i;return((r=e.columnDef.enableSorting)!=null?r:!0)&&((i=n.options.enableSorting)!=null?i:!0)&&!!e.accessorFn},e.getCanMultiSort=()=>{var r,i;return(r=(i=e.columnDef.enableMultiSort)!=null?i:n.options.enableMultiSort)!=null?r:!!e.accessorFn},e.getIsSorted=()=>{var r;const i=(r=n.getState().sorting)==null?void 0:r.find(a=>a.id===e.id);return i?i.desc?"desc":"asc":!1},e.getSortIndex=()=>{var r,i;return(r=(i=n.getState().sorting)==null?void 0:i.findIndex(a=>a.id===e.id))!=null?r:-1},e.clearSorting=()=>{n.setSorting(r=>r!=null&&r.length?r.filter(i=>i.id!==e.id):[])},e.getToggleSortingHandler=()=>{const r=e.getCanSort();return i=>{r&&(i.persist==null||i.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?n.options.isMultiSortEvent==null?void 0:n.options.isMultiSortEvent(i):!1))}}},createTable:e=>{e.setSorting=n=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(n),e.resetSorting=n=>{var r,i;e.setSorting(n?[]:(r=(i=e.initialState)==null?void 0:i.sorting)!=null?r:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())}},WC=[fC,MC,_C,TC,pC,gC,kC,$C,BC,RC,NC,AC,bC,LC,DC];function GC(e){var n,r;const i=[...WC,...(n=e._features)!=null?n:[]];let a={_features:i};const l=a._features.reduce((S,y)=>Object.assign(S,y.getDefaultOptions==null?void 0:y.getDefaultOptions(a)),{}),c=S=>a.options.mergeOptions?a.options.mergeOptions(l,S):{...l,...S};let g={...{},...(r=e.initialState)!=null?r:{}};a._features.forEach(S=>{var y;g=(y=S.getInitialState==null?void 0:S.getInitialState(g))!=null?y:g});const v=[];let w=!1;const m={_features:i,options:{...l,...e},initialState:g,_queue:S=>{v.push(S),w||(w=!0,Promise.resolve().then(()=>{for(;v.length;)v.shift()();w=!1}).catch(y=>setTimeout(()=>{throw y})))},reset:()=>{a.setState(a.initialState)},setOptions:S=>{const y=Qn(S,a.options);a.options=c(y)},getState:()=>a.options.state,setState:S=>{a.options.onStateChange==null||a.options.onStateChange(S)},_getRowId:(S,y,P)=>{var x;return(x=a.options.getRowId==null?void 0:a.options.getRowId(S,y,P))!=null?x:`${P?[P.id,y].join("."):y}`},getCoreRowModel:()=>(a._getCoreRowModel||(a._getCoreRowModel=a.options.getCoreRowModel(a)),a._getCoreRowModel()),getRowModel:()=>a.getPaginationRowModel(),getRow:(S,y)=>{let P=(y?a.getPrePaginationRowModel():a.getRowModel()).rowsById[S];if(!P&&(P=a.getCoreRowModel().rowsById[S],!P))throw new Error;return P},_getDefaultColumnDef:Ee(()=>[a.options.defaultColumn],S=>{var y;return S=(y=S)!=null?y:{},{header:P=>{const x=P.header.column.columnDef;return x.accessorKey?x.accessorKey:x.accessorFn?x.id:null},cell:P=>{var x,E;return(x=(E=P.renderValue())==null||E.toString==null?void 0:E.toString())!=null?x:null},...a._features.reduce((P,x)=>Object.assign(P,x.getDefaultColumnDef==null?void 0:x.getDefaultColumnDef()),{}),...S}},Re(e,"debugColumns")),_getColumnDefs:()=>a.options.columns,getAllColumns:Ee(()=>[a._getColumnDefs()],S=>{const y=function(P,x,E){return E===void 0&&(E=0),P.map(_=>{const T=dC(a,_,E,x),I=_;return T.columns=I.columns?y(I.columns,T,E+1):[],T})};return y(S)},Re(e,"debugColumns")),getAllFlatColumns:Ee(()=>[a.getAllColumns()],S=>S.flatMap(y=>y.getFlatColumns()),Re(e,"debugColumns")),_getAllFlatColumnsById:Ee(()=>[a.getAllFlatColumns()],S=>S.reduce((y,P)=>(y[P.id]=P,y),{}),Re(e,"debugColumns")),getAllLeafColumns:Ee(()=>[a.getAllColumns(),a._getOrderColumnsFn()],(S,y)=>{let P=S.flatMap(x=>x.getLeafColumns());return y(P)},Re(e,"debugColumns")),getColumn:S=>a._getAllFlatColumnsById()[S]};Object.assign(a,m);for(let S=0;S<a._features.length;S++){const y=a._features[S];y==null||y.createTable==null||y.createTable(a)}return a}function KC(){return e=>Ee(()=>[e.options.data],n=>{const r={rows:[],flatRows:[],rowsById:{}},i=function(a,l,c){l===void 0&&(l=0);const d=[];for(let v=0;v<a.length;v++){const w=Gc(e,e._getRowId(a[v],v,c),a[v],v,l,void 0,c==null?void 0:c.id);if(r.flatRows.push(w),r.rowsById[w.id]=w,d.push(w),e.options.getSubRows){var g;w.originalSubRows=e.options.getSubRows(a[v],v),(g=w.originalSubRows)!=null&&g.length&&(w.subRows=i(w.originalSubRows,l+1,w))}}return d};return r.rows=i(n),r},Re(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function XC(e){const n=[],r=i=>{var a;n.push(i),(a=i.subRows)!=null&&a.length&&i.getIsExpanded()&&i.subRows.forEach(r)};return e.rows.forEach(r),{rows:n,flatRows:e.flatRows,rowsById:e.rowsById}}function qC(e,n,r){return r.options.filterFromLeafRows?YC(e,n,r):QC(e,n,r)}function YC(e,n,r){var i;const a=[],l={},c=(i=r.options.maxLeafRowFilterDepth)!=null?i:100,d=function(g,v){v===void 0&&(v=0);const w=[];for(let S=0;S<g.length;S++){var m;let y=g[S];const P=Gc(r,y.id,y.original,y.index,y.depth,void 0,y.parentId);if(P.columnFilters=y.columnFilters,(m=y.subRows)!=null&&m.length&&v<c){if(P.subRows=d(y.subRows,v+1),y=P,n(y)&&!P.subRows.length){w.push(y),l[y.id]=y,a.push(y);continue}if(n(y)||P.subRows.length){w.push(y),l[y.id]=y,a.push(y);continue}}else y=P,n(y)&&(w.push(y),l[y.id]=y,a.push(y))}return w};return{rows:d(e),flatRows:a,rowsById:l}}function QC(e,n,r){var i;const a=[],l={},c=(i=r.options.maxLeafRowFilterDepth)!=null?i:100,d=function(g,v){v===void 0&&(v=0);const w=[];for(let S=0;S<g.length;S++){let y=g[S];if(n(y)){var m;if((m=y.subRows)!=null&&m.length&&v<c){const x=Gc(r,y.id,y.original,y.index,y.depth,void 0,y.parentId);x.subRows=d(y.subRows,v+1),y=x}w.push(y),a.push(y),l[y.id]=y}}return w};return{rows:d(e),flatRows:a,rowsById:l}}function ZC(){return e=>Ee(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(n,r,i)=>{if(!n.rows.length||!(r!=null&&r.length)&&!i){for(let S=0;S<n.flatRows.length;S++)n.flatRows[S].columnFilters={},n.flatRows[S].columnFiltersMeta={};return n}const a=[],l=[];(r??[]).forEach(S=>{var y;const P=e.getColumn(S.id);if(!P)return;const x=P.getFilterFn();x&&a.push({id:S.id,filterFn:x,resolvedValue:(y=x.resolveFilterValue==null?void 0:x.resolveFilterValue(S.value))!=null?y:S.value})});const c=(r??[]).map(S=>S.id),d=e.getGlobalFilterFn(),g=e.getAllLeafColumns().filter(S=>S.getCanGlobalFilter());i&&d&&g.length&&(c.push("__global__"),g.forEach(S=>{var y;l.push({id:S.id,filterFn:d,resolvedValue:(y=d.resolveFilterValue==null?void 0:d.resolveFilterValue(i))!=null?y:i})}));let v,w;for(let S=0;S<n.flatRows.length;S++){const y=n.flatRows[S];if(y.columnFilters={},a.length)for(let P=0;P<a.length;P++){v=a[P];const x=v.id;y.columnFilters[x]=v.filterFn(y,x,v.resolvedValue,E=>{y.columnFiltersMeta[x]=E})}if(l.length){for(let P=0;P<l.length;P++){w=l[P];const x=w.id;if(w.filterFn(y,x,w.resolvedValue,E=>{y.columnFiltersMeta[x]=E})){y.columnFilters.__global__=!0;break}}y.columnFilters.__global__!==!0&&(y.columnFilters.__global__=!1)}}const m=S=>{for(let y=0;y<c.length;y++)if(S.columnFilters[c[y]]===!1)return!1;return!0};return qC(n.rows,m,e)},Re(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function JC(e){return n=>Ee(()=>[n.getState().pagination,n.getPrePaginationRowModel(),n.options.paginateExpandedRows?void 0:n.getState().expanded],(r,i)=>{if(!i.rows.length)return i;const{pageSize:a,pageIndex:l}=r;let{rows:c,flatRows:d,rowsById:g}=i;const v=a*l,w=v+a;c=c.slice(v,w);let m;n.options.paginateExpandedRows?m={rows:c,flatRows:d,rowsById:g}:m=XC({rows:c,flatRows:d,rowsById:g}),m.flatRows=[];const S=y=>{m.flatRows.push(y),y.subRows.length&&y.subRows.forEach(S)};return m.rows.forEach(S),m},Re(n.options,"debugTable"))}function eE(){return e=>Ee(()=>[e.getState().sorting,e.getPreSortedRowModel()],(n,r)=>{if(!r.rows.length||!(n!=null&&n.length))return r;const i=e.getState().sorting,a=[],l=i.filter(g=>{var v;return(v=e.getColumn(g.id))==null?void 0:v.getCanSort()}),c={};l.forEach(g=>{const v=e.getColumn(g.id);v&&(c[g.id]={sortUndefined:v.columnDef.sortUndefined,invertSorting:v.columnDef.invertSorting,sortingFn:v.getSortingFn()})});const d=g=>{const v=g.map(w=>({...w}));return v.sort((w,m)=>{for(let y=0;y<l.length;y+=1){var S;const P=l[y],x=c[P.id],E=x.sortUndefined,_=(S=P==null?void 0:P.desc)!=null?S:!1;let T=0;if(E){const I=w.getValue(P.id),$=m.getValue(P.id),L=I===void 0,B=$===void 0;if(L||B){if(E==="first")return L?-1:1;if(E==="last")return L?1:-1;T=L&&B?0:L?E:-E}}if(T===0&&(T=x.sortingFn(w,m,P.id)),T!==0)return _&&(T*=-1),x.invertSorting&&(T*=-1),T}return w.index-m.index}),v.forEach(w=>{var m;a.push(w),(m=w.subRows)!=null&&m.length&&(w.subRows=d(w.subRows))}),v};return{rows:d(r.rows),flatRows:a,rowsById:r.rowsById}},Re(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}/**
   * react-table
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */function dc(e,n){return e?tE(e)?p.createElement(e,n):e:null}function tE(e){return nE(e)||typeof e=="function"||rE(e)}function nE(e){return typeof e=="function"&&(()=>{const n=Object.getPrototypeOf(e);return n.prototype&&n.prototype.isReactComponent})()}function rE(e){return typeof e=="object"&&typeof e.$$typeof=="symbol"&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)}function oE(e){const n={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[r]=p.useState(()=>({current:GC(n)})),[i,a]=p.useState(()=>r.current.initialState);return r.current.setOptions(l=>({...l,...e,state:{...i,...e.state},onStateChange:c=>{a(c),e.onStateChange==null||e.onStateChange(c)}})),r.current}function Fm(e){var n,r,i="";if(typeof e=="string"||typeof e=="number")i+=e;else if(typeof e=="object")if(Array.isArray(e))for(n=0;n<e.length;n++)e[n]&&(r=Fm(e[n]))&&(i&&(i+=" "),i+=r);else for(n in e)e[n]&&(i&&(i+=" "),i+=n);return i}function vt(){for(var e,n,r=0,i="";r<arguments.length;)(e=arguments[r++])&&(n=Fm(e))&&(i&&(i+=" "),i+=n);return i}var Ns={exports:{}},je={},As={exports:{}},hr={},Hg;function jm(){if(Hg)return hr;Hg=1;function e(){var l={};return l["align-content"]=!1,l["align-items"]=!1,l["align-self"]=!1,l["alignment-adjust"]=!1,l["alignment-baseline"]=!1,l.all=!1,l["anchor-point"]=!1,l.animation=!1,l["animation-delay"]=!1,l["animation-direction"]=!1,l["animation-duration"]=!1,l["animation-fill-mode"]=!1,l["animation-iteration-count"]=!1,l["animation-name"]=!1,l["animation-play-state"]=!1,l["animation-timing-function"]=!1,l.azimuth=!1,l["backface-visibility"]=!1,l.background=!0,l["background-attachment"]=!0,l["background-clip"]=!0,l["background-color"]=!0,l["background-image"]=!0,l["background-origin"]=!0,l["background-position"]=!0,l["background-repeat"]=!0,l["background-size"]=!0,l["baseline-shift"]=!1,l.binding=!1,l.bleed=!1,l["bookmark-label"]=!1,l["bookmark-level"]=!1,l["bookmark-state"]=!1,l.border=!0,l["border-bottom"]=!0,l["border-bottom-color"]=!0,l["border-bottom-left-radius"]=!0,l["border-bottom-right-radius"]=!0,l["border-bottom-style"]=!0,l["border-bottom-width"]=!0,l["border-collapse"]=!0,l["border-color"]=!0,l["border-image"]=!0,l["border-image-outset"]=!0,l["border-image-repeat"]=!0,l["border-image-slice"]=!0,l["border-image-source"]=!0,l["border-image-width"]=!0,l["border-left"]=!0,l["border-left-color"]=!0,l["border-left-style"]=!0,l["border-left-width"]=!0,l["border-radius"]=!0,l["border-right"]=!0,l["border-right-color"]=!0,l["border-right-style"]=!0,l["border-right-width"]=!0,l["border-spacing"]=!0,l["border-style"]=!0,l["border-top"]=!0,l["border-top-color"]=!0,l["border-top-left-radius"]=!0,l["border-top-right-radius"]=!0,l["border-top-style"]=!0,l["border-top-width"]=!0,l["border-width"]=!0,l.bottom=!1,l["box-decoration-break"]=!0,l["box-shadow"]=!0,l["box-sizing"]=!0,l["box-snap"]=!0,l["box-suppress"]=!0,l["break-after"]=!0,l["break-before"]=!0,l["break-inside"]=!0,l["caption-side"]=!1,l.chains=!1,l.clear=!0,l.clip=!1,l["clip-path"]=!1,l["clip-rule"]=!1,l.color=!0,l["color-interpolation-filters"]=!0,l["column-count"]=!1,l["column-fill"]=!1,l["column-gap"]=!1,l["column-rule"]=!1,l["column-rule-color"]=!1,l["column-rule-style"]=!1,l["column-rule-width"]=!1,l["column-span"]=!1,l["column-width"]=!1,l.columns=!1,l.contain=!1,l.content=!1,l["counter-increment"]=!1,l["counter-reset"]=!1,l["counter-set"]=!1,l.crop=!1,l.cue=!1,l["cue-after"]=!1,l["cue-before"]=!1,l.cursor=!1,l.direction=!1,l.display=!0,l["display-inside"]=!0,l["display-list"]=!0,l["display-outside"]=!0,l["dominant-baseline"]=!1,l.elevation=!1,l["empty-cells"]=!1,l.filter=!1,l.flex=!1,l["flex-basis"]=!1,l["flex-direction"]=!1,l["flex-flow"]=!1,l["flex-grow"]=!1,l["flex-shrink"]=!1,l["flex-wrap"]=!1,l.float=!1,l["float-offset"]=!1,l["flood-color"]=!1,l["flood-opacity"]=!1,l["flow-from"]=!1,l["flow-into"]=!1,l.font=!0,l["font-family"]=!0,l["font-feature-settings"]=!0,l["font-kerning"]=!0,l["font-language-override"]=!0,l["font-size"]=!0,l["font-size-adjust"]=!0,l["font-stretch"]=!0,l["font-style"]=!0,l["font-synthesis"]=!0,l["font-variant"]=!0,l["font-variant-alternates"]=!0,l["font-variant-caps"]=!0,l["font-variant-east-asian"]=!0,l["font-variant-ligatures"]=!0,l["font-variant-numeric"]=!0,l["font-variant-position"]=!0,l["font-weight"]=!0,l.grid=!1,l["grid-area"]=!1,l["grid-auto-columns"]=!1,l["grid-auto-flow"]=!1,l["grid-auto-rows"]=!1,l["grid-column"]=!1,l["grid-column-end"]=!1,l["grid-column-start"]=!1,l["grid-row"]=!1,l["grid-row-end"]=!1,l["grid-row-start"]=!1,l["grid-template"]=!1,l["grid-template-areas"]=!1,l["grid-template-columns"]=!1,l["grid-template-rows"]=!1,l["hanging-punctuation"]=!1,l.height=!0,l.hyphens=!1,l.icon=!1,l["image-orientation"]=!1,l["image-resolution"]=!1,l["ime-mode"]=!1,l["initial-letters"]=!1,l["inline-box-align"]=!1,l["justify-content"]=!1,l["justify-items"]=!1,l["justify-self"]=!1,l.left=!1,l["letter-spacing"]=!0,l["lighting-color"]=!0,l["line-box-contain"]=!1,l["line-break"]=!1,l["line-grid"]=!1,l["line-height"]=!1,l["line-snap"]=!1,l["line-stacking"]=!1,l["line-stacking-ruby"]=!1,l["line-stacking-shift"]=!1,l["line-stacking-strategy"]=!1,l["list-style"]=!0,l["list-style-image"]=!0,l["list-style-position"]=!0,l["list-style-type"]=!0,l.margin=!0,l["margin-bottom"]=!0,l["margin-left"]=!0,l["margin-right"]=!0,l["margin-top"]=!0,l["marker-offset"]=!1,l["marker-side"]=!1,l.marks=!1,l.mask=!1,l["mask-box"]=!1,l["mask-box-outset"]=!1,l["mask-box-repeat"]=!1,l["mask-box-slice"]=!1,l["mask-box-source"]=!1,l["mask-box-width"]=!1,l["mask-clip"]=!1,l["mask-image"]=!1,l["mask-origin"]=!1,l["mask-position"]=!1,l["mask-repeat"]=!1,l["mask-size"]=!1,l["mask-source-type"]=!1,l["mask-type"]=!1,l["max-height"]=!0,l["max-lines"]=!1,l["max-width"]=!0,l["min-height"]=!0,l["min-width"]=!0,l["move-to"]=!1,l["nav-down"]=!1,l["nav-index"]=!1,l["nav-left"]=!1,l["nav-right"]=!1,l["nav-up"]=!1,l["object-fit"]=!1,l["object-position"]=!1,l.opacity=!1,l.order=!1,l.orphans=!1,l.outline=!1,l["outline-color"]=!1,l["outline-offset"]=!1,l["outline-style"]=!1,l["outline-width"]=!1,l.overflow=!1,l["overflow-wrap"]=!1,l["overflow-x"]=!1,l["overflow-y"]=!1,l.padding=!0,l["padding-bottom"]=!0,l["padding-left"]=!0,l["padding-right"]=!0,l["padding-top"]=!0,l.page=!1,l["page-break-after"]=!1,l["page-break-before"]=!1,l["page-break-inside"]=!1,l["page-policy"]=!1,l.pause=!1,l["pause-after"]=!1,l["pause-before"]=!1,l.perspective=!1,l["perspective-origin"]=!1,l.pitch=!1,l["pitch-range"]=!1,l["play-during"]=!1,l.position=!1,l["presentation-level"]=!1,l.quotes=!1,l["region-fragment"]=!1,l.resize=!1,l.rest=!1,l["rest-after"]=!1,l["rest-before"]=!1,l.richness=!1,l.right=!1,l.rotation=!1,l["rotation-point"]=!1,l["ruby-align"]=!1,l["ruby-merge"]=!1,l["ruby-position"]=!1,l["shape-image-threshold"]=!1,l["shape-outside"]=!1,l["shape-margin"]=!1,l.size=!1,l.speak=!1,l["speak-as"]=!1,l["speak-header"]=!1,l["speak-numeral"]=!1,l["speak-punctuation"]=!1,l["speech-rate"]=!1,l.stress=!1,l["string-set"]=!1,l["tab-size"]=!1,l["table-layout"]=!1,l["text-align"]=!0,l["text-align-last"]=!0,l["text-combine-upright"]=!0,l["text-decoration"]=!0,l["text-decoration-color"]=!0,l["text-decoration-line"]=!0,l["text-decoration-skip"]=!0,l["text-decoration-style"]=!0,l["text-emphasis"]=!0,l["text-emphasis-color"]=!0,l["text-emphasis-position"]=!0,l["text-emphasis-style"]=!0,l["text-height"]=!0,l["text-indent"]=!0,l["text-justify"]=!0,l["text-orientation"]=!0,l["text-overflow"]=!0,l["text-shadow"]=!0,l["text-space-collapse"]=!0,l["text-transform"]=!0,l["text-underline-position"]=!0,l["text-wrap"]=!0,l.top=!1,l.transform=!1,l["transform-origin"]=!1,l["transform-style"]=!1,l.transition=!1,l["transition-delay"]=!1,l["transition-duration"]=!1,l["transition-property"]=!1,l["transition-timing-function"]=!1,l["unicode-bidi"]=!1,l["vertical-align"]=!1,l.visibility=!1,l["voice-balance"]=!1,l["voice-duration"]=!1,l["voice-family"]=!1,l["voice-pitch"]=!1,l["voice-range"]=!1,l["voice-rate"]=!1,l["voice-stress"]=!1,l["voice-volume"]=!1,l.volume=!1,l["white-space"]=!1,l.widows=!1,l.width=!0,l["will-change"]=!1,l["word-break"]=!0,l["word-spacing"]=!0,l["word-wrap"]=!0,l["wrap-flow"]=!1,l["wrap-through"]=!1,l["writing-mode"]=!1,l["z-index"]=!1,l}function n(l,c,d){}function r(l,c,d){}var i=/javascript\s*\:/img;function a(l,c){return i.test(c)?"":c}return hr.whiteList=e(),hr.getDefaultWhiteList=e,hr.onAttr=n,hr.onIgnoreAttr=r,hr.safeAttrValue=a,hr}var Au,Ug;function Vm(){return Ug||(Ug=1,Au={indexOf:function(e,n){var r,i;if(Array.prototype.indexOf)return e.indexOf(n);for(r=0,i=e.length;r<i;r++)if(e[r]===n)return r;return-1},forEach:function(e,n,r){var i,a;if(Array.prototype.forEach)return e.forEach(n,r);for(i=0,a=e.length;i<a;i++)n.call(r,e[i],i,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(e){return String.prototype.trimRight?e.trimRight():e.replace(/(\s*$)/g,"")}}),Au}var bu,Bg;function iE(){if(Bg)return bu;Bg=1;var e=Vm();function n(r,i){r=e.trimRight(r),r[r.length-1]!==";"&&(r+=";");var a=r.length,l=!1,c=0,d=0,g="";function v(){if(!l){var S=e.trim(r.slice(c,d)),y=S.indexOf(":");if(y!==-1){var P=e.trim(S.slice(0,y)),x=e.trim(S.slice(y+1));if(P){var E=i(c,g.length,P,x,S);E&&(g+=E+"; ")}}}c=d+1}for(;d<a;d++){var w=r[d];if(w==="/"&&r[d+1]==="*"){var m=r.indexOf("*/",d+2);if(m===-1)break;d=m+1,c=d+1,l=!1}else w==="("?l=!0:w===")"?l=!1:w===";"?l||v():w===`
`&&v()}return e.trim(g)}return bu=n,bu}var Lu,Wg;function sE(){if(Wg)return Lu;Wg=1;var e=jm(),n=iE();Vm();function r(l){return l==null}function i(l){var c={};for(var d in l)c[d]=l[d];return c}function a(l){l=i(l||{}),l.whiteList=l.whiteList||e.whiteList,l.onAttr=l.onAttr||e.onAttr,l.onIgnoreAttr=l.onIgnoreAttr||e.onIgnoreAttr,l.safeAttrValue=l.safeAttrValue||e.safeAttrValue,this.options=l}return a.prototype.process=function(l){if(l=l||"",l=l.toString(),!l)return"";var c=this,d=c.options,g=d.whiteList,v=d.onAttr,w=d.onIgnoreAttr,m=d.safeAttrValue,S=n(l,function(y,P,x,E,_){var T=g[x],I=!1;if(T===!0?I=T:typeof T=="function"?I=T(E):T instanceof RegExp&&(I=T.test(E)),I!==!0&&(I=!1),E=m(x,E),!!E){var $={position:P,sourcePosition:y,source:_,isWhite:I};if(I){var L=v(x,E,$);return r(L)?x+":"+E:L}else{var L=w(x,E,$);if(!r(L))return L}}});return S},Lu=a,Lu}var Gg;function fc(){return Gg||(Gg=1,function(e,n){var r=jm(),i=sE();function a(c,d){var g=new i(d);return g.process(c)}n=e.exports=a,n.FilterCSS=i;for(var l in r)n[l]=r[l];typeof window<"u"&&(window.filterCSS=e.exports)}(As,As.exports)),As.exports}var Fu,Kg;function Yc(){return Kg||(Kg=1,Fu={indexOf:function(e,n){var r,i;if(Array.prototype.indexOf)return e.indexOf(n);for(r=0,i=e.length;r<i;r++)if(e[r]===n)return r;return-1},forEach:function(e,n,r){var i,a;if(Array.prototype.forEach)return e.forEach(n,r);for(i=0,a=e.length;i<a;i++)n.call(r,e[i],i,e)},trim:function(e){return String.prototype.trim?e.trim():e.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(e){var n=/\s|\n|\t/,r=n.exec(e);return r?r.index:-1}}),Fu}var Xg;function zm(){if(Xg)return je;Xg=1;var e=fc().FilterCSS,n=fc().getDefaultWhiteList,r=Yc();function i(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var a=new e;function l(W,Y,A){}function c(W,Y,A){}function d(W,Y,A){}function g(W,Y,A){}function v(W){return W.replace(m,"&lt;").replace(S,"&gt;")}function w(W,Y,A,F){if(A=J(A),Y==="href"||Y==="src"){if(A=r.trim(A),A==="#")return"#";if(!(A.substr(0,7)==="http://"||A.substr(0,8)==="https://"||A.substr(0,7)==="mailto:"||A.substr(0,4)==="tel:"||A.substr(0,11)==="data:image/"||A.substr(0,6)==="ftp://"||A.substr(0,2)==="./"||A.substr(0,3)==="../"||A[0]==="#"||A[0]==="/"))return""}else if(Y==="background"){if(T.lastIndex=0,T.test(A))return""}else if(Y==="style"){if(I.lastIndex=0,I.test(A)||($.lastIndex=0,$.test(A)&&(T.lastIndex=0,T.test(A))))return"";F!==!1&&(F=F||a,A=F.process(A))}return A=ne(A),A}var m=/</g,S=/>/g,y=/"/g,P=/&quot;/g,x=/&#([a-zA-Z0-9]*);?/gim,E=/&colon;?/gim,_=/&newline;?/gim,T=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,I=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,$=/u\s*r\s*l\s*\(.*/gi;function L(W){return W.replace(y,"&quot;")}function B(W){return W.replace(P,'"')}function N(W){return W.replace(x,function(A,F){return F[0]==="x"||F[0]==="X"?String.fromCharCode(parseInt(F.substr(1),16)):String.fromCharCode(parseInt(F,10))})}function b(W){return W.replace(E,":").replace(_," ")}function K(W){for(var Y="",A=0,F=W.length;A<F;A++)Y+=W.charCodeAt(A)<32?" ":W.charAt(A);return r.trim(Y)}function J(W){return W=B(W),W=N(W),W=b(W),W=K(W),W}function ne(W){return W=L(W),W=v(W),W}function te(){return""}function Z(W,Y){typeof Y!="function"&&(Y=function(){});var A=!Array.isArray(W);function F(z){return A?!0:r.indexOf(W,z)!==-1}var U=[],O=!1;return{onIgnoreTag:function(z,ue,de){if(F(z))if(de.isClosing){var X="[/removed]",se=de.position+X.length;return U.push([O!==!1?O:de.position,se]),O=!1,X}else return O||(O=de.position),"[removed]";else return Y(z,ue,de)},remove:function(z){var ue="",de=0;return r.forEach(U,function(X){ue+=z.slice(de,X[0]),de=X[1]}),ue+=z.slice(de),ue}}}function G(W){for(var Y="",A=0;A<W.length;){var F=W.indexOf("<!--",A);if(F===-1){Y+=W.slice(A);break}Y+=W.slice(A,F);var U=W.indexOf("-->",F);if(U===-1)break;A=U+3}return Y}function ae(W){var Y=W.split("");return Y=Y.filter(function(A){var F=A.charCodeAt(0);return F===127?!1:F<=31?F===10||F===13:!0}),Y.join("")}return je.whiteList=i(),je.getDefaultWhiteList=i,je.onTag=l,je.onIgnoreTag=c,je.onTagAttr=d,je.onIgnoreTagAttr=g,je.safeAttrValue=w,je.escapeHtml=v,je.escapeQuote=L,je.unescapeQuote=B,je.escapeHtmlEntities=N,je.escapeDangerHtml5Entities=b,je.clearNonPrintableCharacter=K,je.friendlyAttrValue=J,je.escapeAttrValue=ne,je.onIgnoreTagStripAll=te,je.StripTagBody=Z,je.stripCommentTag=G,je.stripBlankChar=ae,je.attributeWrapSign='"',je.cssFilter=a,je.getDefaultCSSWhiteList=n,je}var bs={},qg;function Hm(){if(qg)return bs;qg=1;var e=Yc();function n(m){var S=e.spaceIndex(m),y;return S===-1?y=m.slice(1,-1):y=m.slice(1,S+1),y=e.trim(y).toLowerCase(),y.slice(0,1)==="/"&&(y=y.slice(1)),y.slice(-1)==="/"&&(y=y.slice(0,-1)),y}function r(m){return m.slice(0,2)==="</"}function i(m,S,y){var P="",x=0,E=!1,_=!1,T=0,I=m.length,$="",L="";e:for(T=0;T<I;T++){var B=m.charAt(T);if(E===!1){if(B==="<"){E=T;continue}}else if(_===!1){if(B==="<"){P+=y(m.slice(x,T)),E=T,x=T;continue}if(B===">"||T===I-1){P+=y(m.slice(x,E)),L=m.slice(E,T+1),$=n(L),P+=S(E,P.length,$,L,r(L)),x=T+1,E=!1;continue}if(B==='"'||B==="'")for(var N=1,b=m.charAt(T-N);b.trim()===""||b==="=";){if(b==="="){_=B;continue e}b=m.charAt(T-++N)}}else if(B===_){_=!1;continue}}return x<I&&(P+=y(m.substr(x))),P}var a=/[^a-zA-Z0-9\\_:.-]/gim;function l(m,S){var y=0,P=0,x=[],E=!1,_=m.length;function T(N,b){if(N=e.trim(N),N=N.replace(a,"").toLowerCase(),!(N.length<1)){var K=S(N,b||"");K&&x.push(K)}}for(var I=0;I<_;I++){var $=m.charAt(I),L,B;if(E===!1&&$==="="){E=m.slice(y,I),y=I+1,P=m.charAt(y)==='"'||m.charAt(y)==="'"?y:d(m,I+1);continue}if(E!==!1&&I===P){if(B=m.indexOf($,I+1),B===-1)break;L=e.trim(m.slice(P+1,B)),T(E,L),E=!1,I=B,y=I+1;continue}if(/\s|\n|\t/.test($))if(m=m.replace(/\s|\n|\t/g," "),E===!1)if(B=c(m,I),B===-1){L=e.trim(m.slice(y,I)),T(L),E=!1,y=I+1;continue}else{I=B-1;continue}else if(B=g(m,I-1),B===-1){L=e.trim(m.slice(y,I)),L=w(L),T(E,L),E=!1,y=I+1;continue}else continue}return y<m.length&&(E===!1?T(m.slice(y)):T(E,w(e.trim(m.slice(y))))),e.trim(x.join(" "))}function c(m,S){for(;S<m.length;S++){var y=m[S];if(y!==" ")return y==="="?S:-1}}function d(m,S){for(;S<m.length;S++){var y=m[S];if(y!==" ")return y==="'"||y==='"'?S:-1}}function g(m,S){for(;S>0;S--){var y=m[S];if(y!==" ")return y==="="?S:-1}}function v(m){return m[0]==='"'&&m[m.length-1]==='"'||m[0]==="'"&&m[m.length-1]==="'"}function w(m){return v(m)?m.substr(1,m.length-2):m}return bs.parseTag=i,bs.parseAttr=l,bs}var ju,Yg;function lE(){if(Yg)return ju;Yg=1;var e=fc().FilterCSS,n=zm(),r=Hm(),i=r.parseTag,a=r.parseAttr,l=Yc();function c(m){return m==null}function d(m){var S=l.spaceIndex(m);if(S===-1)return{html:"",closing:m[m.length-2]==="/"};m=l.trim(m.slice(S+1,-1));var y=m[m.length-1]==="/";return y&&(m=l.trim(m.slice(0,-1))),{html:m,closing:y}}function g(m){var S={};for(var y in m)S[y]=m[y];return S}function v(m){var S={};for(var y in m)Array.isArray(m[y])?S[y.toLowerCase()]=m[y].map(function(P){return P.toLowerCase()}):S[y.toLowerCase()]=m[y];return S}function w(m){m=g(m||{}),m.stripIgnoreTag&&(m.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),m.onIgnoreTag=n.onIgnoreTagStripAll),m.whiteList||m.allowList?m.whiteList=v(m.whiteList||m.allowList):m.whiteList=n.whiteList,this.attributeWrapSign=m.singleQuotedAttributeValue===!0?"'":n.attributeWrapSign,m.onTag=m.onTag||n.onTag,m.onTagAttr=m.onTagAttr||n.onTagAttr,m.onIgnoreTag=m.onIgnoreTag||n.onIgnoreTag,m.onIgnoreTagAttr=m.onIgnoreTagAttr||n.onIgnoreTagAttr,m.safeAttrValue=m.safeAttrValue||n.safeAttrValue,m.escapeHtml=m.escapeHtml||n.escapeHtml,this.options=m,m.css===!1?this.cssFilter=!1:(m.css=m.css||{},this.cssFilter=new e(m.css))}return w.prototype.process=function(m){if(m=m||"",m=m.toString(),!m)return"";var S=this,y=S.options,P=y.whiteList,x=y.onTag,E=y.onIgnoreTag,_=y.onTagAttr,T=y.onIgnoreTagAttr,I=y.safeAttrValue,$=y.escapeHtml,L=S.attributeWrapSign,B=S.cssFilter;y.stripBlankChar&&(m=n.stripBlankChar(m)),y.allowCommentTag||(m=n.stripCommentTag(m));var N=!1;y.stripIgnoreTagBody&&(N=n.StripTagBody(y.stripIgnoreTagBody,E),E=N.onIgnoreTag);var b=i(m,function(K,J,ne,te,Z){var G={sourcePosition:K,position:J,isClosing:Z,isWhite:Object.prototype.hasOwnProperty.call(P,ne)},ae=x(ne,te,G);if(!c(ae))return ae;if(G.isWhite){if(G.isClosing)return"</"+ne+">";var W=d(te),Y=P[ne],A=a(W.html,function(F,U){var O=l.indexOf(Y,F)!==-1,z=_(ne,F,U,O);return c(z)?O?(U=I(ne,F,U,B),U?F+"="+L+U+L:F):(z=T(ne,F,U,O),c(z)?void 0:z):z});return te="<"+ne,A&&(te+=" "+A),W.closing&&(te+=" /"),te+=">",te}else return ae=E(ne,te,G),c(ae)?$(te):ae},$);return N&&(b=N.remove(b)),b},ju=w,ju}var Qg;function aE(){return Qg||(Qg=1,function(e,n){var r=zm(),i=Hm(),a=lE();function l(d,g){var v=new a(g);return v.process(d)}n=e.exports=l,n.filterXSS=l,n.FilterXSS=a,function(){for(var d in r)n[d]=r[d];for(var g in i)n[g]=i[g]}(),typeof window<"u"&&(window.filterXSS=e.exports);function c(){return typeof self<"u"&&typeof DedicatedWorkerGlobalScope<"u"&&self instanceof DedicatedWorkerGlobalScope}c()&&(self.filterXSS=e.exports)}(Ns,Ns.exports)),Ns.exports}var uE=aE();const cE=lo(uE);function dE(e){const[n,r]=p.useState(e),i=n==="dark"?"light":"dark";return p.useEffect(()=>{const a=window.document.documentElement;a.classList.remove(i),a.classList.add(n)},[n,i]),[i,r]}function Jo(e,n,r){const[i,a]=p.useState(()=>{if(typeof window>"u")return n;try{const c=window.localStorage.getItem(e);return c?r?r(JSON.parse(c)):JSON.parse(c):n}catch(c){return console.log(c),n}});return[i,c=>{try{const d=c instanceof Function?c(i):c;a(d),typeof window<"u"&&window.localStorage.setItem(e,JSON.stringify(d))}catch(d){console.log(d)}}]}/**
   * match-sorter-utils
   *
   * Copyright (c) TanStack
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE.md file in the root directory of this source tree.
   *
   * @license MIT
   */const Um={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ù:"u",ú:"u",û:"u",ü:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z"},fE=Object.keys(Um).join("|"),pE=new RegExp(fE,"g");function gE(e){return e.replace(pE,n=>Um[n])}/**
 * @name match-sorter
 * @license MIT license.
 * @copyright (c) 2099 Kent C. Dodds
 * <AUTHOR> C. Dodds <<EMAIL>> (https://kentcdodds.com)
 */const _t={CASE_SENSITIVE_EQUAL:7,EQUAL:6,STARTS_WITH:5,WORD_STARTS_WITH:4,CONTAINS:3,ACRONYM:2,MATCHES:1,NO_MATCH:0};function hE(e,n,r){var i;if(r=r||{},r.threshold=(i=r.threshold)!=null?i:_t.MATCHES,!r.accessors){const c=Zg(e,n,r);return{rankedValue:e,rank:c,accessorIndex:-1,accessorThreshold:r.threshold,passed:c>=r.threshold}}const a=wE(e,r.accessors),l={rankedValue:e,rank:_t.NO_MATCH,accessorIndex:-1,accessorThreshold:r.threshold,passed:!1};for(let c=0;c<a.length;c++){const d=a[c];let g=Zg(d.itemValue,n,r);const{minRanking:v,maxRanking:w,threshold:m=r.threshold}=d.attributes;g<v&&g>=_t.MATCHES?g=v:g>w&&(g=w),g=Math.min(g,w),g>=m&&g>l.rank&&(l.rank=g,l.passed=!0,l.accessorIndex=c,l.accessorThreshold=m,l.rankedValue=d.itemValue)}return l}function Zg(e,n,r){return e=Jg(e,r),n=Jg(n,r),n.length>e.length?_t.NO_MATCH:e===n?_t.CASE_SENSITIVE_EQUAL:(e=e.toLowerCase(),n=n.toLowerCase(),e===n?_t.EQUAL:e.startsWith(n)?_t.STARTS_WITH:e.includes(` ${n}`)?_t.WORD_STARTS_WITH:e.includes(n)?_t.CONTAINS:n.length===1?_t.NO_MATCH:mE(e).includes(n)?_t.ACRONYM:vE(e,n))}function mE(e){let n="";return e.split(" ").forEach(i=>{i.split("-").forEach(l=>{n+=l.substr(0,1)})}),n}function vE(e,n){let r=0,i=0;function a(g,v,w){for(let m=w,S=v.length;m<S;m++)if(v[m]===g)return r+=1,m+1;return-1}function l(g){const v=1/g,w=r/n.length;return _t.MATCHES+w*v}const c=a(n[0],e,0);if(c<0)return _t.NO_MATCH;i=c;for(let g=1,v=n.length;g<v;g++){const w=n[g];if(i=a(w,e,i),!(i>-1))return _t.NO_MATCH}const d=i-c;return l(d)}function Jg(e,n){let{keepDiacritics:r}=n;return e=`${e}`,r||(e=gE(e)),e}function yE(e,n){let r=n;typeof n=="object"&&(r=n.accessor);const i=r(e);return i==null?[]:Array.isArray(i)?i:[String(i)]}function wE(e,n){const r=[];for(let i=0,a=n.length;i<a;i++){const l=n[i],c=SE(l),d=yE(e,l);for(let g=0,v=d.length;g<v;g++)r.push({itemValue:d[g],attributes:c})}return r}const eh={maxRanking:1/0,minRanking:-1/0};function SE(e){return typeof e=="function"?eh:{...eh,...e}}var Vu={exports:{}},th;function xE(){return th||(th=1,function(e){(function(n){var r=I(),i=$(),a=L(),l=B(),c={imagePlaceholder:void 0,cacheBust:!1},d={toSvg:g,toPng:w,toJpeg:m,toBlob:S,toPixelData:v,impl:{fontFaces:a,images:l,util:r,inliner:i,options:{}}};e.exports=d;function g(N,b){return b=b||{},y(b),Promise.resolve(N).then(function(J){return x(J,b.filter,!0)}).then(E).then(_).then(K).then(function(J){return T(J,b.width||r.width(N),b.height||r.height(N))});function K(J){return b.bgcolor&&(J.style.backgroundColor=b.bgcolor),b.width&&(J.style.width=b.width+"px"),b.height&&(J.style.height=b.height+"px"),b.style&&Object.keys(b.style).forEach(function(ne){J.style[ne]=b.style[ne]}),J}}function v(N,b){return P(N,b||{}).then(function(K){return K.getContext("2d").getImageData(0,0,r.width(N),r.height(N)).data})}function w(N,b){return P(N,b||{}).then(function(K){return K.toDataURL()})}function m(N,b){return b=b||{},P(N,b).then(function(K){return K.toDataURL("image/jpeg",b.quality||1)})}function S(N,b){return P(N,b||{}).then(r.canvasToBlob)}function y(N){typeof N.imagePlaceholder>"u"?d.impl.options.imagePlaceholder=c.imagePlaceholder:d.impl.options.imagePlaceholder=N.imagePlaceholder,typeof N.cacheBust>"u"?d.impl.options.cacheBust=c.cacheBust:d.impl.options.cacheBust=N.cacheBust}function P(N,b){return g(N,b).then(r.makeImage).then(r.delay(100)).then(function(J){var ne=K(N);return ne.getContext("2d").drawImage(J,0,0),ne});function K(J){var ne=document.createElement("canvas");if(ne.width=b.width||r.width(J),ne.height=b.height||r.height(J),b.bgcolor){var te=ne.getContext("2d");te.fillStyle=b.bgcolor,te.fillRect(0,0,ne.width,ne.height)}return ne}}function x(N,b,K){if(!K&&b&&!b(N))return Promise.resolve();return Promise.resolve(N).then(J).then(function(Z){return ne(N,Z,b)}).then(function(Z){return te(N,Z)});function J(Z){return Z instanceof HTMLCanvasElement?r.makeImage(Z.toDataURL()):Z.cloneNode(!1)}function ne(Z,G,ae){var W=Z.childNodes;if(W.length===0)return Promise.resolve(G);return Y(G,r.asArray(W),ae).then(function(){return G});function Y(A,F,U){var O=Promise.resolve();return F.forEach(function(z){O=O.then(function(){return x(z,U)}).then(function(ue){ue&&A.appendChild(ue)})}),O}}function te(Z,G){if(!(G instanceof Element))return G;return Promise.resolve().then(ae).then(W).then(Y).then(A).then(function(){return G});function ae(){F(window.getComputedStyle(Z),G.style);function F(U,O){U.cssText?O.cssText=U.cssText:z(U,O);function z(ue,de){r.asArray(ue).forEach(function(X){de.setProperty(X,ue.getPropertyValue(X),ue.getPropertyPriority(X))})}}}function W(){[":before",":after"].forEach(function(U){F(U)});function F(U){var O=window.getComputedStyle(Z,U),z=O.getPropertyValue("content");if(z===""||z==="none")return;var ue=r.uid();G.className=G.className+" "+ue;var de=document.createElement("style");de.appendChild(X(ue,U,O)),G.appendChild(de);function X(se,Q,oe){var q="."+se+":"+Q,fe=oe.cssText?pe(oe):Pe(oe);return document.createTextNode(q+"{"+fe+"}");function pe(we){var ce=we.getPropertyValue("content");return we.cssText+" content: "+ce+";"}function Pe(we){return r.asArray(we).map(ce).join("; ")+";";function ce(Ne){return Ne+": "+we.getPropertyValue(Ne)+(we.getPropertyPriority(Ne)?" !important":"")}}}}}function Y(){Z instanceof HTMLTextAreaElement&&(G.innerHTML=Z.value),Z instanceof HTMLInputElement&&G.setAttribute("value",Z.value)}function A(){G instanceof SVGElement&&(G.setAttribute("xmlns","http://www.w3.org/2000/svg"),G instanceof SVGRectElement&&["width","height"].forEach(function(F){var U=G.getAttribute(F);U&&G.style.setProperty(F,U)}))}}}function E(N){return a.resolveAll().then(function(b){var K=document.createElement("style");return N.appendChild(K),K.appendChild(document.createTextNode(b)),N})}function _(N){return l.inlineAll(N).then(function(){return N})}function T(N,b,K){return Promise.resolve(N).then(function(J){return J.setAttribute("xmlns","http://www.w3.org/1999/xhtml"),new XMLSerializer().serializeToString(J)}).then(r.escapeXhtml).then(function(J){return'<foreignObject x="0" y="0" width="100%" height="100%">'+J+"</foreignObject>"}).then(function(J){return'<svg xmlns="http://www.w3.org/2000/svg" width="'+b+'" height="'+K+'">'+J+"</svg>"}).then(function(J){return"data:image/svg+xml;charset=utf-8,"+J})}function I(){return{escape:A,parseExtension:b,mimeType:K,dataAsUrl:Y,isDataUrl:J,canvasToBlob:te,resolveUrl:Z,getAndEncode:W,uid:G(),delay:F,asArray:U,escapeXhtml:O,makeImage:ae,width:z,height:ue};function N(){var X="application/font-woff",se="image/jpeg";return{woff:X,woff2:X,ttf:"application/font-truetype",eot:"application/vnd.ms-fontobject",png:"image/png",jpg:se,jpeg:se,gif:"image/gif",tiff:"image/tiff",svg:"image/svg+xml"}}function b(X){var se=/\.([^\.\/]*?)$/g.exec(X);return se?se[1]:""}function K(X){var se=b(X).toLowerCase();return N()[se]||""}function J(X){return X.search(/^(data:)/)!==-1}function ne(X){return new Promise(function(se){for(var Q=window.atob(X.toDataURL().split(",")[1]),oe=Q.length,q=new Uint8Array(oe),fe=0;fe<oe;fe++)q[fe]=Q.charCodeAt(fe);se(new Blob([q],{type:"image/png"}))})}function te(X){return X.toBlob?new Promise(function(se){X.toBlob(se)}):ne(X)}function Z(X,se){var Q=document.implementation.createHTMLDocument(),oe=Q.createElement("base");Q.head.appendChild(oe);var q=Q.createElement("a");return Q.body.appendChild(q),oe.href=se,q.href=X,q.href}function G(){var X=0;return function(){return"u"+se()+X++;function se(){return("0000"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)}}}function ae(X){return new Promise(function(se,Q){var oe=new Image;oe.onload=function(){se(oe)},oe.onerror=Q,oe.src=X})}function W(X){var se=3e4;return d.impl.options.cacheBust&&(X+=(/\?/.test(X)?"&":"?")+new Date().getTime()),new Promise(function(Q){var oe=new XMLHttpRequest;oe.onreadystatechange=pe,oe.ontimeout=Pe,oe.responseType="blob",oe.timeout=se,oe.open("GET",X,!0),oe.send();var q;if(d.impl.options.imagePlaceholder){var fe=d.impl.options.imagePlaceholder.split(/,/);fe&&fe[1]&&(q=fe[1])}function pe(){if(oe.readyState===4){if(oe.status!==200){q?Q(q):we("cannot fetch resource: "+X+", status: "+oe.status);return}var ce=new FileReader;ce.onloadend=function(){var Ne=ce.result.split(/,/)[1];Q(Ne)},ce.readAsDataURL(oe.response)}}function Pe(){q?Q(q):we("timeout of "+se+"ms occured while fetching resource: "+X)}function we(ce){console.error(ce),Q("")}})}function Y(X,se){return"data:"+se+";base64,"+X}function A(X){return X.replace(/([.*+?^${}()|\[\]\/\\])/g,"\\$1")}function F(X){return function(se){return new Promise(function(Q){setTimeout(function(){Q(se)},X)})}}function U(X){for(var se=[],Q=X.length,oe=0;oe<Q;oe++)se.push(X[oe]);return se}function O(X){return X.replace(/#/g,"%23").replace(/\n/g,"%0A")}function z(X){var se=de(X,"border-left-width"),Q=de(X,"border-right-width");return X.scrollWidth+se+Q}function ue(X){var se=de(X,"border-top-width"),Q=de(X,"border-bottom-width");return X.scrollHeight+se+Q}function de(X,se){var Q=window.getComputedStyle(X).getPropertyValue(se);return parseFloat(Q.replace("px",""))}}function $(){var N=/url\(['"]?([^'"]+?)['"]?\)/g;return{inlineAll:ne,shouldProcess:b,impl:{readUrls:K,inline:J}};function b(te){return te.search(N)!==-1}function K(te){for(var Z=[],G;(G=N.exec(te))!==null;)Z.push(G[1]);return Z.filter(function(ae){return!r.isDataUrl(ae)})}function J(te,Z,G,ae){return Promise.resolve(Z).then(function(Y){return G?r.resolveUrl(Y,G):Y}).then(ae||r.getAndEncode).then(function(Y){return r.dataAsUrl(Y,r.mimeType(Z))}).then(function(Y){return te.replace(W(Z),"$1"+Y+"$3")});function W(Y){return new RegExp(`(url\\(['"]?)(`+r.escape(Y)+`)(['"]?\\))`,"g")}}function ne(te,Z,G){if(ae())return Promise.resolve(te);return Promise.resolve(te).then(K).then(function(W){var Y=Promise.resolve(te);return W.forEach(function(A){Y=Y.then(function(F){return J(F,A,Z,G)})}),Y});function ae(){return!b(te)}}}function L(){return{resolveAll:N,impl:{readAll:b}};function N(){return b().then(function(K){return Promise.all(K.map(function(J){return J.resolve()}))}).then(function(K){return K.join(`
`)})}function b(){return Promise.resolve(r.asArray(document.styleSheets)).then(J).then(K).then(function(te){return te.map(ne)});function K(te){return te.filter(function(Z){return Z.type===CSSRule.FONT_FACE_RULE}).filter(function(Z){return i.shouldProcess(Z.style.getPropertyValue("src"))})}function J(te){var Z=[];return te.forEach(function(G){try{r.asArray(G.cssRules||[]).forEach(Z.push.bind(Z))}catch(ae){console.log("Error while reading CSS rules from "+G.href,ae.toString())}}),Z}function ne(te){return{resolve:function(){var G=(te.parentStyleSheet||{}).href;return i.inlineAll(te.cssText,G)},src:function(){return te.style.getPropertyValue("src")}}}}}function B(){return{inlineAll:b,impl:{newImage:N}};function N(K){return{inline:J};function J(ne){return r.isDataUrl(K.src)?Promise.resolve():Promise.resolve(K.src).then(ne||r.getAndEncode).then(function(te){return r.dataAsUrl(te,r.mimeType(K.src))}).then(function(te){return new Promise(function(Z,G){K.onload=Z,K.onerror=G,K.src=te})})}}function b(K){if(!(K instanceof Element))return Promise.resolve(K);return J(K).then(function(){return K instanceof HTMLImageElement?N(K).inline():Promise.all(r.asArray(K.childNodes).map(function(ne){return b(ne)}))});function J(ne){var te=ne.style.getPropertyValue("background");return te?i.inlineAll(te).then(function(Z){ne.style.setProperty("background",Z,ne.style.getPropertyPriority("background"))}).then(function(){return ne}):Promise.resolve(ne)}}}})()}(Vu)),Vu.exports}var CE=xE();const EE=lo(CE);function pc(e){if(typeof e=="string"){const n=e.replace(/[^a-zA-Z]/g,"").trim(),r=["","K","M","B","T"].indexOf(n.replace(/\s/g,""));e=Number(e.replace(/[^0-9.]/g,"").trim())*Math.pow(10,r*3)}return e}function RE(e,n){var r;if(typeof e=="string"&&(e=Number(pc(e))),e%1!==0){const i=Math.max(2,((r=e.toString().split(".")[1])==null?void 0:r.length)||0),a=Math.min(4,i);if(e<5)return e.toFixed(a)||0;e=Number(e.toFixed(2))}if((e>1e5||e<-1e5)&&!Wm(n||"")){const i=Math.min(4,Math.floor(Math.log10(Math.abs(e))/3)),a=["","K","M","B","T"][i];return`${(e/10**(i*3)).toFixed(3).replace(/\.?0+$/,"")} ${a}`}return e>1e3||e<-1e3?Bm(e):e}function Bm(e){if(e>1e3||e<-1e3){const n=e.toString().split("."),r=n[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),i=n[1]?`.${n[1]}`:"";return`${r}${i}`}return e}function Ks(e){return["date","day","time","timestamp","year"].some(n=>e==null?void 0:e.toLowerCase().includes(n))}function Wm(e){return["price","open","close"].some(n=>e==null?void 0:e.toLowerCase().includes(n))}function Xs(e,n){const r=window.document.getElementById("loading"),i=window.document.getElementById("loading_text");return new Promise(a=>{n?r.classList.remove("show"):(i.innerHTML=e,r.classList.add("show"));const l=setInterval(function(){(n?!r.classList.contains("show"):r.classList.contains("show"))&&(clearInterval(l),a(!0))},.01)})}function PE(e,n){if(e===n)return!0;if(e==null||n==null||(e==null?void 0:e.length)!==(n==null?void 0:n.length))return!1;for(let r=0;r<(e==null?void 0:e.length);++r)if(e[r]!==n[r])return!1;return!0}const _E=(e,n,r,i)=>{const a=hE(e.getValue(n),r);return i(a),a},TE=async({fileHandle:e,blob:n})=>{e&&await IE({fileHandle:e,blob:n})},IE=async({fileHandle:e,blob:n})=>{const r=await e.createWritable();await r.write(n),await r.close()},DE=[{description:"PNG Image",accept:{"image/png":[".png"]}},{description:"JPEG Image",accept:{"image/jpeg":[".jpeg"]}}],Gm=({filename:e,is_image:n})=>{try{if("showSaveFilePicker"in window){const r={suggestedName:e,types:n?DE:[{description:"CSV File",accept:{"image/csv":[".csv"]}}],excludeAcceptAllOption:!0};return showSaveFilePicker(r)}}catch(r){console.error(r)}return new Promise(r=>{r(null)})},Km=(e,n,r)=>{try{if(r===null)throw new Error("Cannot access filesystem");TE({fileHandle:r,blob:e})}catch(i){console.error("oops, something went wrong!",i);const a=URL.createObjectURL(e),l=document.createElement("a");l.setAttribute("href",a),l.setAttribute("download",n),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l)}return new Promise(i=>{i(!0)})};async function OE(e,n,r,i){const a=n,l=r.map(d=>a.map(g=>d[g])),c=[a,...l];{const d=c.map(w=>w.join(",")).join(`
`),g=new Blob([d],{type:"text/csv;charset=utf-8;"}),v=`${window.title}.csv`;try{const w=await Gm({filename:v});let m="csv";w!==null&&(m=w.name.split(".").pop()),await Xs(`Saving ${m.toUpperCase()}`),Xm(async function(){Km(g,v,w).then(async function(){await new Promise(S=>setTimeout(S,1500)),await Xs("",!0),w||i(!0)})},2)()}catch(w){console.error(w)}return}}async function ME(e,n){const r=document.getElementById(e),i=`${window.title}.png`;try{const a=await Gm({filename:i,is_image:!0});let l="png";a!==null&&(l=a.name.split(".").pop()),await Xs(`Saving ${l.toUpperCase()}`),Xm(async function(){EE.toBlob(r).then(function(c){Km(c,i,a).then(async function(){await new Promise(d=>setTimeout(d,1500)),await Xs("",!0),a||n(!0)})})},2)()}catch(a){console.error(a)}}const Xm=(e,n)=>{let r;return function(){const i=this,a=arguments;clearTimeout(r),r=setTimeout(()=>e.apply(i,a),n)}},qs=({title:e,titleId:n,...r})=>R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:1.5,...r,children:R.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18L18 6M6 6l12 12"})});function Oe(){return Oe=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var i in r)({}).hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e},Oe.apply(null,arguments)}function nh(e,[n,r]){return Math.min(r,Math.max(n,e))}function tt(e,n,{checkForDefaultPrevented:r=!0}={}){return function(a){if(e==null||e(a),r===!1||!a.defaultPrevented)return n==null?void 0:n(a)}}function Qc(e,n=[]){let r=[];function i(l,c){const d=p.createContext(c),g=r.length;r=[...r,c];function v(m){const{scope:S,children:y,...P}=m,x=(S==null?void 0:S[e][g])||d,E=p.useMemo(()=>P,Object.values(P));return p.createElement(x.Provider,{value:E},y)}function w(m,S){const y=(S==null?void 0:S[e][g])||d,P=p.useContext(y);if(P)return P;if(c!==void 0)return c;throw new Error(`\`${m}\` must be used within \`${l}\``)}return v.displayName=l+"Provider",[v,w]}const a=()=>{const l=r.map(c=>p.createContext(c));return function(d){const g=(d==null?void 0:d[e])||l;return p.useMemo(()=>({[`__scope${e}`]:{...d,[e]:g}}),[d,g])}};return a.scopeName=e,[i,kE(a,...n)]}function kE(...e){const n=e[0];if(e.length===1)return n;const r=()=>{const i=e.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(l){const c=i.reduce((d,{useScope:g,scopeName:v})=>{const m=g(l)[`__scope${v}`];return{...d,...m}},{});return p.useMemo(()=>({[`__scope${n.scopeName}`]:c}),[c])}};return r.scopeName=n.scopeName,r}function $E(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function qm(...e){return n=>e.forEach(r=>$E(r,n))}function pt(...e){return p.useCallback(qm(...e),e)}const si=p.forwardRef((e,n)=>{const{children:r,...i}=e,a=p.Children.toArray(r),l=a.find(AE);if(l){const c=l.props.children,d=a.map(g=>g===l?p.Children.count(c)>1?p.Children.only(null):p.isValidElement(c)?c.props.children:null:g);return p.createElement(gc,Oe({},i,{ref:n}),p.isValidElement(c)?p.cloneElement(c,void 0,d):null)}return p.createElement(gc,Oe({},i,{ref:n}),r)});si.displayName="Slot";const gc=p.forwardRef((e,n)=>{const{children:r,...i}=e;return p.isValidElement(r)?p.cloneElement(r,{...bE(i,r.props),ref:n?qm(n,r.ref):r.ref}):p.Children.count(r)>1?p.Children.only(null):null});gc.displayName="SlotClone";const NE=({children:e})=>p.createElement(p.Fragment,null,e);function AE(e){return p.isValidElement(e)&&e.type===NE}function bE(e,n){const r={...n};for(const i in n){const a=e[i],l=n[i];/^on[A-Z]/.test(i)?a&&l?r[i]=(...d)=>{l(...d),a(...d)}:a&&(r[i]=a):i==="style"?r[i]={...a,...l}:i==="className"&&(r[i]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}function LE(e){const n=e+"CollectionProvider",[r,i]=Qc(n),[a,l]=r(n,{collectionRef:{current:null},itemMap:new Map}),c=y=>{const{scope:P,children:x}=y,E=qe.useRef(null),_=qe.useRef(new Map).current;return qe.createElement(a,{scope:P,itemMap:_,collectionRef:E},x)},d=e+"CollectionSlot",g=qe.forwardRef((y,P)=>{const{scope:x,children:E}=y,_=l(d,x),T=pt(P,_.collectionRef);return qe.createElement(si,{ref:T},E)}),v=e+"CollectionItemSlot",w="data-radix-collection-item",m=qe.forwardRef((y,P)=>{const{scope:x,children:E,..._}=y,T=qe.useRef(null),I=pt(P,T),$=l(v,x);return qe.useEffect(()=>($.itemMap.set(T,{ref:T,..._}),()=>void $.itemMap.delete(T))),qe.createElement(si,{[w]:"",ref:I},E)});function S(y){const P=l(e+"CollectionConsumer",y);return qe.useCallback(()=>{const E=P.collectionRef.current;if(!E)return[];const _=Array.from(E.querySelectorAll(`[${w}]`));return Array.from(P.itemMap.values()).sort(($,L)=>_.indexOf($.ref.current)-_.indexOf(L.ref.current))},[P.collectionRef,P.itemMap])}return[{Provider:c,Slot:g,ItemSlot:m},S,i]}const FE=p.createContext(void 0);function jE(e){const n=p.useContext(FE);return e||n||"ltr"}const VE=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],gt=VE.reduce((e,n)=>{const r=p.forwardRef((i,a)=>{const{asChild:l,...c}=i,d=l?si:n;return p.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),p.createElement(d,Oe({},c,{ref:a}))});return r.displayName=`Primitive.${n}`,{...e,[n]:r}},{});function zE(e,n){e&&wr.flushSync(()=>e.dispatchEvent(n))}function _n(e){const n=p.useRef(e);return p.useEffect(()=>{n.current=e}),p.useMemo(()=>(...r)=>{var i;return(i=n.current)===null||i===void 0?void 0:i.call(n,...r)},[])}function HE(e,n=globalThis==null?void 0:globalThis.document){const r=_n(e);p.useEffect(()=>{const i=a=>{a.key==="Escape"&&r(a)};return n.addEventListener("keydown",i),()=>n.removeEventListener("keydown",i)},[r,n])}const hc="dismissableLayer.update",UE="dismissableLayer.pointerDownOutside",BE="dismissableLayer.focusOutside";let rh;const WE=p.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),GE=p.forwardRef((e,n)=>{var r;const{disableOutsidePointerEvents:i=!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:c,onInteractOutside:d,onDismiss:g,...v}=e,w=p.useContext(WE),[m,S]=p.useState(null),y=(r=m==null?void 0:m.ownerDocument)!==null&&r!==void 0?r:globalThis==null?void 0:globalThis.document,[,P]=p.useState({}),x=pt(n,b=>S(b)),E=Array.from(w.layers),[_]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),T=E.indexOf(_),I=m?E.indexOf(m):-1,$=w.layersWithOutsidePointerEventsDisabled.size>0,L=I>=T,B=KE(b=>{const K=b.target,J=[...w.branches].some(ne=>ne.contains(K));!L||J||(l==null||l(b),d==null||d(b),b.defaultPrevented||g==null||g())},y),N=XE(b=>{const K=b.target;[...w.branches].some(ne=>ne.contains(K))||(c==null||c(b),d==null||d(b),b.defaultPrevented||g==null||g())},y);return HE(b=>{I===w.layers.size-1&&(a==null||a(b),!b.defaultPrevented&&g&&(b.preventDefault(),g()))},y),p.useEffect(()=>{if(m)return i&&(w.layersWithOutsidePointerEventsDisabled.size===0&&(rh=y.body.style.pointerEvents,y.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(m)),w.layers.add(m),oh(),()=>{i&&w.layersWithOutsidePointerEventsDisabled.size===1&&(y.body.style.pointerEvents=rh)}},[m,y,i,w]),p.useEffect(()=>()=>{m&&(w.layers.delete(m),w.layersWithOutsidePointerEventsDisabled.delete(m),oh())},[m,w]),p.useEffect(()=>{const b=()=>P({});return document.addEventListener(hc,b),()=>document.removeEventListener(hc,b)},[]),p.createElement(gt.div,Oe({},v,{ref:x,style:{pointerEvents:$?L?"auto":"none":void 0,...e.style},onFocusCapture:tt(e.onFocusCapture,N.onFocusCapture),onBlurCapture:tt(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:tt(e.onPointerDownCapture,B.onPointerDownCapture)}))});function KE(e,n=globalThis==null?void 0:globalThis.document){const r=_n(e),i=p.useRef(!1),a=p.useRef(()=>{});return p.useEffect(()=>{const l=d=>{if(d.target&&!i.current){let w=function(){Ym(UE,r,v,{discrete:!0})};var g=w;const v={originalEvent:d};d.pointerType==="touch"?(n.removeEventListener("click",a.current),a.current=w,n.addEventListener("click",a.current,{once:!0})):w()}i.current=!1},c=window.setTimeout(()=>{n.addEventListener("pointerdown",l)},0);return()=>{window.clearTimeout(c),n.removeEventListener("pointerdown",l),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}function XE(e,n=globalThis==null?void 0:globalThis.document){const r=_n(e),i=p.useRef(!1);return p.useEffect(()=>{const a=l=>{l.target&&!i.current&&Ym(BE,r,{originalEvent:l},{discrete:!1})};return n.addEventListener("focusin",a),()=>n.removeEventListener("focusin",a)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}function oh(){const e=new CustomEvent(hc);document.dispatchEvent(e)}function Ym(e,n,r,{discrete:i}){const a=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});n&&a.addEventListener(e,n,{once:!0}),i?zE(a,l):a.dispatchEvent(l)}let zu=0;function qE(){p.useEffect(()=>{var e,n;const r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(e=r[0])!==null&&e!==void 0?e:ih()),document.body.insertAdjacentElement("beforeend",(n=r[1])!==null&&n!==void 0?n:ih()),zu++,()=>{zu===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(i=>i.remove()),zu--}},[])}function ih(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}const Hu="focusScope.autoFocusOnMount",Uu="focusScope.autoFocusOnUnmount",sh={bubbles:!1,cancelable:!0},YE=p.forwardRef((e,n)=>{const{loop:r=!1,trapped:i=!1,onMountAutoFocus:a,onUnmountAutoFocus:l,...c}=e,[d,g]=p.useState(null),v=_n(a),w=_n(l),m=p.useRef(null),S=pt(n,x=>g(x)),y=p.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;p.useEffect(()=>{if(i){let T=function(B){if(y.paused||!d)return;const N=B.target;d.contains(N)?m.current=N:Yn(m.current,{select:!0})},I=function(B){if(y.paused||!d)return;const N=B.relatedTarget;N!==null&&(d.contains(N)||Yn(m.current,{select:!0}))},$=function(B){const N=document.activeElement;for(const b of B)b.removedNodes.length>0&&(d!=null&&d.contains(N)||Yn(d))};var x=T,E=I,_=$;document.addEventListener("focusin",T),document.addEventListener("focusout",I);const L=new MutationObserver($);return d&&L.observe(d,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",T),document.removeEventListener("focusout",I),L.disconnect()}}},[i,d,y.paused]),p.useEffect(()=>{if(d){ah.add(y);const x=document.activeElement;if(!d.contains(x)){const _=new CustomEvent(Hu,sh);d.addEventListener(Hu,v),d.dispatchEvent(_),_.defaultPrevented||(QE(n1(Qm(d)),{select:!0}),document.activeElement===x&&Yn(d))}return()=>{d.removeEventListener(Hu,v),setTimeout(()=>{const _=new CustomEvent(Uu,sh);d.addEventListener(Uu,w),d.dispatchEvent(_),_.defaultPrevented||Yn(x??document.body,{select:!0}),d.removeEventListener(Uu,w),ah.remove(y)},0)}}},[d,v,w,y]);const P=p.useCallback(x=>{if(!r&&!i||y.paused)return;const E=x.key==="Tab"&&!x.altKey&&!x.ctrlKey&&!x.metaKey,_=document.activeElement;if(E&&_){const T=x.currentTarget,[I,$]=ZE(T);I&&$?!x.shiftKey&&_===$?(x.preventDefault(),r&&Yn(I,{select:!0})):x.shiftKey&&_===I&&(x.preventDefault(),r&&Yn($,{select:!0})):_===T&&x.preventDefault()}},[r,i,y.paused]);return p.createElement(gt.div,Oe({tabIndex:-1},c,{ref:S,onKeyDown:P}))});function QE(e,{select:n=!1}={}){const r=document.activeElement;for(const i of e)if(Yn(i,{select:n}),document.activeElement!==r)return}function ZE(e){const n=Qm(e),r=lh(n,e),i=lh(n.reverse(),e);return[r,i]}function Qm(e){const n=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const a=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||a?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)n.push(r.currentNode);return n}function lh(e,n){for(const r of e)if(!JE(r,{upTo:n}))return r}function JE(e,{upTo:n}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(n!==void 0&&e===n)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function e1(e){return e instanceof HTMLInputElement&&"select"in e}function Yn(e,{select:n=!1}={}){if(e&&e.focus){const r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&e1(e)&&n&&e.select()}}const ah=t1();function t1(){let e=[];return{add(n){const r=e[0];n!==r&&(r==null||r.pause()),e=uh(e,n),e.unshift(n)},remove(n){var r;e=uh(e,n),(r=e[0])===null||r===void 0||r.resume()}}}function uh(e,n){const r=[...e],i=r.indexOf(n);return i!==-1&&r.splice(i,1),r}function n1(e){return e.filter(n=>n.tagName!=="A")}const Wt=globalThis!=null&&globalThis.document?p.useLayoutEffect:()=>{},r1=bc.useId||(()=>{});let o1=0;function Zc(e){const[n,r]=p.useState(r1());return Wt(()=>{r(i=>i??String(o1++))},[e]),n?`radix-${n}`:""}const i1=["top","right","bottom","left"],Jn=Math.min,$t=Math.max,Ys=Math.round,Ls=Math.floor,cn=e=>({x:e,y:e}),s1={left:"right",right:"left",bottom:"top",top:"bottom"},l1={start:"end",end:"start"};function mc(e,n,r){return $t(e,Jn(n,r))}function Tn(e,n){return typeof e=="function"?e(n):e}function In(e){return e.split("-")[0]}function ao(e){return e.split("-")[1]}function Jc(e){return e==="x"?"y":"x"}function ed(e){return e==="y"?"height":"width"}function Rn(e){return["top","bottom"].includes(In(e))?"y":"x"}function td(e){return Jc(Rn(e))}function a1(e,n,r){r===void 0&&(r=!1);const i=ao(e),a=td(e),l=ed(a);let c=a==="x"?i===(r?"end":"start")?"right":"left":i==="start"?"bottom":"top";return n.reference[l]>n.floating[l]&&(c=Qs(c)),[c,Qs(c)]}function u1(e){const n=Qs(e);return[vc(e),n,vc(n)]}function vc(e){return e.replace(/start|end/g,n=>l1[n])}function c1(e,n,r){const i=["left","right"],a=["right","left"],l=["top","bottom"],c=["bottom","top"];switch(e){case"top":case"bottom":return r?n?a:i:n?i:a;case"left":case"right":return n?l:c;default:return[]}}function d1(e,n,r,i){const a=ao(e);let l=c1(In(e),r==="start",i);return a&&(l=l.map(c=>c+"-"+a),n&&(l=l.concat(l.map(vc)))),l}function Qs(e){return e.replace(/left|right|bottom|top/g,n=>s1[n])}function f1(e){return{top:0,right:0,bottom:0,left:0,...e}}function Zm(e){return typeof e!="number"?f1(e):{top:e,right:e,bottom:e,left:e}}function Zs(e){const{x:n,y:r,width:i,height:a}=e;return{width:i,height:a,top:r,left:n,right:n+i,bottom:r+a,x:n,y:r}}function ch(e,n,r){let{reference:i,floating:a}=e;const l=Rn(n),c=td(n),d=ed(c),g=In(n),v=l==="y",w=i.x+i.width/2-a.width/2,m=i.y+i.height/2-a.height/2,S=i[d]/2-a[d]/2;let y;switch(g){case"top":y={x:w,y:i.y-a.height};break;case"bottom":y={x:w,y:i.y+i.height};break;case"right":y={x:i.x+i.width,y:m};break;case"left":y={x:i.x-a.width,y:m};break;default:y={x:i.x,y:i.y}}switch(ao(n)){case"start":y[c]-=S*(r&&v?-1:1);break;case"end":y[c]+=S*(r&&v?-1:1);break}return y}const p1=async(e,n,r)=>{const{placement:i="bottom",strategy:a="absolute",middleware:l=[],platform:c}=r,d=l.filter(Boolean),g=await(c.isRTL==null?void 0:c.isRTL(n));let v=await c.getElementRects({reference:e,floating:n,strategy:a}),{x:w,y:m}=ch(v,i,g),S=i,y={},P=0;for(let x=0;x<d.length;x++){const{name:E,fn:_}=d[x],{x:T,y:I,data:$,reset:L}=await _({x:w,y:m,initialPlacement:i,placement:S,strategy:a,middlewareData:y,rects:v,platform:c,elements:{reference:e,floating:n}});w=T??w,m=I??m,y={...y,[E]:{...y[E],...$}},L&&P<=50&&(P++,typeof L=="object"&&(L.placement&&(S=L.placement),L.rects&&(v=L.rects===!0?await c.getElementRects({reference:e,floating:n,strategy:a}):L.rects),{x:w,y:m}=ch(v,S,g)),x=-1)}return{x:w,y:m,placement:S,strategy:a,middlewareData:y}};async function li(e,n){var r;n===void 0&&(n={});const{x:i,y:a,platform:l,rects:c,elements:d,strategy:g}=e,{boundary:v="clippingAncestors",rootBoundary:w="viewport",elementContext:m="floating",altBoundary:S=!1,padding:y=0}=Tn(n,e),P=Zm(y),E=d[S?m==="floating"?"reference":"floating":m],_=Zs(await l.getClippingRect({element:(r=await(l.isElement==null?void 0:l.isElement(E)))==null||r?E:E.contextElement||await(l.getDocumentElement==null?void 0:l.getDocumentElement(d.floating)),boundary:v,rootBoundary:w,strategy:g})),T=m==="floating"?{x:i,y:a,width:c.floating.width,height:c.floating.height}:c.reference,I=await(l.getOffsetParent==null?void 0:l.getOffsetParent(d.floating)),$=await(l.isElement==null?void 0:l.isElement(I))?await(l.getScale==null?void 0:l.getScale(I))||{x:1,y:1}:{x:1,y:1},L=Zs(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:d,rect:T,offsetParent:I,strategy:g}):T);return{top:(_.top-L.top+P.top)/$.y,bottom:(L.bottom-_.bottom+P.bottom)/$.y,left:(_.left-L.left+P.left)/$.x,right:(L.right-_.right+P.right)/$.x}}const g1=e=>({name:"arrow",options:e,async fn(n){const{x:r,y:i,placement:a,rects:l,platform:c,elements:d,middlewareData:g}=n,{element:v,padding:w=0}=Tn(e,n)||{};if(v==null)return{};const m=Zm(w),S={x:r,y:i},y=td(a),P=ed(y),x=await c.getDimensions(v),E=y==="y",_=E?"top":"left",T=E?"bottom":"right",I=E?"clientHeight":"clientWidth",$=l.reference[P]+l.reference[y]-S[y]-l.floating[P],L=S[y]-l.reference[y],B=await(c.getOffsetParent==null?void 0:c.getOffsetParent(v));let N=B?B[I]:0;(!N||!await(c.isElement==null?void 0:c.isElement(B)))&&(N=d.floating[I]||l.floating[P]);const b=$/2-L/2,K=N/2-x[P]/2-1,J=Jn(m[_],K),ne=Jn(m[T],K),te=J,Z=N-x[P]-ne,G=N/2-x[P]/2+b,ae=mc(te,G,Z),W=!g.arrow&&ao(a)!=null&&G!==ae&&l.reference[P]/2-(G<te?J:ne)-x[P]/2<0,Y=W?G<te?G-te:G-Z:0;return{[y]:S[y]+Y,data:{[y]:ae,centerOffset:G-ae-Y,...W&&{alignmentOffset:Y}},reset:W}}}),h1=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(n){var r,i;const{placement:a,middlewareData:l,rects:c,initialPlacement:d,platform:g,elements:v}=n,{mainAxis:w=!0,crossAxis:m=!0,fallbackPlacements:S,fallbackStrategy:y="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:x=!0,...E}=Tn(e,n);if((r=l.arrow)!=null&&r.alignmentOffset)return{};const _=In(a),T=Rn(d),I=In(d)===d,$=await(g.isRTL==null?void 0:g.isRTL(v.floating)),L=S||(I||!x?[Qs(d)]:u1(d)),B=P!=="none";!S&&B&&L.push(...d1(d,x,P,$));const N=[d,...L],b=await li(n,E),K=[];let J=((i=l.flip)==null?void 0:i.overflows)||[];if(w&&K.push(b[_]),m){const ae=a1(a,c,$);K.push(b[ae[0]],b[ae[1]])}if(J=[...J,{placement:a,overflows:K}],!K.every(ae=>ae<=0)){var ne,te;const ae=(((ne=l.flip)==null?void 0:ne.index)||0)+1,W=N[ae];if(W){var Z;const A=m==="alignment"?T!==Rn(W):!1,F=((Z=J[0])==null?void 0:Z.overflows[0])>0;if(!A||F)return{data:{index:ae,overflows:J},reset:{placement:W}}}let Y=(te=J.filter(A=>A.overflows[0]<=0).sort((A,F)=>A.overflows[1]-F.overflows[1])[0])==null?void 0:te.placement;if(!Y)switch(y){case"bestFit":{var G;const A=(G=J.filter(F=>{if(B){const U=Rn(F.placement);return U===T||U==="y"}return!0}).map(F=>[F.placement,F.overflows.filter(U=>U>0).reduce((U,O)=>U+O,0)]).sort((F,U)=>F[1]-U[1])[0])==null?void 0:G[0];A&&(Y=A);break}case"initialPlacement":Y=d;break}if(a!==Y)return{reset:{placement:Y}}}return{}}}};function dh(e,n){return{top:e.top-n.height,right:e.right-n.width,bottom:e.bottom-n.height,left:e.left-n.width}}function fh(e){return i1.some(n=>e[n]>=0)}const m1=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(n){const{rects:r}=n,{strategy:i="referenceHidden",...a}=Tn(e,n);switch(i){case"referenceHidden":{const l=await li(n,{...a,elementContext:"reference"}),c=dh(l,r.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:fh(c)}}}case"escaped":{const l=await li(n,{...a,altBoundary:!0}),c=dh(l,r.floating);return{data:{escapedOffsets:c,escaped:fh(c)}}}default:return{}}}}};async function v1(e,n){const{placement:r,platform:i,elements:a}=e,l=await(i.isRTL==null?void 0:i.isRTL(a.floating)),c=In(r),d=ao(r),g=Rn(r)==="y",v=["left","top"].includes(c)?-1:1,w=l&&g?-1:1,m=Tn(n,e);let{mainAxis:S,crossAxis:y,alignmentAxis:P}=typeof m=="number"?{mainAxis:m,crossAxis:0,alignmentAxis:null}:{mainAxis:m.mainAxis||0,crossAxis:m.crossAxis||0,alignmentAxis:m.alignmentAxis};return d&&typeof P=="number"&&(y=d==="end"?P*-1:P),g?{x:y*w,y:S*v}:{x:S*v,y:y*w}}const y1=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(n){var r,i;const{x:a,y:l,placement:c,middlewareData:d}=n,g=await v1(n,e);return c===((r=d.offset)==null?void 0:r.placement)&&(i=d.arrow)!=null&&i.alignmentOffset?{}:{x:a+g.x,y:l+g.y,data:{...g,placement:c}}}}},w1=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(n){const{x:r,y:i,placement:a}=n,{mainAxis:l=!0,crossAxis:c=!1,limiter:d={fn:E=>{let{x:_,y:T}=E;return{x:_,y:T}}},...g}=Tn(e,n),v={x:r,y:i},w=await li(n,g),m=Rn(In(a)),S=Jc(m);let y=v[S],P=v[m];if(l){const E=S==="y"?"top":"left",_=S==="y"?"bottom":"right",T=y+w[E],I=y-w[_];y=mc(T,y,I)}if(c){const E=m==="y"?"top":"left",_=m==="y"?"bottom":"right",T=P+w[E],I=P-w[_];P=mc(T,P,I)}const x=d.fn({...n,[S]:y,[m]:P});return{...x,data:{x:x.x-r,y:x.y-i,enabled:{[S]:l,[m]:c}}}}}},S1=function(e){return e===void 0&&(e={}),{options:e,fn(n){const{x:r,y:i,placement:a,rects:l,middlewareData:c}=n,{offset:d=0,mainAxis:g=!0,crossAxis:v=!0}=Tn(e,n),w={x:r,y:i},m=Rn(a),S=Jc(m);let y=w[S],P=w[m];const x=Tn(d,n),E=typeof x=="number"?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(g){const I=S==="y"?"height":"width",$=l.reference[S]-l.floating[I]+E.mainAxis,L=l.reference[S]+l.reference[I]-E.mainAxis;y<$?y=$:y>L&&(y=L)}if(v){var _,T;const I=S==="y"?"width":"height",$=["top","left"].includes(In(a)),L=l.reference[m]-l.floating[I]+($&&((_=c.offset)==null?void 0:_[m])||0)+($?0:E.crossAxis),B=l.reference[m]+l.reference[I]+($?0:((T=c.offset)==null?void 0:T[m])||0)-($?E.crossAxis:0);P<L?P=L:P>B&&(P=B)}return{[S]:y,[m]:P}}}},x1=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(n){var r,i;const{placement:a,rects:l,platform:c,elements:d}=n,{apply:g=()=>{},...v}=Tn(e,n),w=await li(n,v),m=In(a),S=ao(a),y=Rn(a)==="y",{width:P,height:x}=l.floating;let E,_;m==="top"||m==="bottom"?(E=m,_=S===(await(c.isRTL==null?void 0:c.isRTL(d.floating))?"start":"end")?"left":"right"):(_=m,E=S==="end"?"top":"bottom");const T=x-w.top-w.bottom,I=P-w.left-w.right,$=Jn(x-w[E],T),L=Jn(P-w[_],I),B=!n.middlewareData.shift;let N=$,b=L;if((r=n.middlewareData.shift)!=null&&r.enabled.x&&(b=I),(i=n.middlewareData.shift)!=null&&i.enabled.y&&(N=T),B&&!S){const J=$t(w.left,0),ne=$t(w.right,0),te=$t(w.top,0),Z=$t(w.bottom,0);y?b=P-2*(J!==0||ne!==0?J+ne:$t(w.left,w.right)):N=x-2*(te!==0||Z!==0?te+Z:$t(w.top,w.bottom))}await g({...n,availableWidth:b,availableHeight:N});const K=await c.getDimensions(d.floating);return P!==K.width||x!==K.height?{reset:{rects:!0}}:{}}}};function cl(){return typeof window<"u"}function uo(e){return Jm(e)?(e.nodeName||"").toLowerCase():"#document"}function Nt(e){var n;return(e==null||(n=e.ownerDocument)==null?void 0:n.defaultView)||window}function fn(e){var n;return(n=(Jm(e)?e.ownerDocument:e.document)||window.document)==null?void 0:n.documentElement}function Jm(e){return cl()?e instanceof Node||e instanceof Nt(e).Node:!1}function en(e){return cl()?e instanceof Element||e instanceof Nt(e).Element:!1}function dn(e){return cl()?e instanceof HTMLElement||e instanceof Nt(e).HTMLElement:!1}function ph(e){return!cl()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Nt(e).ShadowRoot}function di(e){const{overflow:n,overflowX:r,overflowY:i,display:a}=tn(e);return/auto|scroll|overlay|hidden|clip/.test(n+i+r)&&!["inline","contents"].includes(a)}function C1(e){return["table","td","th"].includes(uo(e))}function dl(e){return[":popover-open",":modal"].some(n=>{try{return e.matches(n)}catch{return!1}})}function nd(e){const n=rd(),r=en(e)?tn(e):e;return["transform","translate","scale","rotate","perspective"].some(i=>r[i]?r[i]!=="none":!1)||(r.containerType?r.containerType!=="normal":!1)||!n&&(r.backdropFilter?r.backdropFilter!=="none":!1)||!n&&(r.filter?r.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(i=>(r.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(r.contain||"").includes(i))}function E1(e){let n=er(e);for(;dn(n)&&!io(n);){if(nd(n))return n;if(dl(n))return null;n=er(n)}return null}function rd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function io(e){return["html","body","#document"].includes(uo(e))}function tn(e){return Nt(e).getComputedStyle(e)}function fl(e){return en(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function er(e){if(uo(e)==="html")return e;const n=e.assignedSlot||e.parentNode||ph(e)&&e.host||fn(e);return ph(n)?n.host:n}function ev(e){const n=er(e);return io(n)?e.ownerDocument?e.ownerDocument.body:e.body:dn(n)&&di(n)?n:ev(n)}function ai(e,n,r){var i;n===void 0&&(n=[]),r===void 0&&(r=!0);const a=ev(e),l=a===((i=e.ownerDocument)==null?void 0:i.body),c=Nt(a);if(l){const d=yc(c);return n.concat(c,c.visualViewport||[],di(a)?a:[],d&&r?ai(d):[])}return n.concat(a,ai(a,[],r))}function yc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function tv(e){const n=tn(e);let r=parseFloat(n.width)||0,i=parseFloat(n.height)||0;const a=dn(e),l=a?e.offsetWidth:r,c=a?e.offsetHeight:i,d=Ys(r)!==l||Ys(i)!==c;return d&&(r=l,i=c),{width:r,height:i,$:d}}function od(e){return en(e)?e:e.contextElement}function oo(e){const n=od(e);if(!dn(n))return cn(1);const r=n.getBoundingClientRect(),{width:i,height:a,$:l}=tv(n);let c=(l?Ys(r.width):r.width)/i,d=(l?Ys(r.height):r.height)/a;return(!c||!Number.isFinite(c))&&(c=1),(!d||!Number.isFinite(d))&&(d=1),{x:c,y:d}}const R1=cn(0);function nv(e){const n=Nt(e);return!rd()||!n.visualViewport?R1:{x:n.visualViewport.offsetLeft,y:n.visualViewport.offsetTop}}function P1(e,n,r){return n===void 0&&(n=!1),!r||n&&r!==Nt(e)?!1:n}function vr(e,n,r,i){n===void 0&&(n=!1),r===void 0&&(r=!1);const a=e.getBoundingClientRect(),l=od(e);let c=cn(1);n&&(i?en(i)&&(c=oo(i)):c=oo(e));const d=P1(l,r,i)?nv(l):cn(0);let g=(a.left+d.x)/c.x,v=(a.top+d.y)/c.y,w=a.width/c.x,m=a.height/c.y;if(l){const S=Nt(l),y=i&&en(i)?Nt(i):i;let P=S,x=yc(P);for(;x&&i&&y!==P;){const E=oo(x),_=x.getBoundingClientRect(),T=tn(x),I=_.left+(x.clientLeft+parseFloat(T.paddingLeft))*E.x,$=_.top+(x.clientTop+parseFloat(T.paddingTop))*E.y;g*=E.x,v*=E.y,w*=E.x,m*=E.y,g+=I,v+=$,P=Nt(x),x=yc(P)}}return Zs({width:w,height:m,x:g,y:v})}function id(e,n){const r=fl(e).scrollLeft;return n?n.left+r:vr(fn(e)).left+r}function rv(e,n,r){r===void 0&&(r=!1);const i=e.getBoundingClientRect(),a=i.left+n.scrollLeft-(r?0:id(e,i)),l=i.top+n.scrollTop;return{x:a,y:l}}function _1(e){let{elements:n,rect:r,offsetParent:i,strategy:a}=e;const l=a==="fixed",c=fn(i),d=n?dl(n.floating):!1;if(i===c||d&&l)return r;let g={scrollLeft:0,scrollTop:0},v=cn(1);const w=cn(0),m=dn(i);if((m||!m&&!l)&&((uo(i)!=="body"||di(c))&&(g=fl(i)),dn(i))){const y=vr(i);v=oo(i),w.x=y.x+i.clientLeft,w.y=y.y+i.clientTop}const S=c&&!m&&!l?rv(c,g,!0):cn(0);return{width:r.width*v.x,height:r.height*v.y,x:r.x*v.x-g.scrollLeft*v.x+w.x+S.x,y:r.y*v.y-g.scrollTop*v.y+w.y+S.y}}function T1(e){return Array.from(e.getClientRects())}function I1(e){const n=fn(e),r=fl(e),i=e.ownerDocument.body,a=$t(n.scrollWidth,n.clientWidth,i.scrollWidth,i.clientWidth),l=$t(n.scrollHeight,n.clientHeight,i.scrollHeight,i.clientHeight);let c=-r.scrollLeft+id(e);const d=-r.scrollTop;return tn(i).direction==="rtl"&&(c+=$t(n.clientWidth,i.clientWidth)-a),{width:a,height:l,x:c,y:d}}function D1(e,n){const r=Nt(e),i=fn(e),a=r.visualViewport;let l=i.clientWidth,c=i.clientHeight,d=0,g=0;if(a){l=a.width,c=a.height;const v=rd();(!v||v&&n==="fixed")&&(d=a.offsetLeft,g=a.offsetTop)}return{width:l,height:c,x:d,y:g}}function O1(e,n){const r=vr(e,!0,n==="fixed"),i=r.top+e.clientTop,a=r.left+e.clientLeft,l=dn(e)?oo(e):cn(1),c=e.clientWidth*l.x,d=e.clientHeight*l.y,g=a*l.x,v=i*l.y;return{width:c,height:d,x:g,y:v}}function gh(e,n,r){let i;if(n==="viewport")i=D1(e,r);else if(n==="document")i=I1(fn(e));else if(en(n))i=O1(n,r);else{const a=nv(e);i={x:n.x-a.x,y:n.y-a.y,width:n.width,height:n.height}}return Zs(i)}function ov(e,n){const r=er(e);return r===n||!en(r)||io(r)?!1:tn(r).position==="fixed"||ov(r,n)}function M1(e,n){const r=n.get(e);if(r)return r;let i=ai(e,[],!1).filter(d=>en(d)&&uo(d)!=="body"),a=null;const l=tn(e).position==="fixed";let c=l?er(e):e;for(;en(c)&&!io(c);){const d=tn(c),g=nd(c);!g&&d.position==="fixed"&&(a=null),(l?!g&&!a:!g&&d.position==="static"&&!!a&&["absolute","fixed"].includes(a.position)||di(c)&&!g&&ov(e,c))?i=i.filter(w=>w!==c):a=d,c=er(c)}return n.set(e,i),i}function k1(e){let{element:n,boundary:r,rootBoundary:i,strategy:a}=e;const c=[...r==="clippingAncestors"?dl(n)?[]:M1(n,this._c):[].concat(r),i],d=c[0],g=c.reduce((v,w)=>{const m=gh(n,w,a);return v.top=$t(m.top,v.top),v.right=Jn(m.right,v.right),v.bottom=Jn(m.bottom,v.bottom),v.left=$t(m.left,v.left),v},gh(n,d,a));return{width:g.right-g.left,height:g.bottom-g.top,x:g.left,y:g.top}}function $1(e){const{width:n,height:r}=tv(e);return{width:n,height:r}}function N1(e,n,r){const i=dn(n),a=fn(n),l=r==="fixed",c=vr(e,!0,l,n);let d={scrollLeft:0,scrollTop:0};const g=cn(0);function v(){g.x=id(a)}if(i||!i&&!l)if((uo(n)!=="body"||di(a))&&(d=fl(n)),i){const y=vr(n,!0,l,n);g.x=y.x+n.clientLeft,g.y=y.y+n.clientTop}else a&&v();l&&!i&&a&&v();const w=a&&!i&&!l?rv(a,d):cn(0),m=c.left+d.scrollLeft-g.x-w.x,S=c.top+d.scrollTop-g.y-w.y;return{x:m,y:S,width:c.width,height:c.height}}function Bu(e){return tn(e).position==="static"}function hh(e,n){if(!dn(e)||tn(e).position==="fixed")return null;if(n)return n(e);let r=e.offsetParent;return fn(e)===r&&(r=r.ownerDocument.body),r}function iv(e,n){const r=Nt(e);if(dl(e))return r;if(!dn(e)){let a=er(e);for(;a&&!io(a);){if(en(a)&&!Bu(a))return a;a=er(a)}return r}let i=hh(e,n);for(;i&&C1(i)&&Bu(i);)i=hh(i,n);return i&&io(i)&&Bu(i)&&!nd(i)?r:i||E1(e)||r}const A1=async function(e){const n=this.getOffsetParent||iv,r=this.getDimensions,i=await r(e.floating);return{reference:N1(e.reference,await n(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function b1(e){return tn(e).direction==="rtl"}const L1={convertOffsetParentRelativeRectToViewportRelativeRect:_1,getDocumentElement:fn,getClippingRect:k1,getOffsetParent:iv,getElementRects:A1,getClientRects:T1,getDimensions:$1,getScale:oo,isElement:en,isRTL:b1};function sv(e,n){return e.x===n.x&&e.y===n.y&&e.width===n.width&&e.height===n.height}function F1(e,n){let r=null,i;const a=fn(e);function l(){var d;clearTimeout(i),(d=r)==null||d.disconnect(),r=null}function c(d,g){d===void 0&&(d=!1),g===void 0&&(g=1),l();const v=e.getBoundingClientRect(),{left:w,top:m,width:S,height:y}=v;if(d||n(),!S||!y)return;const P=Ls(m),x=Ls(a.clientWidth-(w+S)),E=Ls(a.clientHeight-(m+y)),_=Ls(w),I={rootMargin:-P+"px "+-x+"px "+-E+"px "+-_+"px",threshold:$t(0,Jn(1,g))||1};let $=!0;function L(B){const N=B[0].intersectionRatio;if(N!==g){if(!$)return c();N?c(!1,N):i=setTimeout(()=>{c(!1,1e-7)},1e3)}N===1&&!sv(v,e.getBoundingClientRect())&&c(),$=!1}try{r=new IntersectionObserver(L,{...I,root:a.ownerDocument})}catch{r=new IntersectionObserver(L,I)}r.observe(e)}return c(!0),l}function lv(e,n,r,i){i===void 0&&(i={});const{ancestorScroll:a=!0,ancestorResize:l=!0,elementResize:c=typeof ResizeObserver=="function",layoutShift:d=typeof IntersectionObserver=="function",animationFrame:g=!1}=i,v=od(e),w=a||l?[...v?ai(v):[],...ai(n)]:[];w.forEach(_=>{a&&_.addEventListener("scroll",r,{passive:!0}),l&&_.addEventListener("resize",r)});const m=v&&d?F1(v,r):null;let S=-1,y=null;c&&(y=new ResizeObserver(_=>{let[T]=_;T&&T.target===v&&y&&(y.unobserve(n),cancelAnimationFrame(S),S=requestAnimationFrame(()=>{var I;(I=y)==null||I.observe(n)})),r()}),v&&!g&&y.observe(v),y.observe(n));let P,x=g?vr(e):null;g&&E();function E(){const _=vr(e);x&&!sv(x,_)&&r(),x=_,P=requestAnimationFrame(E)}return r(),()=>{var _;w.forEach(T=>{a&&T.removeEventListener("scroll",r),l&&T.removeEventListener("resize",r)}),m==null||m(),(_=y)==null||_.disconnect(),y=null,g&&cancelAnimationFrame(P)}}const j1=y1,V1=w1,z1=h1,H1=x1,U1=m1,mh=g1,B1=S1,W1=(e,n,r)=>{const i=new Map,a={platform:L1,...r},l={...a.platform,_c:i};return p1(e,n,{...a,platform:l})};var Bs=typeof document<"u"?p.useLayoutEffect:p.useEffect;function Js(e,n){if(e===n)return!0;if(typeof e!=typeof n)return!1;if(typeof e=="function"&&e.toString()===n.toString())return!0;let r,i,a;if(e&&n&&typeof e=="object"){if(Array.isArray(e)){if(r=e.length,r!==n.length)return!1;for(i=r;i--!==0;)if(!Js(e[i],n[i]))return!1;return!0}if(a=Object.keys(e),r=a.length,r!==Object.keys(n).length)return!1;for(i=r;i--!==0;)if(!{}.hasOwnProperty.call(n,a[i]))return!1;for(i=r;i--!==0;){const l=a[i];if(!(l==="_owner"&&e.$$typeof)&&!Js(e[l],n[l]))return!1}return!0}return e!==e&&n!==n}function av(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function vh(e,n){const r=av(e);return Math.round(n*r)/r}function Wu(e){const n=p.useRef(e);return Bs(()=>{n.current=e}),n}function uv(e){e===void 0&&(e={});const{placement:n="bottom",strategy:r="absolute",middleware:i=[],platform:a,elements:{reference:l,floating:c}={},transform:d=!0,whileElementsMounted:g,open:v}=e,[w,m]=p.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[S,y]=p.useState(i);Js(S,i)||y(i);const[P,x]=p.useState(null),[E,_]=p.useState(null),T=p.useCallback(A=>{A!==B.current&&(B.current=A,x(A))},[]),I=p.useCallback(A=>{A!==N.current&&(N.current=A,_(A))},[]),$=l||P,L=c||E,B=p.useRef(null),N=p.useRef(null),b=p.useRef(w),K=g!=null,J=Wu(g),ne=Wu(a),te=Wu(v),Z=p.useCallback(()=>{if(!B.current||!N.current)return;const A={placement:n,strategy:r,middleware:S};ne.current&&(A.platform=ne.current),W1(B.current,N.current,A).then(F=>{const U={...F,isPositioned:te.current!==!1};G.current&&!Js(b.current,U)&&(b.current=U,wr.flushSync(()=>{m(U)}))})},[S,n,r,ne,te]);Bs(()=>{v===!1&&b.current.isPositioned&&(b.current.isPositioned=!1,m(A=>({...A,isPositioned:!1})))},[v]);const G=p.useRef(!1);Bs(()=>(G.current=!0,()=>{G.current=!1}),[]),Bs(()=>{if($&&(B.current=$),L&&(N.current=L),$&&L){if(J.current)return J.current($,L,Z);Z()}},[$,L,Z,J,K]);const ae=p.useMemo(()=>({reference:B,floating:N,setReference:T,setFloating:I}),[T,I]),W=p.useMemo(()=>({reference:$,floating:L}),[$,L]),Y=p.useMemo(()=>{const A={position:r,left:0,top:0};if(!W.floating)return A;const F=vh(W.floating,w.x),U=vh(W.floating,w.y);return d?{...A,transform:"translate("+F+"px, "+U+"px)",...av(W.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:F,top:U}},[r,d,W.floating,w.x,w.y]);return p.useMemo(()=>({...w,update:Z,refs:ae,elements:W,floatingStyles:Y}),[w,Z,ae,W,Y])}const G1=e=>{function n(r){return{}.hasOwnProperty.call(r,"current")}return{name:"arrow",options:e,fn(r){const{element:i,padding:a}=typeof e=="function"?e(r):e;return i&&n(i)?i.current!=null?mh({element:i.current,padding:a}).fn(r):{}:i?mh({element:i,padding:a}).fn(r):{}}}},cv=(e,n)=>({...j1(e),options:[e,n]}),dv=(e,n)=>({...V1(e),options:[e,n]}),fv=(e,n)=>({...B1(e),options:[e,n]}),pv=(e,n)=>({...z1(e),options:[e,n]}),gv=(e,n)=>({...H1(e),options:[e,n]}),hv=(e,n)=>({...U1(e),options:[e,n]}),mv=(e,n)=>({...G1(e),options:[e,n]});function K1(e){const[n,r]=p.useState(void 0);return Wt(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const i=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const l=a[0];let c,d;if("borderBoxSize"in l){const g=l.borderBoxSize,v=Array.isArray(g)?g[0]:g;c=v.inlineSize,d=v.blockSize}else c=e.offsetWidth,d=e.offsetHeight;r({width:c,height:d})});return i.observe(e,{box:"border-box"}),()=>i.unobserve(e)}else r(void 0)},[e]),n}const vv="Popper",[yv,wv]=Qc(vv),[X1,Sv]=yv(vv),q1=e=>{const{__scopePopper:n,children:r}=e,[i,a]=p.useState(null);return p.createElement(X1,{scope:n,anchor:i,onAnchorChange:a},r)},Y1="PopperAnchor",Q1=p.forwardRef((e,n)=>{const{__scopePopper:r,virtualRef:i,...a}=e,l=Sv(Y1,r),c=p.useRef(null),d=pt(n,c);return p.useEffect(()=>{l.onAnchorChange((i==null?void 0:i.current)||c.current)}),i?null:p.createElement(gt.div,Oe({},a,{ref:d}))}),xv="PopperContent",[Z1,IO]=yv(xv),J1=p.forwardRef((e,n)=>{var r,i,a,l,c,d,g,v;const{__scopePopper:w,side:m="bottom",sideOffset:S=0,align:y="center",alignOffset:P=0,arrowPadding:x=0,collisionBoundary:E=[],collisionPadding:_=0,sticky:T="partial",hideWhenDetached:I=!1,avoidCollisions:$=!0,onPlaced:L,...B}=e,N=Sv(xv,w),[b,K]=p.useState(null),J=pt(n,ce=>K(ce)),[ne,te]=p.useState(null),Z=K1(ne),G=(r=Z==null?void 0:Z.width)!==null&&r!==void 0?r:0,ae=(i=Z==null?void 0:Z.height)!==null&&i!==void 0?i:0,W=m+(y!=="center"?"-"+y:""),Y=typeof _=="number"?_:{top:0,right:0,bottom:0,left:0,..._},A=Array.isArray(E)?E:[E],F=A.length>0,U={padding:Y,boundary:A.filter(eR),altBoundary:F},{refs:O,floatingStyles:z,placement:ue,isPositioned:de,middlewareData:X}=uv({strategy:"fixed",placement:W,whileElementsMounted:lv,elements:{reference:N.anchor},middleware:[cv({mainAxis:S+ae,alignmentAxis:P}),$&&dv({mainAxis:!0,crossAxis:!1,limiter:T==="partial"?fv():void 0,...U}),$&&pv({...U}),gv({...U,apply:({elements:ce,rects:Ne,availableWidth:nt,availableHeight:rt})=>{const{width:rn,height:st}=Ne.reference,It=ce.floating.style;It.setProperty("--radix-popper-available-width",`${nt}px`),It.setProperty("--radix-popper-available-height",`${rt}px`),It.setProperty("--radix-popper-anchor-width",`${rn}px`),It.setProperty("--radix-popper-anchor-height",`${st}px`)}}),ne&&mv({element:ne,padding:x}),tR({arrowWidth:G,arrowHeight:ae}),I&&hv({strategy:"referenceHidden"})]}),[se,Q]=Cv(ue),oe=_n(L);Wt(()=>{de&&(oe==null||oe())},[de,oe]);const q=(a=X.arrow)===null||a===void 0?void 0:a.x,fe=(l=X.arrow)===null||l===void 0?void 0:l.y,pe=((c=X.arrow)===null||c===void 0?void 0:c.centerOffset)!==0,[Pe,we]=p.useState();return Wt(()=>{b&&we(window.getComputedStyle(b).zIndex)},[b]),p.createElement("div",{ref:O.setFloating,"data-radix-popper-content-wrapper":"",style:{...z,transform:de?z.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Pe,"--radix-popper-transform-origin":[(d=X.transformOrigin)===null||d===void 0?void 0:d.x,(g=X.transformOrigin)===null||g===void 0?void 0:g.y].join(" ")},dir:e.dir},p.createElement(Z1,{scope:w,placedSide:se,onArrowChange:te,arrowX:q,arrowY:fe,shouldHideArrow:pe},p.createElement(gt.div,Oe({"data-side":se,"data-align":Q},B,{ref:J,style:{...B.style,animation:de?void 0:"none",opacity:(v=X.hide)!==null&&v!==void 0&&v.referenceHidden?0:void 0}}))))});function eR(e){return e!==null}const tR=e=>({name:"transformOrigin",options:e,fn(n){var r,i,a,l,c;const{placement:d,rects:g,middlewareData:v}=n,m=((r=v.arrow)===null||r===void 0?void 0:r.centerOffset)!==0,S=m?0:e.arrowWidth,y=m?0:e.arrowHeight,[P,x]=Cv(d),E={start:"0%",center:"50%",end:"100%"}[x],_=((i=(a=v.arrow)===null||a===void 0?void 0:a.x)!==null&&i!==void 0?i:0)+S/2,T=((l=(c=v.arrow)===null||c===void 0?void 0:c.y)!==null&&l!==void 0?l:0)+y/2;let I="",$="";return P==="bottom"?(I=m?E:`${_}px`,$=`${-y}px`):P==="top"?(I=m?E:`${_}px`,$=`${g.floating.height+y}px`):P==="right"?(I=`${-y}px`,$=m?E:`${T}px`):P==="left"&&(I=`${g.floating.width+y}px`,$=m?E:`${T}px`),{data:{x:I,y:$}}}});function Cv(e){const[n,r="center"]=e.split("-");return[n,r]}const nR=q1,rR=Q1,oR=J1,iR=p.forwardRef((e,n)=>{var r;const{container:i=globalThis==null||(r=globalThis.document)===null||r===void 0?void 0:r.body,...a}=e;return i?Kh.createPortal(p.createElement(gt.div,Oe({},a,{ref:n})),i):null});function yh({prop:e,defaultProp:n,onChange:r=()=>{}}){const[i,a]=sR({defaultProp:n,onChange:r}),l=e!==void 0,c=l?e:i,d=_n(r),g=p.useCallback(v=>{if(l){const m=typeof v=="function"?v(e):v;m!==e&&d(m)}else a(v)},[l,e,a,d]);return[c,g]}function sR({defaultProp:e,onChange:n}){const r=p.useState(e),[i]=r,a=p.useRef(i),l=_n(n);return p.useEffect(()=>{a.current!==i&&(l(i),a.current=i)},[i,a,l]),r}function lR(e){const n=p.useRef({value:e,previous:e});return p.useMemo(()=>(n.current.value!==e&&(n.current.previous=n.current.value,n.current.value=e),n.current.previous),[e])}function aR(e,n){typeof e=="function"?e(n):e!=null&&(e.current=n)}function uR(...e){return n=>e.forEach(r=>aR(r,n))}const Ev=p.forwardRef((e,n)=>{const{children:r,...i}=e,a=p.Children.toArray(r),l=a.find(dR);if(l){const c=l.props.children,d=a.map(g=>g===l?p.Children.count(c)>1?p.Children.only(null):p.isValidElement(c)?c.props.children:null:g);return p.createElement(wc,Oe({},i,{ref:n}),p.isValidElement(c)?p.cloneElement(c,void 0,d):null)}return p.createElement(wc,Oe({},i,{ref:n}),r)});Ev.displayName="Slot";const wc=p.forwardRef((e,n)=>{const{children:r,...i}=e;return p.isValidElement(r)?p.cloneElement(r,{...fR(i,r.props),ref:n?uR(n,r.ref):r.ref}):p.Children.count(r)>1?p.Children.only(null):null});wc.displayName="SlotClone";const cR=({children:e})=>p.createElement(p.Fragment,null,e);function dR(e){return p.isValidElement(e)&&e.type===cR}function fR(e,n){const r={...n};for(const i in n){const a=e[i],l=n[i];/^on[A-Z]/.test(i)?a&&l?r[i]=(...d)=>{l(...d),a(...d)}:a&&(r[i]=a):i==="style"?r[i]={...a,...l}:i==="className"&&(r[i]=[a,l].filter(Boolean).join(" "))}return{...e,...r}}const pR=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],gR=pR.reduce((e,n)=>{const r=p.forwardRef((i,a)=>{const{asChild:l,...c}=i,d=l?Ev:n;return p.useEffect(()=>{window[Symbol.for("radix-ui")]=!0},[]),p.createElement(d,Oe({},c,{ref:a}))});return r.displayName=`Primitive.${n}`,{...e,[n]:r}},{}),hR=p.forwardRef((e,n)=>p.createElement(gR.span,Oe({},e,{ref:n,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}})));var Rv=tm(),Gu=function(){},pl=p.forwardRef(function(e,n){var r=p.useRef(null),i=p.useState({onScrollCapture:Gu,onWheelCapture:Gu,onTouchMoveCapture:Gu}),a=i[0],l=i[1],c=e.forwardProps,d=e.children,g=e.className,v=e.removeScrollBar,w=e.enabled,m=e.shards,S=e.sideCar,y=e.noIsolation,P=e.inert,x=e.allowPinchZoom,E=e.as,_=E===void 0?"div":E,T=jc(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),I=S,$=em([r,n]),L=ft(ft({},T),a);return p.createElement(p.Fragment,null,w&&p.createElement(I,{sideCar:Rv,removeScrollBar:v,shards:m,noIsolation:y,inert:P,setCallbacks:l,allowPinchZoom:!!x,lockRef:r}),c?p.cloneElement(p.Children.only(d),ft(ft({},L),{ref:$})):p.createElement(_,ft({},L,{className:g,ref:$}),d))});pl.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};pl.classNames={fullWidth:ni,zeroRight:ti};var Sc=!1;if(typeof window<"u")try{var Fs=Object.defineProperty({},"passive",{get:function(){return Sc=!0,!0}});window.addEventListener("test",Fs,Fs),window.removeEventListener("test",Fs,Fs)}catch{Sc=!1}var Zr=Sc?{passive:!1}:!1,mR=function(e){return e.tagName==="TEXTAREA"},Pv=function(e,n){var r=window.getComputedStyle(e);return r[n]!=="hidden"&&!(r.overflowY===r.overflowX&&!mR(e)&&r[n]==="visible")},vR=function(e){return Pv(e,"overflowY")},yR=function(e){return Pv(e,"overflowX")},wh=function(e,n){var r=n;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var i=_v(e,r);if(i){var a=Tv(e,r),l=a[1],c=a[2];if(l>c)return!0}r=r.parentNode}while(r&&r!==document.body);return!1},wR=function(e){var n=e.scrollTop,r=e.scrollHeight,i=e.clientHeight;return[n,r,i]},SR=function(e){var n=e.scrollLeft,r=e.scrollWidth,i=e.clientWidth;return[n,r,i]},_v=function(e,n){return e==="v"?vR(n):yR(n)},Tv=function(e,n){return e==="v"?wR(n):SR(n)},xR=function(e,n){return e==="h"&&n==="rtl"?-1:1},CR=function(e,n,r,i,a){var l=xR(e,window.getComputedStyle(n).direction),c=l*i,d=r.target,g=n.contains(d),v=!1,w=c>0,m=0,S=0;do{var y=Tv(e,d),P=y[0],x=y[1],E=y[2],_=x-E-l*P;(P||_)&&_v(e,d)&&(m+=_,S+=P),d=d.parentNode}while(!g&&d!==document.body||g&&(n.contains(d)||n===d));return(w&&m===0||!w&&S===0)&&(v=!0),v},js=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Sh=function(e){return[e.deltaX,e.deltaY]},xh=function(e){return e&&"current"in e?e.current:e},ER=function(e,n){return e[0]===n[0]&&e[1]===n[1]},RR=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},PR=0,Jr=[];function _R(e){var n=p.useRef([]),r=p.useRef([0,0]),i=p.useRef(),a=p.useState(PR++)[0],l=p.useState(function(){return Vc()})[0],c=p.useRef(e);p.useEffect(function(){c.current=e},[e]),p.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var x=Jh([e.lockRef.current],(e.shards||[]).map(xh),!0).filter(Boolean);return x.forEach(function(E){return E.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),x.forEach(function(E){return E.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var d=p.useCallback(function(x,E){if("touches"in x&&x.touches.length===2)return!c.current.allowPinchZoom;var _=js(x),T=r.current,I="deltaX"in x?x.deltaX:T[0]-_[0],$="deltaY"in x?x.deltaY:T[1]-_[1],L,B=x.target,N=Math.abs(I)>Math.abs($)?"h":"v";if("touches"in x&&N==="h"&&B.type==="range")return!1;var b=wh(N,B);if(!b)return!0;if(b?L=N:(L=N==="v"?"h":"v",b=wh(N,B)),!b)return!1;if(!i.current&&"changedTouches"in x&&(I||$)&&(i.current=L),!L)return!0;var K=i.current||L;return CR(K,E,x,K==="h"?I:$)},[]),g=p.useCallback(function(x){var E=x;if(!(!Jr.length||Jr[Jr.length-1]!==l)){var _="deltaY"in E?Sh(E):js(E),T=n.current.filter(function(L){return L.name===E.type&&L.target===E.target&&ER(L.delta,_)})[0];if(T&&T.should){E.cancelable&&E.preventDefault();return}if(!T){var I=(c.current.shards||[]).map(xh).filter(Boolean).filter(function(L){return L.contains(E.target)}),$=I.length>0?d(E,I[0]):!c.current.noIsolation;$&&E.cancelable&&E.preventDefault()}}},[]),v=p.useCallback(function(x,E,_,T){var I={name:x,delta:E,target:_,should:T};n.current.push(I),setTimeout(function(){n.current=n.current.filter(function($){return $!==I})},1)},[]),w=p.useCallback(function(x){r.current=js(x),i.current=void 0},[]),m=p.useCallback(function(x){v(x.type,Sh(x),x.target,d(x,e.lockRef.current))},[]),S=p.useCallback(function(x){v(x.type,js(x),x.target,d(x,e.lockRef.current))},[]);p.useEffect(function(){return Jr.push(l),e.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:S}),document.addEventListener("wheel",g,Zr),document.addEventListener("touchmove",g,Zr),document.addEventListener("touchstart",w,Zr),function(){Jr=Jr.filter(function(x){return x!==l}),document.removeEventListener("wheel",g,Zr),document.removeEventListener("touchmove",g,Zr),document.removeEventListener("touchstart",w,Zr)}},[]);var y=e.removeScrollBar,P=e.inert;return p.createElement(p.Fragment,null,P?p.createElement(l,{styles:RR(a)}):null,y?p.createElement(im,{gapMode:"margin"}):null)}const TR=rm(Rv,_R);var Iv=p.forwardRef(function(e,n){return p.createElement(pl,ft({},e,{ref:n,sideCar:TR}))});Iv.classNames=pl.classNames;const IR=[" ","Enter","ArrowUp","ArrowDown"],DR=[" ","Enter"],gl="Select",[hl,ml,OR]=LE(gl),[co,DO]=Qc(gl,[OR,wv]),sd=wv(),[MR,Sr]=co(gl),[kR,$R]=co(gl),NR=e=>{const{__scopeSelect:n,children:r,open:i,defaultOpen:a,onOpenChange:l,value:c,defaultValue:d,onValueChange:g,dir:v,name:w,autoComplete:m,disabled:S,required:y}=e,P=sd(n),[x,E]=p.useState(null),[_,T]=p.useState(null),[I,$]=p.useState(!1),L=jE(v),[B=!1,N]=yh({prop:i,defaultProp:a,onChange:l}),[b,K]=yh({prop:c,defaultProp:d,onChange:g}),J=p.useRef(null),ne=x?!!x.closest("form"):!0,[te,Z]=p.useState(new Set),G=Array.from(te).map(ae=>ae.props.value).join(";");return p.createElement(nR,P,p.createElement(MR,{required:y,scope:n,trigger:x,onTriggerChange:E,valueNode:_,onValueNodeChange:T,valueNodeHasChildren:I,onValueNodeHasChildrenChange:$,contentId:Zc(),value:b,onValueChange:K,open:B,onOpenChange:N,dir:L,triggerPointerDownPosRef:J,disabled:S},p.createElement(hl.Provider,{scope:n},p.createElement(kR,{scope:e.__scopeSelect,onNativeOptionAdd:p.useCallback(ae=>{Z(W=>new Set(W).add(ae))},[]),onNativeOptionRemove:p.useCallback(ae=>{Z(W=>{const Y=new Set(W);return Y.delete(ae),Y})},[])},r)),ne?p.createElement(kv,{key:G,"aria-hidden":!0,required:y,tabIndex:-1,name:w,autoComplete:m,value:b,onChange:ae=>K(ae.target.value),disabled:S},b===void 0?p.createElement("option",{value:""}):null,Array.from(te)):null))},AR="SelectTrigger",bR=p.forwardRef((e,n)=>{const{__scopeSelect:r,disabled:i=!1,...a}=e,l=sd(r),c=Sr(AR,r),d=c.disabled||i,g=pt(n,c.onTriggerChange),v=ml(r),[w,m,S]=$v(P=>{const x=v().filter(T=>!T.disabled),E=x.find(T=>T.value===c.value),_=Nv(x,P,E);_!==void 0&&c.onValueChange(_.value)}),y=()=>{d||(c.onOpenChange(!0),S())};return p.createElement(rR,Oe({asChild:!0},l),p.createElement(gt.button,Oe({type:"button",role:"combobox","aria-controls":c.contentId,"aria-expanded":c.open,"aria-required":c.required,"aria-autocomplete":"none",dir:c.dir,"data-state":c.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":c.value===void 0?"":void 0},a,{ref:g,onClick:tt(a.onClick,P=>{P.currentTarget.focus()}),onPointerDown:tt(a.onPointerDown,P=>{const x=P.target;x.hasPointerCapture(P.pointerId)&&x.releasePointerCapture(P.pointerId),P.button===0&&P.ctrlKey===!1&&(y(),c.triggerPointerDownPosRef.current={x:Math.round(P.pageX),y:Math.round(P.pageY)},P.preventDefault())}),onKeyDown:tt(a.onKeyDown,P=>{const x=w.current!=="";!(P.ctrlKey||P.altKey||P.metaKey)&&P.key.length===1&&m(P.key),!(x&&P.key===" ")&&IR.includes(P.key)&&(y(),P.preventDefault())})})))}),LR="SelectValue",FR=p.forwardRef((e,n)=>{const{__scopeSelect:r,className:i,style:a,children:l,placeholder:c,...d}=e,g=Sr(LR,r),{onValueNodeHasChildrenChange:v}=g,w=l!==void 0,m=pt(n,g.onValueNodeChange);return Wt(()=>{v(w)},[v,w]),p.createElement(gt.span,Oe({},d,{ref:m,style:{pointerEvents:"none"}}),g.value===void 0&&c!==void 0?c:l)}),jR=p.forwardRef((e,n)=>{const{__scopeSelect:r,children:i,...a}=e;return p.createElement(gt.span,Oe({"aria-hidden":!0},a,{ref:n}),i||"▼")}),VR=e=>p.createElement(iR,Oe({asChild:!0},e)),so="SelectContent",zR=p.forwardRef((e,n)=>{const r=Sr(so,e.__scopeSelect),[i,a]=p.useState();if(Wt(()=>{a(new DocumentFragment)},[]),!r.open){const l=i;return l?wr.createPortal(p.createElement(Dv,{scope:e.__scopeSelect},p.createElement(hl.Slot,{scope:e.__scopeSelect},p.createElement("div",null,e.children))),l):null}return p.createElement(HR,Oe({},e,{ref:n}))}),En=10,[Dv,xr]=co(so),HR=p.forwardRef((e,n)=>{const{__scopeSelect:r,position:i="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:c,side:d,sideOffset:g,align:v,alignOffset:w,arrowPadding:m,collisionBoundary:S,collisionPadding:y,sticky:P,hideWhenDetached:x,avoidCollisions:E,..._}=e,T=Sr(so,r),[I,$]=p.useState(null),[L,B]=p.useState(null),N=pt(n,Q=>$(Q)),[b,K]=p.useState(null),[J,ne]=p.useState(null),te=ml(r),[Z,G]=p.useState(!1),ae=p.useRef(!1);p.useEffect(()=>{if(I)return Hc(I)},[I]),qE();const W=p.useCallback(Q=>{const[oe,...q]=te().map(Pe=>Pe.ref.current),[fe]=q.slice(-1),pe=document.activeElement;for(const Pe of Q)if(Pe===pe||(Pe==null||Pe.scrollIntoView({block:"nearest"}),Pe===oe&&L&&(L.scrollTop=0),Pe===fe&&L&&(L.scrollTop=L.scrollHeight),Pe==null||Pe.focus(),document.activeElement!==pe))return},[te,L]),Y=p.useCallback(()=>W([b,I]),[W,b,I]);p.useEffect(()=>{Z&&Y()},[Z,Y]);const{onOpenChange:A,triggerPointerDownPosRef:F}=T;p.useEffect(()=>{if(I){let Q={x:0,y:0};const oe=fe=>{var pe,Pe,we,ce;Q={x:Math.abs(Math.round(fe.pageX)-((pe=(Pe=F.current)===null||Pe===void 0?void 0:Pe.x)!==null&&pe!==void 0?pe:0)),y:Math.abs(Math.round(fe.pageY)-((we=(ce=F.current)===null||ce===void 0?void 0:ce.y)!==null&&we!==void 0?we:0))}},q=fe=>{Q.x<=10&&Q.y<=10?fe.preventDefault():I.contains(fe.target)||A(!1),document.removeEventListener("pointermove",oe),F.current=null};return F.current!==null&&(document.addEventListener("pointermove",oe),document.addEventListener("pointerup",q,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",oe),document.removeEventListener("pointerup",q,{capture:!0})}}},[I,A,F]),p.useEffect(()=>{const Q=()=>A(!1);return window.addEventListener("blur",Q),window.addEventListener("resize",Q),()=>{window.removeEventListener("blur",Q),window.removeEventListener("resize",Q)}},[A]);const[U,O]=$v(Q=>{const oe=te().filter(pe=>!pe.disabled),q=oe.find(pe=>pe.ref.current===document.activeElement),fe=Nv(oe,Q,q);fe&&setTimeout(()=>fe.ref.current.focus())}),z=p.useCallback((Q,oe,q)=>{const fe=!ae.current&&!q;(T.value!==void 0&&T.value===oe||fe)&&(K(Q),fe&&(ae.current=!0))},[T.value]),ue=p.useCallback(()=>I==null?void 0:I.focus(),[I]),de=p.useCallback((Q,oe,q)=>{const fe=!ae.current&&!q;(T.value!==void 0&&T.value===oe||fe)&&ne(Q)},[T.value]),X=i==="popper"?Ch:UR,se=X===Ch?{side:d,sideOffset:g,align:v,alignOffset:w,arrowPadding:m,collisionBoundary:S,collisionPadding:y,sticky:P,hideWhenDetached:x,avoidCollisions:E}:{};return p.createElement(Dv,{scope:r,content:I,viewport:L,onViewportChange:B,itemRefCallback:z,selectedItem:b,onItemLeave:ue,itemTextRefCallback:de,focusSelectedItem:Y,selectedItemText:J,position:i,isPositioned:Z,searchRef:U},p.createElement(Iv,{as:si,allowPinchZoom:!0},p.createElement(YE,{asChild:!0,trapped:T.open,onMountAutoFocus:Q=>{Q.preventDefault()},onUnmountAutoFocus:tt(a,Q=>{var oe;(oe=T.trigger)===null||oe===void 0||oe.focus({preventScroll:!0}),Q.preventDefault()})},p.createElement(GE,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:c,onFocusOutside:Q=>Q.preventDefault(),onDismiss:()=>T.onOpenChange(!1)},p.createElement(X,Oe({role:"listbox",id:T.contentId,"data-state":T.open?"open":"closed",dir:T.dir,onContextMenu:Q=>Q.preventDefault()},_,se,{onPlaced:()=>G(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",..._.style},onKeyDown:tt(_.onKeyDown,Q=>{const oe=Q.ctrlKey||Q.altKey||Q.metaKey;if(Q.key==="Tab"&&Q.preventDefault(),!oe&&Q.key.length===1&&O(Q.key),["ArrowUp","ArrowDown","Home","End"].includes(Q.key)){let fe=te().filter(pe=>!pe.disabled).map(pe=>pe.ref.current);if(["ArrowUp","End"].includes(Q.key)&&(fe=fe.slice().reverse()),["ArrowUp","ArrowDown"].includes(Q.key)){const pe=Q.target,Pe=fe.indexOf(pe);fe=fe.slice(Pe+1)}setTimeout(()=>W(fe)),Q.preventDefault()}})}))))))}),UR=p.forwardRef((e,n)=>{const{__scopeSelect:r,onPlaced:i,...a}=e,l=Sr(so,r),c=xr(so,r),[d,g]=p.useState(null),[v,w]=p.useState(null),m=pt(n,N=>w(N)),S=ml(r),y=p.useRef(!1),P=p.useRef(!0),{viewport:x,selectedItem:E,selectedItemText:_,focusSelectedItem:T}=c,I=p.useCallback(()=>{if(l.trigger&&l.valueNode&&d&&v&&x&&E&&_){const N=l.trigger.getBoundingClientRect(),b=v.getBoundingClientRect(),K=l.valueNode.getBoundingClientRect(),J=_.getBoundingClientRect();if(l.dir!=="rtl"){const pe=J.left-b.left,Pe=K.left-pe,we=N.left-Pe,ce=N.width+we,Ne=Math.max(ce,b.width),nt=window.innerWidth-En,rt=nh(Pe,[En,nt-Ne]);d.style.minWidth=ce+"px",d.style.left=rt+"px"}else{const pe=b.right-J.right,Pe=window.innerWidth-K.right-pe,we=window.innerWidth-N.right-Pe,ce=N.width+we,Ne=Math.max(ce,b.width),nt=window.innerWidth-En,rt=nh(Pe,[En,nt-Ne]);d.style.minWidth=ce+"px",d.style.right=rt+"px"}const ne=S(),te=window.innerHeight-En*2,Z=x.scrollHeight,G=window.getComputedStyle(v),ae=parseInt(G.borderTopWidth,10),W=parseInt(G.paddingTop,10),Y=parseInt(G.borderBottomWidth,10),A=parseInt(G.paddingBottom,10),F=ae+W+Z+A+Y,U=Math.min(E.offsetHeight*5,F),O=window.getComputedStyle(x),z=parseInt(O.paddingTop,10),ue=parseInt(O.paddingBottom,10),de=N.top+N.height/2-En,X=te-de,se=E.offsetHeight/2,Q=E.offsetTop+se,oe=ae+W+Q,q=F-oe;if(oe<=de){const pe=E===ne[ne.length-1].ref.current;d.style.bottom="0px";const Pe=v.clientHeight-x.offsetTop-x.offsetHeight,we=Math.max(X,se+(pe?ue:0)+Pe+Y),ce=oe+we;d.style.height=ce+"px"}else{const pe=E===ne[0].ref.current;d.style.top="0px";const we=Math.max(de,ae+x.offsetTop+(pe?z:0)+se)+q;d.style.height=we+"px",x.scrollTop=oe-de+x.offsetTop}d.style.margin=`${En}px 0`,d.style.minHeight=U+"px",d.style.maxHeight=te+"px",i==null||i(),requestAnimationFrame(()=>y.current=!0)}},[S,l.trigger,l.valueNode,d,v,x,E,_,l.dir,i]);Wt(()=>I(),[I]);const[$,L]=p.useState();Wt(()=>{v&&L(window.getComputedStyle(v).zIndex)},[v]);const B=p.useCallback(N=>{N&&P.current===!0&&(I(),T==null||T(),P.current=!1)},[I,T]);return p.createElement(BR,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:y,onScrollButtonChange:B},p.createElement("div",{ref:g,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:$}},p.createElement(gt.div,Oe({},a,{ref:m,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}}))))}),Ch=p.forwardRef((e,n)=>{const{__scopeSelect:r,align:i="start",collisionPadding:a=En,...l}=e,c=sd(r);return p.createElement(oR,Oe({},c,l,{ref:n,align:i,collisionPadding:a,style:{boxSizing:"border-box",...l.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}}))}),[BR,ld]=co(so,{}),Eh="SelectViewport",WR=p.forwardRef((e,n)=>{const{__scopeSelect:r,...i}=e,a=xr(Eh,r),l=ld(Eh,r),c=pt(n,a.onViewportChange),d=p.useRef(0);return p.createElement(p.Fragment,null,p.createElement("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"}}),p.createElement(hl.Slot,{scope:r},p.createElement(gt.div,Oe({"data-radix-select-viewport":"",role:"presentation"},i,{ref:c,style:{position:"relative",flex:1,overflow:"auto",...i.style},onScroll:tt(i.onScroll,g=>{const v=g.currentTarget,{contentWrapper:w,shouldExpandOnScrollRef:m}=l;if(m!=null&&m.current&&w){const S=Math.abs(d.current-v.scrollTop);if(S>0){const y=window.innerHeight-En*2,P=parseFloat(w.style.minHeight),x=parseFloat(w.style.height),E=Math.max(P,x);if(E<y){const _=E+S,T=Math.min(y,_),I=_-T;w.style.height=T+"px",w.style.bottom==="0px"&&(v.scrollTop=I>0?I:0,w.style.justifyContent="flex-end")}}}d.current=v.scrollTop})}))))}),GR="SelectGroup",[KR,XR]=co(GR),qR=p.forwardRef((e,n)=>{const{__scopeSelect:r,...i}=e,a=Zc();return p.createElement(KR,{scope:r,id:a},p.createElement(gt.div,Oe({role:"group","aria-labelledby":a},i,{ref:n})))}),YR="SelectLabel",QR=p.forwardRef((e,n)=>{const{__scopeSelect:r,...i}=e,a=XR(YR,r);return p.createElement(gt.div,Oe({id:a.id},i,{ref:n}))}),xc="SelectItem",[ZR,Ov]=co(xc),JR=p.forwardRef((e,n)=>{const{__scopeSelect:r,value:i,disabled:a=!1,textValue:l,...c}=e,d=Sr(xc,r),g=xr(xc,r),v=d.value===i,[w,m]=p.useState(l??""),[S,y]=p.useState(!1),P=pt(n,_=>{var T;return(T=g.itemRefCallback)===null||T===void 0?void 0:T.call(g,_,i,a)}),x=Zc(),E=()=>{a||(d.onValueChange(i),d.onOpenChange(!1))};return p.createElement(ZR,{scope:r,value:i,disabled:a,textId:x,isSelected:v,onItemTextChange:p.useCallback(_=>{m(T=>{var I;return T||((I=_==null?void 0:_.textContent)!==null&&I!==void 0?I:"").trim()})},[])},p.createElement(hl.ItemSlot,{scope:r,value:i,disabled:a,textValue:w},p.createElement(gt.div,Oe({role:"option","aria-labelledby":x,"data-highlighted":S?"":void 0,"aria-selected":v&&S,"data-state":v?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1},c,{ref:P,onFocus:tt(c.onFocus,()=>y(!0)),onBlur:tt(c.onBlur,()=>y(!1)),onPointerUp:tt(c.onPointerUp,E),onPointerMove:tt(c.onPointerMove,_=>{if(a){var T;(T=g.onItemLeave)===null||T===void 0||T.call(g)}else _.currentTarget.focus({preventScroll:!0})}),onPointerLeave:tt(c.onPointerLeave,_=>{if(_.currentTarget===document.activeElement){var T;(T=g.onItemLeave)===null||T===void 0||T.call(g)}}),onKeyDown:tt(c.onKeyDown,_=>{var T;((T=g.searchRef)===null||T===void 0?void 0:T.current)!==""&&_.key===" "||(DR.includes(_.key)&&E(),_.key===" "&&_.preventDefault())})}))))}),Vs="SelectItemText",eP=p.forwardRef((e,n)=>{const{__scopeSelect:r,className:i,style:a,...l}=e,c=Sr(Vs,r),d=xr(Vs,r),g=Ov(Vs,r),v=$R(Vs,r),[w,m]=p.useState(null),S=pt(n,_=>m(_),g.onItemTextChange,_=>{var T;return(T=d.itemTextRefCallback)===null||T===void 0?void 0:T.call(d,_,g.value,g.disabled)}),y=w==null?void 0:w.textContent,P=p.useMemo(()=>p.createElement("option",{key:g.value,value:g.value,disabled:g.disabled},y),[g.disabled,g.value,y]),{onNativeOptionAdd:x,onNativeOptionRemove:E}=v;return Wt(()=>(x(P),()=>E(P)),[x,E,P]),p.createElement(p.Fragment,null,p.createElement(gt.span,Oe({id:g.textId},l,{ref:S})),g.isSelected&&c.valueNode&&!c.valueNodeHasChildren?wr.createPortal(l.children,c.valueNode):null)}),tP="SelectItemIndicator",nP=p.forwardRef((e,n)=>{const{__scopeSelect:r,...i}=e;return Ov(tP,r).isSelected?p.createElement(gt.span,Oe({"aria-hidden":!0},i,{ref:n})):null}),Rh="SelectScrollUpButton",rP=p.forwardRef((e,n)=>{const r=xr(Rh,e.__scopeSelect),i=ld(Rh,e.__scopeSelect),[a,l]=p.useState(!1),c=pt(n,i.onScrollButtonChange);return Wt(()=>{if(r.viewport&&r.isPositioned){let v=function(){const w=g.scrollTop>0;l(w)};var d=v;const g=r.viewport;return v(),g.addEventListener("scroll",v),()=>g.removeEventListener("scroll",v)}},[r.viewport,r.isPositioned]),a?p.createElement(Mv,Oe({},e,{ref:c,onAutoScroll:()=>{const{viewport:d,selectedItem:g}=r;d&&g&&(d.scrollTop=d.scrollTop-g.offsetHeight)}})):null}),Ph="SelectScrollDownButton",oP=p.forwardRef((e,n)=>{const r=xr(Ph,e.__scopeSelect),i=ld(Ph,e.__scopeSelect),[a,l]=p.useState(!1),c=pt(n,i.onScrollButtonChange);return Wt(()=>{if(r.viewport&&r.isPositioned){let v=function(){const w=g.scrollHeight-g.clientHeight,m=Math.ceil(g.scrollTop)<w;l(m)};var d=v;const g=r.viewport;return v(),g.addEventListener("scroll",v),()=>g.removeEventListener("scroll",v)}},[r.viewport,r.isPositioned]),a?p.createElement(Mv,Oe({},e,{ref:c,onAutoScroll:()=>{const{viewport:d,selectedItem:g}=r;d&&g&&(d.scrollTop=d.scrollTop+g.offsetHeight)}})):null}),Mv=p.forwardRef((e,n)=>{const{__scopeSelect:r,onAutoScroll:i,...a}=e,l=xr("SelectScrollButton",r),c=p.useRef(null),d=ml(r),g=p.useCallback(()=>{c.current!==null&&(window.clearInterval(c.current),c.current=null)},[]);return p.useEffect(()=>()=>g(),[g]),Wt(()=>{var v;const w=d().find(m=>m.ref.current===document.activeElement);w==null||(v=w.ref.current)===null||v===void 0||v.scrollIntoView({block:"nearest"})},[d]),p.createElement(gt.div,Oe({"aria-hidden":!0},a,{ref:n,style:{flexShrink:0,...a.style},onPointerDown:tt(a.onPointerDown,()=>{c.current===null&&(c.current=window.setInterval(i,50))}),onPointerMove:tt(a.onPointerMove,()=>{var v;(v=l.onItemLeave)===null||v===void 0||v.call(l),c.current===null&&(c.current=window.setInterval(i,50))}),onPointerLeave:tt(a.onPointerLeave,()=>{g()})}))}),kv=p.forwardRef((e,n)=>{const{value:r,...i}=e,a=p.useRef(null),l=pt(n,a),c=lR(r);return p.useEffect(()=>{const d=a.current,g=window.HTMLSelectElement.prototype,w=Object.getOwnPropertyDescriptor(g,"value").set;if(c!==r&&w){const m=new Event("change",{bubbles:!0});w.call(d,r),d.dispatchEvent(m)}},[c,r]),p.createElement(hR,{asChild:!0},p.createElement("select",Oe({},i,{ref:l,defaultValue:r})))});kv.displayName="BubbleSelect";function $v(e){const n=_n(e),r=p.useRef(""),i=p.useRef(0),a=p.useCallback(c=>{const d=r.current+c;n(d),function g(v){r.current=v,window.clearTimeout(i.current),v!==""&&(i.current=window.setTimeout(()=>g(""),1e3))}(d)},[n]),l=p.useCallback(()=>{r.current="",window.clearTimeout(i.current)},[]);return p.useEffect(()=>()=>window.clearTimeout(i.current),[]),[r,a,l]}function Nv(e,n,r){const a=n.length>1&&Array.from(n).every(v=>v===n[0])?n[0]:n,l=r?e.indexOf(r):-1;let c=iP(e,Math.max(l,0));a.length===1&&(c=c.filter(v=>v!==r));const g=c.find(v=>v.textValue.toLowerCase().startsWith(a.toLowerCase()));return g!==r?g:void 0}function iP(e,n){return e.map((r,i)=>e[(n+i)%e.length])}const sP=NR,lP=bR,aP=FR,uP=jR,cP=VR,dP=zR,fP=WR,Ku=qR,_h=QR,pP=JR,gP=eP,hP=nP,mP=rP,vP=oP;function ad(e,n){if(e==null)return{};var r={},i=Object.keys(e),a,l;for(l=0;l<i.length;l++)a=i[l],!(n.indexOf(a)>=0)&&(r[a]=e[a]);return r}var yP=["color"],wP=p.forwardRef(function(e,n){var r=e.color,i=r===void 0?"currentColor":r,a=ad(e,yP);return p.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a,{ref:n}),p.createElement("path",{d:"M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z",fill:i,fillRule:"evenodd",clipRule:"evenodd"}))}),SP=["color"],Cc=p.forwardRef(function(e,n){var r=e.color,i=r===void 0?"currentColor":r,a=ad(e,SP);return p.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a,{ref:n}),p.createElement("path",{d:"M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z",fill:i,fillRule:"evenodd",clipRule:"evenodd"}))}),xP=["color"],CP=p.forwardRef(function(e,n){var r=e.color,i=r===void 0?"currentColor":r,a=ad(e,xP);return p.createElement("svg",Object.assign({width:"15",height:"15",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a,{ref:n}),p.createElement("path",{d:"M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z",fill:i,fillRule:"evenodd",clipRule:"evenodd"}))});const to=({value:e,onChange:n,label:r="Select",placeholder:i="Select a fruit…",groups:a,labelType:l="col"})=>{const c=(a==null?void 0:a.length)===1;return R.jsxs(sP,{value:e,onValueChange:n,children:[R.jsxs(Ku,{className:vt("flex gap-1",{"flex-row items-center gap-2":l==="row","flex-col":l==="col"}),children:[R.jsx(_h,{className:"whitespace-nowrap",children:r}),R.jsxs(lP,{className:"justify-between bg-white text-black dark:text-white dark:bg-grey-900 whitespace-nowrap h-[36px] border-[1.5px] border-grey-700 rounded p-3 inline-flex items-center leading-none gap-[5px] shadow-[0_2px_10px] shadow-black/10 focus:shadow-[0_0_0_2px] focus:shadow-black data-[placeholder]:text-white outline-none","aria-label":r,children:[R.jsx(aP,{placeholder:i}),R.jsx(uP,{children:R.jsx(Cc,{})})]})]}),R.jsx(cP,{children:R.jsxs(dP,{className:"z-50 bg-white/80 dark:bg-grey-900/80 backdrop-filter backdrop-blur overflow-hidden border-[1.5px] border-grey-700 rounded p-3 shadow-[0px_10px_38px_-10px_rgba(22,_23,_24,_0.35),0px_10px_20px_-15px_rgba(22,_23,_24,_0.2)]",children:[R.jsx(mP,{className:"flex items-center justify-center h-[25px] cursor-default dark:text-white text-black",children:R.jsx(CP,{})}),R.jsx(fP,{className:"p-[5px]",children:c?R.jsx(Ku,{children:a[0].items.map(d=>R.jsx(Th,{value:d.value,disabled:d.disabled,children:d.label}))}):a.map((d,g)=>R.jsxs(Ku,{children:[R.jsx(_h,{className:"text-xs leading-[25px]",children:d.label}),d.items.map(v=>R.jsx(Th,{value:v.value,disabled:v.disabled,children:v.label},v.value))]},d.label))}),R.jsx(vP,{className:"flex items-center justify-center h-[25px] cursor-default dark:text-white text-black",children:R.jsx(Cc,{})})]})})]})},Th=p.forwardRef(({children:e,className:n,...r},i)=>R.jsxs(pP,{className:vt("text-[13px] leading-none rounded-[3px] flex items-center h-[25px] pr-[35px] pl-[25px] relative select-none data-[disabled]:text-grey-400 data-[disabled]:pointer-events-none data-[highlighted]:outline-none data-[highlighted]:bg-grey-600 data-[highlighted]:text-white  text-black dark:text-white",n),...r,ref:i,children:[R.jsx(gP,{children:e}),R.jsx(hP,{className:"absolute left-0 w-[25px] inline-flex items-center justify-center",children:R.jsx(wP,{})})]}));function ud(e){const n=e+"CollectionProvider",[r,i]=tr(n),[a,l]=r(n,{collectionRef:{current:null},itemMap:new Map}),c=x=>{const{scope:E,children:_}=x,T=qe.useRef(null),I=qe.useRef(new Map).current;return R.jsx(a,{scope:E,itemMap:I,collectionRef:T,children:_})};c.displayName=n;const d=e+"CollectionSlot",g=ii(d),v=qe.forwardRef((x,E)=>{const{scope:_,children:T}=x,I=l(d,_),$=Ye(E,I.collectionRef);return R.jsx(g,{ref:$,children:T})});v.displayName=d;const w=e+"CollectionItemSlot",m="data-radix-collection-item",S=ii(w),y=qe.forwardRef((x,E)=>{const{scope:_,children:T,...I}=x,$=qe.useRef(null),L=Ye(E,$),B=l(w,_);return qe.useEffect(()=>(B.itemMap.set($,{ref:$,...I}),()=>void B.itemMap.delete($))),R.jsx(S,{[m]:"",ref:L,children:T})});y.displayName=w;function P(x){const E=l(e+"CollectionConsumer",x);return qe.useCallback(()=>{const T=E.collectionRef.current;if(!T)return[];const I=Array.from(T.querySelectorAll(`[${m}]`));return Array.from(E.itemMap.values()).sort((B,N)=>I.indexOf(B.ref.current)-I.indexOf(N.ref.current))},[E.collectionRef,E.itemMap])}return[{Provider:c,Slot:v,ItemSlot:y},P,i]}var EP=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),RP="VisuallyHidden",cd=p.forwardRef((e,n)=>R.jsx($e.span,{...e,ref:n,style:{...EP,...e.style}}));cd.displayName=RP;var dd="ToastProvider",[fd,PP,_P]=ud("Toast"),[Av,OO]=tr("Toast",[_P]),[TP,vl]=Av(dd),bv=e=>{const{__scopeToast:n,label:r="Notification",duration:i=5e3,swipeDirection:a="right",swipeThreshold:l=50,children:c}=e,[d,g]=p.useState(null),[v,w]=p.useState(0),m=p.useRef(!1),S=p.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${dd}\`. Expected non-empty \`string\`.`),R.jsx(fd.Provider,{scope:n,children:R.jsx(TP,{scope:n,label:r,duration:i,swipeDirection:a,swipeThreshold:l,toastCount:v,viewport:d,onViewportChange:g,onToastAdd:p.useCallback(()=>w(y=>y+1),[]),onToastRemove:p.useCallback(()=>w(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:S,children:c})})};bv.displayName=dd;var Lv="ToastViewport",IP=["F8"],Ec="toast.viewportPause",Rc="toast.viewportResume",Fv=p.forwardRef((e,n)=>{const{__scopeToast:r,hotkey:i=IP,label:a="Notifications ({hotkey})",...l}=e,c=vl(Lv,r),d=PP(r),g=p.useRef(null),v=p.useRef(null),w=p.useRef(null),m=p.useRef(null),S=Ye(n,m,c.onViewportChange),y=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),P=c.toastCount>0;p.useEffect(()=>{const E=_=>{var I;i.length!==0&&i.every($=>_[$]||_.code===$)&&((I=m.current)==null||I.focus())};return document.addEventListener("keydown",E),()=>document.removeEventListener("keydown",E)},[i]),p.useEffect(()=>{const E=g.current,_=m.current;if(P&&E&&_){const T=()=>{if(!c.isClosePausedRef.current){const B=new CustomEvent(Ec);_.dispatchEvent(B),c.isClosePausedRef.current=!0}},I=()=>{if(c.isClosePausedRef.current){const B=new CustomEvent(Rc);_.dispatchEvent(B),c.isClosePausedRef.current=!1}},$=B=>{!E.contains(B.relatedTarget)&&I()},L=()=>{E.contains(document.activeElement)||I()};return E.addEventListener("focusin",T),E.addEventListener("focusout",$),E.addEventListener("pointermove",T),E.addEventListener("pointerleave",L),window.addEventListener("blur",T),window.addEventListener("focus",I),()=>{E.removeEventListener("focusin",T),E.removeEventListener("focusout",$),E.removeEventListener("pointermove",T),E.removeEventListener("pointerleave",L),window.removeEventListener("blur",T),window.removeEventListener("focus",I)}}},[P,c.isClosePausedRef]);const x=p.useCallback(({tabbingDirection:E})=>{const T=d().map(I=>{const $=I.ref.current,L=[$,...HP($)];return E==="forwards"?L:L.reverse()});return(E==="forwards"?T.reverse():T).flat()},[d]);return p.useEffect(()=>{const E=m.current;if(E){const _=T=>{var L,B,N;const I=T.altKey||T.ctrlKey||T.metaKey;if(T.key==="Tab"&&!I){const b=document.activeElement,K=T.shiftKey;if(T.target===E&&K){(L=v.current)==null||L.focus();return}const te=x({tabbingDirection:K?"backwards":"forwards"}),Z=te.findIndex(G=>G===b);Xu(te.slice(Z+1))?T.preventDefault():K?(B=v.current)==null||B.focus():(N=w.current)==null||N.focus()}};return E.addEventListener("keydown",_),()=>E.removeEventListener("keydown",_)}},[d,x]),R.jsxs(ox,{ref:g,role:"region","aria-label":a.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:P?void 0:"none"},children:[P&&R.jsx(Pc,{ref:v,onFocusFromOutsideViewport:()=>{const E=x({tabbingDirection:"forwards"});Xu(E)}}),R.jsx(fd.Slot,{scope:r,children:R.jsx($e.ol,{tabIndex:-1,...l,ref:S})}),P&&R.jsx(Pc,{ref:w,onFocusFromOutsideViewport:()=>{const E=x({tabbingDirection:"backwards"});Xu(E)}})]})});Fv.displayName=Lv;var jv="ToastFocusProxy",Pc=p.forwardRef((e,n)=>{const{__scopeToast:r,onFocusFromOutsideViewport:i,...a}=e,l=vl(jv,r);return R.jsx(cd,{"aria-hidden":!0,tabIndex:0,...a,ref:n,style:{position:"fixed"},onFocus:c=>{var v;const d=c.relatedTarget;!((v=l.viewport)!=null&&v.contains(d))&&i()}})});Pc.displayName=jv;var fi="Toast",DP="toast.swipeStart",OP="toast.swipeMove",MP="toast.swipeCancel",kP="toast.swipeEnd",Vv=p.forwardRef((e,n)=>{const{forceMount:r,open:i,defaultOpen:a,onOpenChange:l,...c}=e,[d,g]=ol({prop:i,defaultProp:a??!0,onChange:l,caller:fi});return R.jsx(Dn,{present:r||d,children:R.jsx(AP,{open:d,...c,ref:n,onClose:()=>g(!1),onPause:Tt(e.onPause),onResume:Tt(e.onResume),onSwipeStart:Se(e.onSwipeStart,v=>{v.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:Se(e.onSwipeMove,v=>{const{x:w,y:m}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","move"),v.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${w}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${m}px`)}),onSwipeCancel:Se(e.onSwipeCancel,v=>{v.currentTarget.setAttribute("data-swipe","cancel"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:Se(e.onSwipeEnd,v=>{const{x:w,y:m}=v.detail.delta;v.currentTarget.setAttribute("data-swipe","end"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),v.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),v.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${w}px`),v.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${m}px`),g(!1)})})})});Vv.displayName=fi;var[$P,NP]=Av(fi,{onClose(){}}),AP=p.forwardRef((e,n)=>{const{__scopeToast:r,type:i="foreground",duration:a,open:l,onClose:c,onEscapeKeyDown:d,onPause:g,onResume:v,onSwipeStart:w,onSwipeMove:m,onSwipeCancel:S,onSwipeEnd:y,...P}=e,x=vl(fi,r),[E,_]=p.useState(null),T=Ye(n,G=>_(G)),I=p.useRef(null),$=p.useRef(null),L=a||x.duration,B=p.useRef(0),N=p.useRef(L),b=p.useRef(0),{onToastAdd:K,onToastRemove:J}=x,ne=Tt(()=>{var ae;(E==null?void 0:E.contains(document.activeElement))&&((ae=x.viewport)==null||ae.focus()),c()}),te=p.useCallback(G=>{!G||G===1/0||(window.clearTimeout(b.current),B.current=new Date().getTime(),b.current=window.setTimeout(ne,G))},[ne]);p.useEffect(()=>{const G=x.viewport;if(G){const ae=()=>{te(N.current),v==null||v()},W=()=>{const Y=new Date().getTime()-B.current;N.current=N.current-Y,window.clearTimeout(b.current),g==null||g()};return G.addEventListener(Ec,W),G.addEventListener(Rc,ae),()=>{G.removeEventListener(Ec,W),G.removeEventListener(Rc,ae)}}},[x.viewport,L,g,v,te]),p.useEffect(()=>{l&&!x.isClosePausedRef.current&&te(L)},[l,L,x.isClosePausedRef,te]),p.useEffect(()=>(K(),()=>J()),[K,J]);const Z=p.useMemo(()=>E?Gv(E):null,[E]);return x.viewport?R.jsxs(R.Fragment,{children:[Z&&R.jsx(bP,{__scopeToast:r,role:"status","aria-live":i==="foreground"?"assertive":"polite","aria-atomic":!0,children:Z}),R.jsx($P,{scope:r,onClose:ne,children:wr.createPortal(R.jsx(fd.ItemSlot,{scope:r,children:R.jsx(rx,{asChild:!0,onEscapeKeyDown:Se(d,()=>{x.isFocusedToastEscapeKeyDownRef.current||ne(),x.isFocusedToastEscapeKeyDownRef.current=!1}),children:R.jsx($e.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":x.swipeDirection,...P,ref:T,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:Se(e.onKeyDown,G=>{G.key==="Escape"&&(d==null||d(G.nativeEvent),G.nativeEvent.defaultPrevented||(x.isFocusedToastEscapeKeyDownRef.current=!0,ne()))}),onPointerDown:Se(e.onPointerDown,G=>{G.button===0&&(I.current={x:G.clientX,y:G.clientY})}),onPointerMove:Se(e.onPointerMove,G=>{if(!I.current)return;const ae=G.clientX-I.current.x,W=G.clientY-I.current.y,Y=!!$.current,A=["left","right"].includes(x.swipeDirection),F=["left","up"].includes(x.swipeDirection)?Math.min:Math.max,U=A?F(0,ae):0,O=A?0:F(0,W),z=G.pointerType==="touch"?10:2,ue={x:U,y:O},de={originalEvent:G,delta:ue};Y?($.current=ue,zs(OP,m,de,{discrete:!1})):Ih(ue,x.swipeDirection,z)?($.current=ue,zs(DP,w,de,{discrete:!1}),G.target.setPointerCapture(G.pointerId)):(Math.abs(ae)>z||Math.abs(W)>z)&&(I.current=null)}),onPointerUp:Se(e.onPointerUp,G=>{const ae=$.current,W=G.target;if(W.hasPointerCapture(G.pointerId)&&W.releasePointerCapture(G.pointerId),$.current=null,I.current=null,ae){const Y=G.currentTarget,A={originalEvent:G,delta:ae};Ih(ae,x.swipeDirection,x.swipeThreshold)?zs(kP,y,A,{discrete:!0}):zs(MP,S,A,{discrete:!0}),Y.addEventListener("click",F=>F.preventDefault(),{once:!0})}})})})}),x.viewport)})]}):null}),bP=e=>{const{__scopeToast:n,children:r,...i}=e,a=vl(fi,n),[l,c]=p.useState(!1),[d,g]=p.useState(!1);return VP(()=>c(!0)),p.useEffect(()=>{const v=window.setTimeout(()=>g(!0),1e3);return()=>window.clearTimeout(v)},[]),d?null:R.jsx(sl,{asChild:!0,children:R.jsx(cd,{...i,children:l&&R.jsxs(R.Fragment,{children:[a.label," ",r]})})})},LP="ToastTitle",zv=p.forwardRef((e,n)=>{const{__scopeToast:r,...i}=e;return R.jsx($e.div,{...i,ref:n})});zv.displayName=LP;var FP="ToastDescription",Hv=p.forwardRef((e,n)=>{const{__scopeToast:r,...i}=e;return R.jsx($e.div,{...i,ref:n})});Hv.displayName=FP;var Uv="ToastAction",jP=p.forwardRef((e,n)=>{const{altText:r,...i}=e;return r.trim()?R.jsx(Wv,{altText:r,asChild:!0,children:R.jsx(pd,{...i,ref:n})}):(console.error(`Invalid prop \`altText\` supplied to \`${Uv}\`. Expected non-empty \`string\`.`),null)});jP.displayName=Uv;var Bv="ToastClose",pd=p.forwardRef((e,n)=>{const{__scopeToast:r,...i}=e,a=NP(Bv,r);return R.jsx(Wv,{asChild:!0,children:R.jsx($e.button,{type:"button",...i,ref:n,onClick:Se(e.onClick,a.onClose)})})});pd.displayName=Bv;var Wv=p.forwardRef((e,n)=>{const{__scopeToast:r,altText:i,...a}=e;return R.jsx($e.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":i||void 0,...a,ref:n})});function Gv(e){const n=[];return Array.from(e.childNodes).forEach(i=>{if(i.nodeType===i.TEXT_NODE&&i.textContent&&n.push(i.textContent),zP(i)){const a=i.ariaHidden||i.hidden||i.style.display==="none",l=i.dataset.radixToastAnnounceExclude==="";if(!a)if(l){const c=i.dataset.radixToastAnnounceAlt;c&&n.push(c)}else n.push(...Gv(i))}}),n}function zs(e,n,r,{discrete:i}){const a=r.originalEvent.currentTarget,l=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});n&&a.addEventListener(e,n,{once:!0}),i?Lc(a,l):a.dispatchEvent(l)}var Ih=(e,n,r=0)=>{const i=Math.abs(e.x),a=Math.abs(e.y),l=i>a;return n==="left"||n==="right"?l&&i>r:!l&&a>r};function VP(e=()=>{}){const n=Tt(e);Pn(()=>{let r=0,i=0;return r=window.requestAnimationFrame(()=>i=window.requestAnimationFrame(n)),()=>{window.cancelAnimationFrame(r),window.cancelAnimationFrame(i)}},[n])}function zP(e){return e.nodeType===e.ELEMENT_NODE}function HP(e){const n=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:i=>{const a=i.tagName==="INPUT"&&i.type==="hidden";return i.disabled||i.hidden||a?NodeFilter.FILTER_SKIP:i.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)n.push(r.currentNode);return n}function Xu(e){const n=document.activeElement;return e.some(r=>r===n?!0:(r.focus(),document.activeElement!==n))}var UP=bv,BP=Fv,WP=Vv,GP=zv,KP=Hv,XP=pd;const qP=({title:e,titleId:n,...r})=>R.jsxs("svg",{viewBox:"0 0 18 18",width:18,height:18,fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":n,...r,children:[e?R.jsx("title",{id:n,children:e}):null,R.jsx("path",{d:"M9 16.5a7.5 7.5 0 1 0 0-15 7.5 7.5 0 0 0 0 15ZM11.25 6.75l-4.5 4.5M6.75 6.75l4.5 4.5",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})]}),YP=({title:e,titleId:n,...r})=>R.jsxs("svg",{viewBox:"0 0 24 24",width:24,height:24,fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":n,...r,children:[e?R.jsx("title",{id:n,children:e}):null,R.jsx("path",{d:"M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10ZM12 16v-4M12 8h.01",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})]}),QP=({title:e,titleId:n,...r})=>R.jsxs("svg",{width:18,height:18,viewBox:"0 0 18 18",fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":n,...r,children:[e?R.jsx("title",{id:n,children:e}):null,R.jsx("path",{d:"M16.5 8.31V9a7.5 7.5 0 1 1-4.447-6.855",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"}),R.jsx("path",{d:"M16.5 3 9 10.508l-2.25-2.25",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})]}),ZP=({title:e,titleId:n,...r})=>R.jsxs("svg",{viewBox:"0 0 18 18",width:18,height:18,fill:"none",xmlns:"http://www.w3.org/2000/svg","aria-labelledby":n,...r,children:[e?R.jsx("title",{id:n,children:e}):null,R.jsx("path",{d:"M7.718 2.895 1.366 13.5a1.5 1.5 0 0 0 1.282 2.25h12.705a1.5 1.5 0 0 0 1.283-2.25L10.283 2.895a1.5 1.5 0 0 0-2.565 0v0ZM9 6.75v3M9 12.75h.008",stroke:"currentColor",strokeWidth:1.5,strokeLinecap:"round",strokeLinejoin:"round"})]}),JP=({toast:e,open:n,setOpen:r})=>R.jsxs(UP,{children:[R.jsxs(WP,{open:n,onOpenChange:i=>{e.preventClose||r(i)},className:vt("z-50 fixed bottom-4 md:left-1/2 md:-translate-x-[50%] inset-x-4 w-auto shadow-lg md:max-w-[658px] duration-300","radix-state-open:animate-fade-in","radix-state-closed:animate-toast-hide","radix-swipe-end:animate-toast-swipe-out","translate-x-radix-toast-swipe-move-x","radix-swipe-cancel:translate-x-0 radix-swipe-cancel:duration-200 radix-swipe-cancel:ease-[ease]","px-[40px] md:px-[58px] py-6 flex flex-col border rounded-[4px]",{"bg-green-100 text-green-600 border-green-600":e.status==="success","bg-red-200 text-red-600 border-red-600":e.status==="error","bg-blue-100 text-blue-700 border-blue-600":e.status==="info","bg-orange-200 text-orange-600 border-orange-600":e.status==="warning"},{"h-[72px]":!e.description}),children:[e.status==="success"?R.jsx(QP,{className:"absolute left-[8px] md:left-[25px] top-[25px]"}):e.status==="warning"?R.jsx(ZP,{className:"absolute left-[8px] md:left-[25px] top-[25px]"}):e.status==="error"?R.jsx(qP,{className:"absolute left-[8px] md:left-[25px] top-[25px]"}):R.jsx(YP,{className:"absolute left-[8px] md:left-[25px] top-[25px]"}),R.jsx(GP,{className:"text-grey-900 font-bold text-sm",children:e.title}),e.description&&R.jsx(KP,{className:"mt-2 text-[10px] md:text-xs text-grey-800",children:e.description}),R.jsx(XP,{className:"absolute top-7 right-5 md:right-7",children:R.jsx(qs,{className:"w-4 h-4 text-grey-900"})})]}),R.jsx(BP,{})]});var e_=p.createContext(void 0);function Kv(e){const n=p.useContext(e_);return e||n||"ltr"}var t_="Arrow",Xv=p.forwardRef((e,n)=>{const{children:r,width:i=10,height:a=5,...l}=e;return R.jsx($e.svg,{...l,ref:n,width:i,height:a,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:R.jsx("polygon",{points:"0,0 30,0 15,10"})})});Xv.displayName=t_;var n_=Xv;function r_(e){const[n,r]=p.useState(void 0);return Pn(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});const i=new ResizeObserver(a=>{if(!Array.isArray(a)||!a.length)return;const l=a[0];let c,d;if("borderBoxSize"in l){const g=l.borderBoxSize,v=Array.isArray(g)?g[0]:g;c=v.inlineSize,d=v.blockSize}else c=e.offsetWidth,d=e.offsetHeight;r({width:c,height:d})});return i.observe(e,{box:"border-box"}),()=>i.unobserve(e)}else r(void 0)},[e]),n}var gd="Popper",[qv,Yv]=tr(gd),[o_,Qv]=qv(gd),Zv=e=>{const{__scopePopper:n,children:r}=e,[i,a]=p.useState(null);return R.jsx(o_,{scope:n,anchor:i,onAnchorChange:a,children:r})};Zv.displayName=gd;var Jv="PopperAnchor",ey=p.forwardRef((e,n)=>{const{__scopePopper:r,virtualRef:i,...a}=e,l=Qv(Jv,r),c=p.useRef(null),d=Ye(n,c);return p.useEffect(()=>{l.onAnchorChange((i==null?void 0:i.current)||c.current)}),i?null:R.jsx($e.div,{...a,ref:d})});ey.displayName=Jv;var hd="PopperContent",[i_,s_]=qv(hd),ty=p.forwardRef((e,n)=>{var Q,oe,q,fe,pe,Pe;const{__scopePopper:r,side:i="bottom",sideOffset:a=0,align:l="center",alignOffset:c=0,arrowPadding:d=0,avoidCollisions:g=!0,collisionBoundary:v=[],collisionPadding:w=0,sticky:m="partial",hideWhenDetached:S=!1,updatePositionStrategy:y="optimized",onPlaced:P,...x}=e,E=Qv(hd,r),[_,T]=p.useState(null),I=Ye(n,we=>T(we)),[$,L]=p.useState(null),B=r_($),N=(B==null?void 0:B.width)??0,b=(B==null?void 0:B.height)??0,K=i+(l!=="center"?"-"+l:""),J=typeof w=="number"?w:{top:0,right:0,bottom:0,left:0,...w},ne=Array.isArray(v)?v:[v],te=ne.length>0,Z={padding:J,boundary:ne.filter(a_),altBoundary:te},{refs:G,floatingStyles:ae,placement:W,isPositioned:Y,middlewareData:A}=uv({strategy:"fixed",placement:K,whileElementsMounted:(...we)=>lv(...we,{animationFrame:y==="always"}),elements:{reference:E.anchor},middleware:[cv({mainAxis:a+b,alignmentAxis:c}),g&&dv({mainAxis:!0,crossAxis:!1,limiter:m==="partial"?fv():void 0,...Z}),g&&pv({...Z}),gv({...Z,apply:({elements:we,rects:ce,availableWidth:Ne,availableHeight:nt})=>{const{width:rt,height:rn}=ce.reference,st=we.floating.style;st.setProperty("--radix-popper-available-width",`${Ne}px`),st.setProperty("--radix-popper-available-height",`${nt}px`),st.setProperty("--radix-popper-anchor-width",`${rt}px`),st.setProperty("--radix-popper-anchor-height",`${rn}px`)}}),$&&mv({element:$,padding:d}),u_({arrowWidth:N,arrowHeight:b}),S&&hv({strategy:"referenceHidden",...Z})]}),[F,U]=oy(W),O=Tt(P);Pn(()=>{Y&&(O==null||O())},[Y,O]);const z=(Q=A.arrow)==null?void 0:Q.x,ue=(oe=A.arrow)==null?void 0:oe.y,de=((q=A.arrow)==null?void 0:q.centerOffset)!==0,[X,se]=p.useState();return Pn(()=>{_&&se(window.getComputedStyle(_).zIndex)},[_]),R.jsx("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...ae,transform:Y?ae.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:X,"--radix-popper-transform-origin":[(fe=A.transformOrigin)==null?void 0:fe.x,(pe=A.transformOrigin)==null?void 0:pe.y].join(" "),...((Pe=A.hide)==null?void 0:Pe.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:R.jsx(i_,{scope:r,placedSide:F,onArrowChange:L,arrowX:z,arrowY:ue,shouldHideArrow:de,children:R.jsx($e.div,{"data-side":F,"data-align":U,...x,ref:I,style:{...x.style,animation:Y?void 0:"none"}})})})});ty.displayName=hd;var ny="PopperArrow",l_={top:"bottom",right:"left",bottom:"top",left:"right"},ry=p.forwardRef(function(n,r){const{__scopePopper:i,...a}=n,l=s_(ny,i),c=l_[l.placedSide];return R.jsx("span",{ref:l.onArrowChange,style:{position:"absolute",left:l.arrowX,top:l.arrowY,[c]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[l.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[l.placedSide],visibility:l.shouldHideArrow?"hidden":void 0},children:R.jsx(n_,{...a,ref:r,style:{...a.style,display:"block"}})})});ry.displayName=ny;function a_(e){return e!==null}var u_=e=>({name:"transformOrigin",options:e,fn(n){var E,_,T;const{placement:r,rects:i,middlewareData:a}=n,c=((E=a.arrow)==null?void 0:E.centerOffset)!==0,d=c?0:e.arrowWidth,g=c?0:e.arrowHeight,[v,w]=oy(r),m={start:"0%",center:"50%",end:"100%"}[w],S=(((_=a.arrow)==null?void 0:_.x)??0)+d/2,y=(((T=a.arrow)==null?void 0:T.y)??0)+g/2;let P="",x="";return v==="bottom"?(P=c?m:`${S}px`,x=`${-g}px`):v==="top"?(P=c?m:`${S}px`,x=`${i.floating.height+g}px`):v==="right"?(P=`${-g}px`,x=c?m:`${y}px`):v==="left"&&(P=`${i.floating.width+g}px`,x=c?m:`${y}px`),{data:{x:P,y:x}}}});function oy(e){const[n,r="center"]=e.split("-");return[n,r]}var c_=Zv,d_=ey,f_=ty,p_=ry,qu="rovingFocusGroup.onEntryFocus",g_={bubbles:!1,cancelable:!0},pi="RovingFocusGroup",[_c,iy,h_]=ud(pi),[m_,sy]=tr(pi,[h_]),[v_,y_]=m_(pi),ly=p.forwardRef((e,n)=>R.jsx(_c.Provider,{scope:e.__scopeRovingFocusGroup,children:R.jsx(_c.Slot,{scope:e.__scopeRovingFocusGroup,children:R.jsx(w_,{...e,ref:n})})}));ly.displayName=pi;var w_=p.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:l,currentTabStopId:c,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:g,onEntryFocus:v,preventScrollOnEntryFocus:w=!1,...m}=e,S=p.useRef(null),y=Ye(n,S),P=Kv(l),[x,E]=ol({prop:c,defaultProp:d??null,onChange:g,caller:pi}),[_,T]=p.useState(!1),I=Tt(v),$=iy(r),L=p.useRef(!1),[B,N]=p.useState(0);return p.useEffect(()=>{const b=S.current;if(b)return b.addEventListener(qu,I),()=>b.removeEventListener(qu,I)},[I]),R.jsx(v_,{scope:r,orientation:i,dir:P,loop:a,currentTabStopId:x,onItemFocus:p.useCallback(b=>E(b),[E]),onItemShiftTab:p.useCallback(()=>T(!0),[]),onFocusableItemAdd:p.useCallback(()=>N(b=>b+1),[]),onFocusableItemRemove:p.useCallback(()=>N(b=>b-1),[]),children:R.jsx($e.div,{tabIndex:_||B===0?-1:0,"data-orientation":i,...m,ref:y,style:{outline:"none",...e.style},onMouseDown:Se(e.onMouseDown,()=>{L.current=!0}),onFocus:Se(e.onFocus,b=>{const K=!L.current;if(b.target===b.currentTarget&&K&&!_){const J=new CustomEvent(qu,g_);if(b.currentTarget.dispatchEvent(J),!J.defaultPrevented){const ne=$().filter(W=>W.focusable),te=ne.find(W=>W.active),Z=ne.find(W=>W.id===x),ae=[te,Z,...ne].filter(Boolean).map(W=>W.ref.current);cy(ae,w)}}L.current=!1}),onBlur:Se(e.onBlur,()=>T(!1))})})}),ay="RovingFocusGroupItem",uy=p.forwardRef((e,n)=>{const{__scopeRovingFocusGroup:r,focusable:i=!0,active:a=!1,tabStopId:l,children:c,...d}=e,g=no(),v=l||g,w=y_(ay,r),m=w.currentTabStopId===v,S=iy(r),{onFocusableItemAdd:y,onFocusableItemRemove:P,currentTabStopId:x}=w;return p.useEffect(()=>{if(i)return y(),()=>P()},[i,y,P]),R.jsx(_c.ItemSlot,{scope:r,id:v,focusable:i,active:a,children:R.jsx($e.span,{tabIndex:m?0:-1,"data-orientation":w.orientation,...d,ref:n,onMouseDown:Se(e.onMouseDown,E=>{i?w.onItemFocus(v):E.preventDefault()}),onFocus:Se(e.onFocus,()=>w.onItemFocus(v)),onKeyDown:Se(e.onKeyDown,E=>{if(E.key==="Tab"&&E.shiftKey){w.onItemShiftTab();return}if(E.target!==E.currentTarget)return;const _=C_(E,w.orientation,w.dir);if(_!==void 0){if(E.metaKey||E.ctrlKey||E.altKey||E.shiftKey)return;E.preventDefault();let I=S().filter($=>$.focusable).map($=>$.ref.current);if(_==="last")I.reverse();else if(_==="prev"||_==="next"){_==="prev"&&I.reverse();const $=I.indexOf(E.currentTarget);I=w.loop?E_(I,$+1):I.slice($+1)}setTimeout(()=>cy(I))}}),children:typeof c=="function"?c({isCurrentTabStop:m,hasTabStop:x!=null}):c})})});uy.displayName=ay;var S_={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function x_(e,n){return n!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function C_(e,n,r){const i=x_(e.key,r);if(!(n==="vertical"&&["ArrowLeft","ArrowRight"].includes(i))&&!(n==="horizontal"&&["ArrowUp","ArrowDown"].includes(i)))return S_[i]}function cy(e,n=!1){const r=document.activeElement;for(const i of e)if(i===r||(i.focus({preventScroll:n}),document.activeElement!==r))return}function E_(e,n){return e.map((r,i)=>e[(n+i)%e.length])}var R_=ly,P_=uy,Tc=["Enter"," "],__=["ArrowDown","PageUp","Home"],dy=["ArrowUp","PageDown","End"],T_=[...__,...dy],I_={ltr:[...Tc,"ArrowRight"],rtl:[...Tc,"ArrowLeft"]},D_={ltr:["ArrowLeft"],rtl:["ArrowRight"]},gi="Menu",[ui,O_,M_]=ud(gi),[Cr,yl]=tr(gi,[M_,Yv,sy]),wl=Yv(),fy=sy(),[k_,Er]=Cr(gi),[$_,hi]=Cr(gi),py=e=>{const{__scopeMenu:n,open:r=!1,children:i,dir:a,onOpenChange:l,modal:c=!0}=e,d=wl(n),[g,v]=p.useState(null),w=p.useRef(!1),m=Tt(l),S=Kv(a);return p.useEffect(()=>{const y=()=>{w.current=!0,document.addEventListener("pointerdown",P,{capture:!0,once:!0}),document.addEventListener("pointermove",P,{capture:!0,once:!0})},P=()=>w.current=!1;return document.addEventListener("keydown",y,{capture:!0}),()=>{document.removeEventListener("keydown",y,{capture:!0}),document.removeEventListener("pointerdown",P,{capture:!0}),document.removeEventListener("pointermove",P,{capture:!0})}},[]),R.jsx(c_,{...d,children:R.jsx(k_,{scope:n,open:r,onOpenChange:m,content:g,onContentChange:v,children:R.jsx($_,{scope:n,onClose:p.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:w,dir:S,modal:c,children:i})})})};py.displayName=gi;var N_="MenuAnchor",md=p.forwardRef((e,n)=>{const{__scopeMenu:r,...i}=e,a=wl(r);return R.jsx(d_,{...a,...i,ref:n})});md.displayName=N_;var vd="MenuPortal",[A_,gy]=Cr(vd,{forceMount:void 0}),hy=e=>{const{__scopeMenu:n,forceMount:r,children:i,container:a}=e,l=Er(vd,n);return R.jsx(A_,{scope:n,forceMount:r,children:R.jsx(Dn,{present:r||l.open,children:R.jsx(sl,{asChild:!0,container:a,children:i})})})};hy.displayName=vd;var Bt="MenuContent",[b_,yd]=Cr(Bt),my=p.forwardRef((e,n)=>{const r=gy(Bt,e.__scopeMenu),{forceMount:i=r.forceMount,...a}=e,l=Er(Bt,e.__scopeMenu),c=hi(Bt,e.__scopeMenu);return R.jsx(ui.Provider,{scope:e.__scopeMenu,children:R.jsx(Dn,{present:i||l.open,children:R.jsx(ui.Slot,{scope:e.__scopeMenu,children:c.modal?R.jsx(L_,{...a,ref:n}):R.jsx(F_,{...a,ref:n})})})})}),L_=p.forwardRef((e,n)=>{const r=Er(Bt,e.__scopeMenu),i=p.useRef(null),a=Ye(n,i);return p.useEffect(()=>{const l=i.current;if(l)return Hc(l)},[]),R.jsx(wd,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:Se(e.onFocusOutside,l=>l.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),F_=p.forwardRef((e,n)=>{const r=Er(Bt,e.__scopeMenu);return R.jsx(wd,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),j_=ii("MenuContent.ScrollLock"),wd=p.forwardRef((e,n)=>{const{__scopeMenu:r,loop:i=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:d,onEntryFocus:g,onEscapeKeyDown:v,onPointerDownOutside:w,onFocusOutside:m,onInteractOutside:S,onDismiss:y,disableOutsideScroll:P,...x}=e,E=Er(Bt,r),_=hi(Bt,r),T=wl(r),I=fy(r),$=O_(r),[L,B]=p.useState(null),N=p.useRef(null),b=Ye(n,N,E.onContentChange),K=p.useRef(0),J=p.useRef(""),ne=p.useRef(0),te=p.useRef(null),Z=p.useRef("right"),G=p.useRef(0),ae=P?zc:p.Fragment,W=P?{as:j_,allowPinchZoom:!0}:void 0,Y=F=>{var Q,oe;const U=J.current+F,O=$().filter(q=>!q.disabled),z=document.activeElement,ue=(Q=O.find(q=>q.ref.current===z))==null?void 0:Q.textValue,de=O.map(q=>q.textValue),X=Q_(de,U,ue),se=(oe=O.find(q=>q.textValue===X))==null?void 0:oe.ref.current;(function q(fe){J.current=fe,window.clearTimeout(K.current),fe!==""&&(K.current=window.setTimeout(()=>q(""),1e3))})(U),se&&setTimeout(()=>se.focus())};p.useEffect(()=>()=>window.clearTimeout(K.current),[]),Zh();const A=p.useCallback(F=>{var O,z;return Z.current===((O=te.current)==null?void 0:O.side)&&J_(F,(z=te.current)==null?void 0:z.area)},[]);return R.jsx(b_,{scope:r,searchRef:J,onItemEnter:p.useCallback(F=>{A(F)&&F.preventDefault()},[A]),onItemLeave:p.useCallback(F=>{var U;A(F)||((U=N.current)==null||U.focus(),B(null))},[A]),onTriggerLeave:p.useCallback(F=>{A(F)&&F.preventDefault()},[A]),pointerGraceTimerRef:ne,onPointerGraceIntentChange:p.useCallback(F=>{te.current=F},[]),children:R.jsx(ae,{...W,children:R.jsx(Fc,{asChild:!0,trapped:a,onMountAutoFocus:Se(l,F=>{var U;F.preventDefault(),(U=N.current)==null||U.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:R.jsx(il,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:v,onPointerDownOutside:w,onFocusOutside:m,onInteractOutside:S,onDismiss:y,children:R.jsx(R_,{asChild:!0,...I,dir:_.dir,orientation:"vertical",loop:i,currentTabStopId:L,onCurrentTabStopIdChange:B,onEntryFocus:Se(g,F=>{_.isUsingKeyboardRef.current||F.preventDefault()}),preventScrollOnEntryFocus:!0,children:R.jsx(f_,{role:"menu","aria-orientation":"vertical","data-state":ky(E.open),"data-radix-menu-content":"",dir:_.dir,...T,...x,ref:b,style:{outline:"none",...x.style},onKeyDown:Se(x.onKeyDown,F=>{const O=F.target.closest("[data-radix-menu-content]")===F.currentTarget,z=F.ctrlKey||F.altKey||F.metaKey,ue=F.key.length===1;O&&(F.key==="Tab"&&F.preventDefault(),!z&&ue&&Y(F.key));const de=N.current;if(F.target!==de||!T_.includes(F.key))return;F.preventDefault();const se=$().filter(Q=>!Q.disabled).map(Q=>Q.ref.current);dy.includes(F.key)&&se.reverse(),q_(se)}),onBlur:Se(e.onBlur,F=>{F.currentTarget.contains(F.target)||(window.clearTimeout(K.current),J.current="")}),onPointerMove:Se(e.onPointerMove,ci(F=>{const U=F.target,O=G.current!==F.clientX;if(F.currentTarget.contains(U)&&O){const z=F.clientX>G.current?"right":"left";Z.current=z,G.current=F.clientX}}))})})})})})})});my.displayName=Bt;var V_="MenuGroup",Sd=p.forwardRef((e,n)=>{const{__scopeMenu:r,...i}=e;return R.jsx($e.div,{role:"group",...i,ref:n})});Sd.displayName=V_;var z_="MenuLabel",vy=p.forwardRef((e,n)=>{const{__scopeMenu:r,...i}=e;return R.jsx($e.div,{...i,ref:n})});vy.displayName=z_;var el="MenuItem",Dh="menu.itemSelect",Sl=p.forwardRef((e,n)=>{const{disabled:r=!1,onSelect:i,...a}=e,l=p.useRef(null),c=hi(el,e.__scopeMenu),d=yd(el,e.__scopeMenu),g=Ye(n,l),v=p.useRef(!1),w=()=>{const m=l.current;if(!r&&m){const S=new CustomEvent(Dh,{bubbles:!0,cancelable:!0});m.addEventListener(Dh,y=>i==null?void 0:i(y),{once:!0}),Lc(m,S),S.defaultPrevented?v.current=!1:c.onClose()}};return R.jsx(yy,{...a,ref:g,disabled:r,onClick:Se(e.onClick,w),onPointerDown:m=>{var S;(S=e.onPointerDown)==null||S.call(e,m),v.current=!0},onPointerUp:Se(e.onPointerUp,m=>{var S;v.current||(S=m.currentTarget)==null||S.click()}),onKeyDown:Se(e.onKeyDown,m=>{const S=d.searchRef.current!=="";r||S&&m.key===" "||Tc.includes(m.key)&&(m.currentTarget.click(),m.preventDefault())})})});Sl.displayName=el;var yy=p.forwardRef((e,n)=>{const{__scopeMenu:r,disabled:i=!1,textValue:a,...l}=e,c=yd(el,r),d=fy(r),g=p.useRef(null),v=Ye(n,g),[w,m]=p.useState(!1),[S,y]=p.useState("");return p.useEffect(()=>{const P=g.current;P&&y((P.textContent??"").trim())},[l.children]),R.jsx(ui.ItemSlot,{scope:r,disabled:i,textValue:a??S,children:R.jsx(P_,{asChild:!0,...d,focusable:!i,children:R.jsx($e.div,{role:"menuitem","data-highlighted":w?"":void 0,"aria-disabled":i||void 0,"data-disabled":i?"":void 0,...l,ref:v,onPointerMove:Se(e.onPointerMove,ci(P=>{i?c.onItemLeave(P):(c.onItemEnter(P),P.defaultPrevented||P.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:Se(e.onPointerLeave,ci(P=>c.onItemLeave(P))),onFocus:Se(e.onFocus,()=>m(!0)),onBlur:Se(e.onBlur,()=>m(!1))})})})}),H_="MenuCheckboxItem",wy=p.forwardRef((e,n)=>{const{checked:r=!1,onCheckedChange:i,...a}=e;return R.jsx(Ry,{scope:e.__scopeMenu,checked:r,children:R.jsx(Sl,{role:"menuitemcheckbox","aria-checked":tl(r)?"mixed":r,...a,ref:n,"data-state":Cd(r),onSelect:Se(a.onSelect,()=>i==null?void 0:i(tl(r)?!0:!r),{checkForDefaultPrevented:!1})})})});wy.displayName=H_;var Sy="MenuRadioGroup",[U_,B_]=Cr(Sy,{value:void 0,onValueChange:()=>{}}),xy=p.forwardRef((e,n)=>{const{value:r,onValueChange:i,...a}=e,l=Tt(i);return R.jsx(U_,{scope:e.__scopeMenu,value:r,onValueChange:l,children:R.jsx(Sd,{...a,ref:n})})});xy.displayName=Sy;var Cy="MenuRadioItem",Ey=p.forwardRef((e,n)=>{const{value:r,...i}=e,a=B_(Cy,e.__scopeMenu),l=r===a.value;return R.jsx(Ry,{scope:e.__scopeMenu,checked:l,children:R.jsx(Sl,{role:"menuitemradio","aria-checked":l,...i,ref:n,"data-state":Cd(l),onSelect:Se(i.onSelect,()=>{var c;return(c=a.onValueChange)==null?void 0:c.call(a,r)},{checkForDefaultPrevented:!1})})})});Ey.displayName=Cy;var xd="MenuItemIndicator",[Ry,W_]=Cr(xd,{checked:!1}),Py=p.forwardRef((e,n)=>{const{__scopeMenu:r,forceMount:i,...a}=e,l=W_(xd,r);return R.jsx(Dn,{present:i||tl(l.checked)||l.checked===!0,children:R.jsx($e.span,{...a,ref:n,"data-state":Cd(l.checked)})})});Py.displayName=xd;var G_="MenuSeparator",_y=p.forwardRef((e,n)=>{const{__scopeMenu:r,...i}=e;return R.jsx($e.div,{role:"separator","aria-orientation":"horizontal",...i,ref:n})});_y.displayName=G_;var K_="MenuArrow",Ty=p.forwardRef((e,n)=>{const{__scopeMenu:r,...i}=e,a=wl(r);return R.jsx(p_,{...a,...i,ref:n})});Ty.displayName=K_;var X_="MenuSub",[MO,Iy]=Cr(X_),ei="MenuSubTrigger",Dy=p.forwardRef((e,n)=>{const r=Er(ei,e.__scopeMenu),i=hi(ei,e.__scopeMenu),a=Iy(ei,e.__scopeMenu),l=yd(ei,e.__scopeMenu),c=p.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:g}=l,v={__scopeMenu:e.__scopeMenu},w=p.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return p.useEffect(()=>w,[w]),p.useEffect(()=>{const m=d.current;return()=>{window.clearTimeout(m),g(null)}},[d,g]),R.jsx(md,{asChild:!0,...v,children:R.jsx(yy,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":ky(r.open),...e,ref:rl(n,a.onTriggerChange),onClick:m=>{var S;(S=e.onClick)==null||S.call(e,m),!(e.disabled||m.defaultPrevented)&&(m.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:Se(e.onPointerMove,ci(m=>{l.onItemEnter(m),!m.defaultPrevented&&!e.disabled&&!r.open&&!c.current&&(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{r.onOpenChange(!0),w()},100))})),onPointerLeave:Se(e.onPointerLeave,ci(m=>{var y,P;w();const S=(y=r.content)==null?void 0:y.getBoundingClientRect();if(S){const x=(P=r.content)==null?void 0:P.dataset.side,E=x==="right",_=E?-5:5,T=S[E?"left":"right"],I=S[E?"right":"left"];l.onPointerGraceIntentChange({area:[{x:m.clientX+_,y:m.clientY},{x:T,y:S.top},{x:I,y:S.top},{x:I,y:S.bottom},{x:T,y:S.bottom}],side:x}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(m),m.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:Se(e.onKeyDown,m=>{var y;const S=l.searchRef.current!=="";e.disabled||S&&m.key===" "||I_[i.dir].includes(m.key)&&(r.onOpenChange(!0),(y=r.content)==null||y.focus(),m.preventDefault())})})})});Dy.displayName=ei;var Oy="MenuSubContent",My=p.forwardRef((e,n)=>{const r=gy(Bt,e.__scopeMenu),{forceMount:i=r.forceMount,...a}=e,l=Er(Bt,e.__scopeMenu),c=hi(Bt,e.__scopeMenu),d=Iy(Oy,e.__scopeMenu),g=p.useRef(null),v=Ye(n,g);return R.jsx(ui.Provider,{scope:e.__scopeMenu,children:R.jsx(Dn,{present:i||l.open,children:R.jsx(ui.Slot,{scope:e.__scopeMenu,children:R.jsx(wd,{id:d.contentId,"aria-labelledby":d.triggerId,...a,ref:v,align:"start",side:c.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:w=>{var m;c.isUsingKeyboardRef.current&&((m=g.current)==null||m.focus()),w.preventDefault()},onCloseAutoFocus:w=>w.preventDefault(),onFocusOutside:Se(e.onFocusOutside,w=>{w.target!==d.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:Se(e.onEscapeKeyDown,w=>{c.onClose(),w.preventDefault()}),onKeyDown:Se(e.onKeyDown,w=>{var y;const m=w.currentTarget.contains(w.target),S=D_[c.dir].includes(w.key);m&&S&&(l.onOpenChange(!1),(y=d.trigger)==null||y.focus(),w.preventDefault())})})})})})});My.displayName=Oy;function ky(e){return e?"open":"closed"}function tl(e){return e==="indeterminate"}function Cd(e){return tl(e)?"indeterminate":e?"checked":"unchecked"}function q_(e){const n=document.activeElement;for(const r of e)if(r===n||(r.focus(),document.activeElement!==n))return}function Y_(e,n){return e.map((r,i)=>e[(n+i)%e.length])}function Q_(e,n,r){const a=n.length>1&&Array.from(n).every(v=>v===n[0])?n[0]:n,l=r?e.indexOf(r):-1;let c=Y_(e,Math.max(l,0));a.length===1&&(c=c.filter(v=>v!==r));const g=c.find(v=>v.toLowerCase().startsWith(a.toLowerCase()));return g!==r?g:void 0}function Z_(e,n){const{x:r,y:i}=e;let a=!1;for(let l=0,c=n.length-1;l<n.length;c=l++){const d=n[l],g=n[c],v=d.x,w=d.y,m=g.x,S=g.y;w>i!=S>i&&r<(m-v)*(i-w)/(S-w)+v&&(a=!a)}return a}function J_(e,n){if(!n)return!1;const r={x:e.clientX,y:e.clientY};return Z_(r,n)}function ci(e){return n=>n.pointerType==="mouse"?e(n):void 0}var $y=py,Ny=md,Ay=hy,by=my,Ly=Sd,Fy=vy,jy=Sl,Vy=wy,zy=xy,Hy=Ey,Uy=Py,By=_y,Wy=Ty,Gy=Dy,Ky=My,Ed="ContextMenu",[eT,kO]=tr(Ed,[yl]),yt=yl(),[tT,Xy]=eT(Ed),qy=e=>{const{__scopeContextMenu:n,children:r,onOpenChange:i,dir:a,modal:l=!0}=e,[c,d]=p.useState(!1),g=yt(n),v=Tt(i),w=p.useCallback(m=>{d(m),v(m)},[v]);return R.jsx(tT,{scope:n,open:c,onOpenChange:w,modal:l,children:R.jsx($y,{...g,dir:a,open:c,onOpenChange:w,modal:l,children:r})})};qy.displayName=Ed;var Yy="ContextMenuTrigger",Qy=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,disabled:i=!1,...a}=e,l=Xy(Yy,r),c=yt(r),d=p.useRef({x:0,y:0}),g=p.useRef({getBoundingClientRect:()=>DOMRect.fromRect({width:0,height:0,...d.current})}),v=p.useRef(0),w=p.useCallback(()=>window.clearTimeout(v.current),[]),m=S=>{d.current={x:S.clientX,y:S.clientY},l.onOpenChange(!0)};return p.useEffect(()=>w,[w]),p.useEffect(()=>void(i&&w()),[i,w]),R.jsxs(R.Fragment,{children:[R.jsx(Ny,{...c,virtualRef:g}),R.jsx($e.span,{"data-state":l.open?"open":"closed","data-disabled":i?"":void 0,...a,ref:n,style:{WebkitTouchCallout:"none",...e.style},onContextMenu:i?e.onContextMenu:Se(e.onContextMenu,S=>{w(),m(S),S.preventDefault()}),onPointerDown:i?e.onPointerDown:Se(e.onPointerDown,Hs(S=>{w(),v.current=window.setTimeout(()=>m(S),700)})),onPointerMove:i?e.onPointerMove:Se(e.onPointerMove,Hs(w)),onPointerCancel:i?e.onPointerCancel:Se(e.onPointerCancel,Hs(w)),onPointerUp:i?e.onPointerUp:Se(e.onPointerUp,Hs(w))})]})});Qy.displayName=Yy;var nT="ContextMenuPortal",Zy=e=>{const{__scopeContextMenu:n,...r}=e,i=yt(n);return R.jsx(Ay,{...i,...r})};Zy.displayName=nT;var Jy="ContextMenuContent",ew=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=Xy(Jy,r),l=yt(r),c=p.useRef(!1);return R.jsx(by,{...l,...i,ref:n,side:"right",sideOffset:2,align:"start",onCloseAutoFocus:d=>{var g;(g=e.onCloseAutoFocus)==null||g.call(e,d),!d.defaultPrevented&&c.current&&d.preventDefault(),c.current=!1},onInteractOutside:d=>{var g;(g=e.onInteractOutside)==null||g.call(e,d),!d.defaultPrevented&&!a.modal&&(c.current=!0)},style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ew.displayName=Jy;var rT="ContextMenuGroup",oT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Ly,{...a,...i,ref:n})});oT.displayName=rT;var iT="ContextMenuLabel",sT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Fy,{...a,...i,ref:n})});sT.displayName=iT;var lT="ContextMenuItem",aT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(jy,{...a,...i,ref:n})});aT.displayName=lT;var uT="ContextMenuCheckboxItem",cT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Vy,{...a,...i,ref:n})});cT.displayName=uT;var dT="ContextMenuRadioGroup",fT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(zy,{...a,...i,ref:n})});fT.displayName=dT;var pT="ContextMenuRadioItem",gT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Hy,{...a,...i,ref:n})});gT.displayName=pT;var hT="ContextMenuItemIndicator",mT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Uy,{...a,...i,ref:n})});mT.displayName=hT;var vT="ContextMenuSeparator",yT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(By,{...a,...i,ref:n})});yT.displayName=vT;var wT="ContextMenuArrow",ST=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Wy,{...a,...i,ref:n})});ST.displayName=wT;var xT="ContextMenuSubTrigger",CT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Gy,{...a,...i,ref:n})});CT.displayName=xT;var ET="ContextMenuSubContent",RT=p.forwardRef((e,n)=>{const{__scopeContextMenu:r,...i}=e,a=yt(r);return R.jsx(Ky,{...a,...i,ref:n,style:{...e.style,"--radix-context-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-context-menu-content-available-width":"var(--radix-popper-available-width)","--radix-context-menu-content-available-height":"var(--radix-popper-available-height)","--radix-context-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-context-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});RT.displayName=ET;function Hs(e){return n=>n.pointerType!=="mouse"?e(n):void 0}var PT=qy,_T=Qy,TT=Zy,IT=ew;const tw=p.createContext({dragDropManager:void 0});function Ht(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Oh=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),Mh=function(){return Math.random().toString(36).substring(7).split("").join(".")},kh={INIT:"@@redux/INIT"+Mh(),REPLACE:"@@redux/REPLACE"+Mh()};function DT(e){if(typeof e!="object"||e===null)return!1;for(var n=e;Object.getPrototypeOf(n)!==null;)n=Object.getPrototypeOf(n);return Object.getPrototypeOf(e)===n}function nw(e,n,r){var i;if(typeof n=="function"&&typeof r=="function"||typeof r=="function"&&typeof arguments[3]=="function")throw new Error(Ht(0));if(typeof n=="function"&&typeof r>"u"&&(r=n,n=void 0),typeof r<"u"){if(typeof r!="function")throw new Error(Ht(1));return r(nw)(e,n)}if(typeof e!="function")throw new Error(Ht(2));var a=e,l=n,c=[],d=c,g=!1;function v(){d===c&&(d=c.slice())}function w(){if(g)throw new Error(Ht(3));return l}function m(x){if(typeof x!="function")throw new Error(Ht(4));if(g)throw new Error(Ht(5));var E=!0;return v(),d.push(x),function(){if(E){if(g)throw new Error(Ht(6));E=!1,v();var T=d.indexOf(x);d.splice(T,1),c=null}}}function S(x){if(!DT(x))throw new Error(Ht(7));if(typeof x.type>"u")throw new Error(Ht(8));if(g)throw new Error(Ht(9));try{g=!0,l=a(l,x)}finally{g=!1}for(var E=c=d,_=0;_<E.length;_++){var T=E[_];T()}return x}function y(x){if(typeof x!="function")throw new Error(Ht(10));a=x,S({type:kh.REPLACE})}function P(){var x,E=m;return x={subscribe:function(T){if(typeof T!="object"||T===null)throw new Error(Ht(11));function I(){T.next&&T.next(w())}I();var $=E(I);return{unsubscribe:$}}},x[Oh]=function(){return this},x}return S({type:kh.INIT}),i={dispatch:S,subscribe:m,getState:w,replaceReducer:y},i[Oh]=P,i}function Te(e,n,...r){if(OT()&&n===void 0)throw new Error("invariant requires an error message argument");if(!e){let i;if(n===void 0)i=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let a=0;i=new Error(n.replace(/%s/g,function(){return r[a++]})),i.name="Invariant Violation"}throw i.framesToPop=1,i}}function OT(){return typeof process<"u"&&!0}function MT(e,n,r){return n.split(".").reduce((i,a)=>i&&i[a]?i[a]:r||null,e)}function kT(e,n){return e.filter(r=>r!==n)}function rw(e){return typeof e=="object"}function $T(e,n){const r=new Map,i=l=>{r.set(l,r.has(l)?r.get(l)+1:1)};e.forEach(i),n.forEach(i);const a=[];return r.forEach((l,c)=>{l===1&&a.push(c)}),a}function NT(e,n){return e.filter(r=>n.indexOf(r)>-1)}const Rd="dnd-core/INIT_COORDS",xl="dnd-core/BEGIN_DRAG",Pd="dnd-core/PUBLISH_DRAG_SOURCE",Cl="dnd-core/HOVER",El="dnd-core/DROP",Rl="dnd-core/END_DRAG";function $h(e,n){return{type:Rd,payload:{sourceClientOffset:n||null,clientOffset:e||null}}}const AT={type:Rd,payload:{clientOffset:null,sourceClientOffset:null}};function bT(e){return function(r=[],i={publishSource:!0}){const{publishSource:a=!0,clientOffset:l,getSourceClientOffset:c}=i,d=e.getMonitor(),g=e.getRegistry();e.dispatch($h(l)),LT(r,d,g);const v=VT(r,d);if(v==null){e.dispatch(AT);return}let w=null;if(l){if(!c)throw new Error("getSourceClientOffset must be defined");FT(c),w=c(v)}e.dispatch($h(l,w));const S=g.getSource(v).beginDrag(d,v);if(S==null)return;jT(S),g.pinSource(v);const y=g.getSourceType(v);return{type:xl,payload:{itemType:y,item:S,sourceId:v,clientOffset:l||null,sourceClientOffset:w||null,isSourcePublic:!!a}}}}function LT(e,n,r){Te(!n.isDragging(),"Cannot call beginDrag while dragging."),e.forEach(function(i){Te(r.getSource(i),"Expected sourceIds to be registered.")})}function FT(e){Te(typeof e=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function jT(e){Te(rw(e),"Item must be an object.")}function VT(e,n){let r=null;for(let i=e.length-1;i>=0;i--)if(n.canDragSource(e[i])){r=e[i];break}return r}function zT(e,n,r){return n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function HT(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{},i=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),i.forEach(function(a){zT(e,a,r[a])})}return e}function UT(e){return function(r={}){const i=e.getMonitor(),a=e.getRegistry();BT(i),KT(i).forEach((c,d)=>{const g=WT(c,d,a,i),v={type:El,payload:{dropResult:HT({},r,g)}};e.dispatch(v)})}}function BT(e){Te(e.isDragging(),"Cannot call drop while not dragging."),Te(!e.didDrop(),"Cannot call drop twice during one drag operation.")}function WT(e,n,r,i){const a=r.getTarget(e);let l=a?a.drop(i,e):void 0;return GT(l),typeof l>"u"&&(l=n===0?{}:i.getDropResult()),l}function GT(e){Te(typeof e>"u"||rw(e),"Drop result must either be an object or undefined.")}function KT(e){const n=e.getTargetIds().filter(e.canDropOnTarget,e);return n.reverse(),n}function XT(e){return function(){const r=e.getMonitor(),i=e.getRegistry();qT(r);const a=r.getSourceId();return a!=null&&(i.getSource(a,!0).endDrag(r,a),i.unpinSource()),{type:Rl}}}function qT(e){Te(e.isDragging(),"Cannot call endDrag while not dragging.")}function Ic(e,n){return n===null?e===null:Array.isArray(e)?e.some(r=>r===n):e===n}function YT(e){return function(r,{clientOffset:i}={}){QT(r);const a=r.slice(0),l=e.getMonitor(),c=e.getRegistry(),d=l.getItemType();return JT(a,c,d),ZT(a,l,c),eI(a,l,c),{type:Cl,payload:{targetIds:a,clientOffset:i||null}}}}function QT(e){Te(Array.isArray(e),"Expected targetIds to be an array.")}function ZT(e,n,r){Te(n.isDragging(),"Cannot call hover while not dragging."),Te(!n.didDrop(),"Cannot call hover after drop.");for(let i=0;i<e.length;i++){const a=e[i];Te(e.lastIndexOf(a)===i,"Expected targetIds to be unique in the passed array.");const l=r.getTarget(a);Te(l,"Expected targetIds to be registered.")}}function JT(e,n,r){for(let i=e.length-1;i>=0;i--){const a=e[i],l=n.getTargetType(a);Ic(l,r)||e.splice(i,1)}}function eI(e,n,r){e.forEach(function(i){r.getTarget(i).hover(n,i)})}function tI(e){return function(){if(e.getMonitor().isDragging())return{type:Pd}}}function nI(e){return{beginDrag:bT(e),publishDragSource:tI(e),hover:YT(e),drop:UT(e),endDrag:XT(e)}}class rI{receiveBackend(n){this.backend=n}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const n=this,{dispatch:r}=this.store;function i(l){return(...c)=>{const d=l.apply(n,c);typeof d<"u"&&r(d)}}const a=nI(this);return Object.keys(a).reduce((l,c)=>{const d=a[c];return l[c]=i(d),l},{})}dispatch(n){this.store.dispatch(n)}constructor(n,r){this.isSetUp=!1,this.handleRefCountChange=()=>{const i=this.store.getState().refCount>0;this.backend&&(i&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!i&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=n,this.monitor=r,n.subscribe(this.handleRefCountChange)}}function oI(e,n){return{x:e.x+n.x,y:e.y+n.y}}function ow(e,n){return{x:e.x-n.x,y:e.y-n.y}}function iI(e){const{clientOffset:n,initialClientOffset:r,initialSourceClientOffset:i}=e;return!n||!r||!i?null:ow(oI(n,i),r)}function sI(e){const{clientOffset:n,initialClientOffset:r}=e;return!n||!r?null:ow(n,r)}const oi=[],_d=[];oi.__IS_NONE__=!0;_d.__IS_ALL__=!0;function lI(e,n){return e===oi?!1:e===_d||typeof n>"u"?!0:NT(n,e).length>0}class aI{subscribeToStateChange(n,r={}){const{handlerIds:i}=r;Te(typeof n=="function","listener must be a function."),Te(typeof i>"u"||Array.isArray(i),"handlerIds, when specified, must be an array of strings.");let a=this.store.getState().stateId;const l=()=>{const c=this.store.getState(),d=c.stateId;try{d===a||d===a+1&&!lI(c.dirtyHandlerIds,i)||n()}finally{a=d}};return this.store.subscribe(l)}subscribeToOffsetChange(n){Te(typeof n=="function","listener must be a function.");let r=this.store.getState().dragOffset;const i=()=>{const a=this.store.getState().dragOffset;a!==r&&(r=a,n())};return this.store.subscribe(i)}canDragSource(n){if(!n)return!1;const r=this.registry.getSource(n);return Te(r,`Expected to find a valid source. sourceId=${n}`),this.isDragging()?!1:r.canDrag(this,n)}canDropOnTarget(n){if(!n)return!1;const r=this.registry.getTarget(n);if(Te(r,`Expected to find a valid target. targetId=${n}`),!this.isDragging()||this.didDrop())return!1;const i=this.registry.getTargetType(n),a=this.getItemType();return Ic(i,a)&&r.canDrop(this,n)}isDragging(){return!!this.getItemType()}isDraggingSource(n){if(!n)return!1;const r=this.registry.getSource(n,!0);if(Te(r,`Expected to find a valid source. sourceId=${n}`),!this.isDragging()||!this.isSourcePublic())return!1;const i=this.registry.getSourceType(n),a=this.getItemType();return i!==a?!1:r.isDragging(this,n)}isOverTarget(n,r={shallow:!1}){if(!n)return!1;const{shallow:i}=r;if(!this.isDragging())return!1;const a=this.registry.getTargetType(n),l=this.getItemType();if(l&&!Ic(a,l))return!1;const c=this.getTargetIds();if(!c.length)return!1;const d=c.indexOf(n);return i?d===c.length-1:d>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return iI(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return sI(this.store.getState().dragOffset)}constructor(n,r){this.store=n,this.registry=r}}const Nh=typeof global<"u"?global:self,iw=Nh.MutationObserver||Nh.WebKitMutationObserver;function sw(e){return function(){const r=setTimeout(a,0),i=setInterval(a,50);function a(){clearTimeout(r),clearInterval(i),e()}}}function uI(e){let n=1;const r=new iw(e),i=document.createTextNode("");return r.observe(i,{characterData:!0}),function(){n=-n,i.data=n}}const cI=typeof iw=="function"?uI:sw;class dI{enqueueTask(n){const{queue:r,requestFlush:i}=this;r.length||(i(),this.flushing=!0),r[r.length]=n}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:n}=this;for(;this.index<n.length;){const r=this.index;if(this.index++,n[r].call(),this.index>this.capacity){for(let i=0,a=n.length-this.index;i<a;i++)n[i]=n[i+this.index];n.length-=this.index,this.index=0}}n.length=0,this.index=0,this.flushing=!1},this.registerPendingError=n=>{this.pendingErrors.push(n),this.requestErrorThrow()},this.requestFlush=cI(this.flush),this.requestErrorThrow=sw(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class fI{call(){try{this.task&&this.task()}catch(n){this.onError(n)}finally{this.task=null,this.release(this)}}constructor(n,r){this.onError=n,this.release=r,this.task=null}}class pI{create(n){const r=this.freeTasks,i=r.length?r.pop():new fI(this.onError,a=>r[r.length]=a);return i.task=n,i}constructor(n){this.onError=n,this.freeTasks=[]}}const lw=new dI,gI=new pI(lw.registerPendingError);function hI(e){lw.enqueueTask(gI.create(e))}const Td="dnd-core/ADD_SOURCE",Id="dnd-core/ADD_TARGET",Dd="dnd-core/REMOVE_SOURCE",Pl="dnd-core/REMOVE_TARGET";function mI(e){return{type:Td,payload:{sourceId:e}}}function vI(e){return{type:Id,payload:{targetId:e}}}function yI(e){return{type:Dd,payload:{sourceId:e}}}function wI(e){return{type:Pl,payload:{targetId:e}}}function SI(e){Te(typeof e.canDrag=="function","Expected canDrag to be a function."),Te(typeof e.beginDrag=="function","Expected beginDrag to be a function."),Te(typeof e.endDrag=="function","Expected endDrag to be a function.")}function xI(e){Te(typeof e.canDrop=="function","Expected canDrop to be a function."),Te(typeof e.hover=="function","Expected hover to be a function."),Te(typeof e.drop=="function","Expected beginDrag to be a function.")}function Dc(e,n){if(n&&Array.isArray(e)){e.forEach(r=>Dc(r,!1));return}Te(typeof e=="string"||typeof e=="symbol",n?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var Ut;(function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"})(Ut||(Ut={}));let CI=0;function EI(){return CI++}function RI(e){const n=EI().toString();switch(e){case Ut.SOURCE:return`S${n}`;case Ut.TARGET:return`T${n}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}function Ah(e){switch(e[0]){case"S":return Ut.SOURCE;case"T":return Ut.TARGET;default:throw new Error(`Cannot parse handler ID: ${e}`)}}function bh(e,n){const r=e.entries();let i=!1;do{const{done:a,value:[,l]}=r.next();if(l===n)return!0;i=!!a}while(!i);return!1}class PI{addSource(n,r){Dc(n),SI(r);const i=this.addHandler(Ut.SOURCE,n,r);return this.store.dispatch(mI(i)),i}addTarget(n,r){Dc(n,!0),xI(r);const i=this.addHandler(Ut.TARGET,n,r);return this.store.dispatch(vI(i)),i}containsHandler(n){return bh(this.dragSources,n)||bh(this.dropTargets,n)}getSource(n,r=!1){return Te(this.isSourceId(n),"Expected a valid source ID."),r&&n===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(n)}getTarget(n){return Te(this.isTargetId(n),"Expected a valid target ID."),this.dropTargets.get(n)}getSourceType(n){return Te(this.isSourceId(n),"Expected a valid source ID."),this.types.get(n)}getTargetType(n){return Te(this.isTargetId(n),"Expected a valid target ID."),this.types.get(n)}isSourceId(n){return Ah(n)===Ut.SOURCE}isTargetId(n){return Ah(n)===Ut.TARGET}removeSource(n){Te(this.getSource(n),"Expected an existing source."),this.store.dispatch(yI(n)),hI(()=>{this.dragSources.delete(n),this.types.delete(n)})}removeTarget(n){Te(this.getTarget(n),"Expected an existing target."),this.store.dispatch(wI(n)),this.dropTargets.delete(n),this.types.delete(n)}pinSource(n){const r=this.getSource(n);Te(r,"Expected an existing source."),this.pinnedSourceId=n,this.pinnedSource=r}unpinSource(){Te(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(n,r,i){const a=RI(n);return this.types.set(a,r),n===Ut.SOURCE?this.dragSources.set(a,i):n===Ut.TARGET&&this.dropTargets.set(a,i),a}constructor(n){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=n}}const _I=(e,n)=>e===n;function TI(e,n){return!e&&!n?!0:!e||!n?!1:e.x===n.x&&e.y===n.y}function II(e,n,r=_I){if(e.length!==n.length)return!1;for(let i=0;i<e.length;++i)if(!r(e[i],n[i]))return!1;return!0}function DI(e=oi,n){switch(n.type){case Cl:break;case Td:case Id:case Pl:case Dd:return oi;case xl:case Pd:case Rl:case El:default:return _d}const{targetIds:r=[],prevTargetIds:i=[]}=n.payload,a=$T(r,i);if(!(a.length>0||!II(r,i)))return oi;const c=i[i.length-1],d=r[r.length-1];return c!==d&&(c&&a.push(c),d&&a.push(d)),a}function OI(e,n,r){return n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function MI(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{},i=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),i.forEach(function(a){OI(e,a,r[a])})}return e}const Lh={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function kI(e=Lh,n){const{payload:r}=n;switch(n.type){case Rd:case xl:return{initialSourceClientOffset:r.sourceClientOffset,initialClientOffset:r.clientOffset,clientOffset:r.clientOffset};case Cl:return TI(e.clientOffset,r.clientOffset)?e:MI({},e,{clientOffset:r.clientOffset});case Rl:case El:return Lh;default:return e}}function $I(e,n,r){return n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function eo(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{},i=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),i.forEach(function(a){$I(e,a,r[a])})}return e}const NI={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function AI(e=NI,n){const{payload:r}=n;switch(n.type){case xl:return eo({},e,{itemType:r.itemType,item:r.item,sourceId:r.sourceId,isSourcePublic:r.isSourcePublic,dropResult:null,didDrop:!1});case Pd:return eo({},e,{isSourcePublic:!0});case Cl:return eo({},e,{targetIds:r.targetIds});case Pl:return e.targetIds.indexOf(r.targetId)===-1?e:eo({},e,{targetIds:kT(e.targetIds,r.targetId)});case El:return eo({},e,{dropResult:r.dropResult,didDrop:!0,targetIds:[]});case Rl:return eo({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}function bI(e=0,n){switch(n.type){case Td:case Id:return e+1;case Dd:case Pl:return e-1;default:return e}}function LI(e=0){return e+1}function FI(e,n,r){return n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function jI(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{},i=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),i.forEach(function(a){FI(e,a,r[a])})}return e}function VI(e={},n){return{dirtyHandlerIds:DI(e.dirtyHandlerIds,{type:n.type,payload:jI({},n.payload,{prevTargetIds:MT(e,"dragOperation.targetIds",[])})}),dragOffset:kI(e.dragOffset,n),refCount:bI(e.refCount,n),dragOperation:AI(e.dragOperation,n),stateId:LI(e.stateId)}}function zI(e,n=void 0,r={},i=!1){const a=HI(i),l=new aI(a,new PI(a)),c=new rI(a,l),d=e(c,n,r);return c.receiveBackend(d),c}function HI(e){const n=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return nw(VI,e&&n&&n({name:"dnd-core",instanceId:"dnd-core"}))}function UI(e,n){if(e==null)return{};var r=BI(e,n),i,a;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(a=0;a<l.length;a++)i=l[a],!(n.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(e,i)&&(r[i]=e[i])}return r}function BI(e,n){if(e==null)return{};var r={},i=Object.keys(e),a,l;for(l=0;l<i.length;l++)a=i[l],!(n.indexOf(a)>=0)&&(r[a]=e[a]);return r}let Fh=0;const Ws=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var WI=p.memo(function(n){var{children:r}=n,i=UI(n,["children"]);const[a,l]=GI(i);return p.useEffect(()=>{if(l){const c=aw();return++Fh,()=>{--Fh===0&&(c[Ws]=null)}}},[]),R.jsx(tw.Provider,{value:a,children:r})});function GI(e){if("manager"in e)return[{dragDropManager:e.manager},!1];const n=KI(e.backend,e.context,e.options,e.debugMode),r=!e.context;return[n,r]}function KI(e,n=aw(),r,i){const a=n;return a[Ws]||(a[Ws]={dragDropManager:zI(e,n,r,i)}),a[Ws]}function aw(){return typeof global<"u"?global:window}var Yu,jh;function XI(){return jh||(jh=1,Yu=function e(n,r){if(n===r)return!0;if(n&&r&&typeof n=="object"&&typeof r=="object"){if(n.constructor!==r.constructor)return!1;var i,a,l;if(Array.isArray(n)){if(i=n.length,i!=r.length)return!1;for(a=i;a--!==0;)if(!e(n[a],r[a]))return!1;return!0}if(n.constructor===RegExp)return n.source===r.source&&n.flags===r.flags;if(n.valueOf!==Object.prototype.valueOf)return n.valueOf()===r.valueOf();if(n.toString!==Object.prototype.toString)return n.toString()===r.toString();if(l=Object.keys(n),i=l.length,i!==Object.keys(r).length)return!1;for(a=i;a--!==0;)if(!Object.prototype.hasOwnProperty.call(r,l[a]))return!1;for(a=i;a--!==0;){var c=l[a];if(!e(n[c],r[c]))return!1}return!0}return n!==n&&r!==r}),Yu}var qI=XI();const YI=lo(qI),yr=typeof window<"u"?p.useLayoutEffect:p.useEffect;function QI(e,n,r){const[i,a]=p.useState(()=>n(e)),l=p.useCallback(()=>{const c=n(e);YI(i,c)||(a(c),r&&r())},[i,e,r]);return yr(l),[i,l]}function ZI(e,n,r){const[i,a]=QI(e,n,r);return yr(function(){const c=e.getHandlerId();if(c!=null)return e.subscribeToStateChange(a,{handlerIds:[c]})},[e,a]),i}function uw(e,n,r){return ZI(n,e||(()=>({})),()=>r.reconnect())}function cw(e,n){const r=[];return typeof e!="function"&&r.push(e),p.useMemo(()=>typeof e=="function"?e():e,r)}function JI(e){return p.useMemo(()=>e.hooks.dragSource(),[e])}function eD(e){return p.useMemo(()=>e.hooks.dragPreview(),[e])}let Qu=!1,Zu=!1;class tD{receiveHandlerId(n){this.sourceId=n}getHandlerId(){return this.sourceId}canDrag(){Te(!Qu,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Qu=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{Qu=!1}}isDragging(){if(!this.sourceId)return!1;Te(!Zu,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Zu=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{Zu=!1}}subscribeToStateChange(n,r){return this.internalMonitor.subscribeToStateChange(n,r)}isDraggingSource(n){return this.internalMonitor.isDraggingSource(n)}isOverTarget(n,r){return this.internalMonitor.isOverTarget(n,r)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(n){return this.internalMonitor.subscribeToOffsetChange(n)}canDragSource(n){return this.internalMonitor.canDragSource(n)}canDropOnTarget(n){return this.internalMonitor.canDropOnTarget(n)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(n){this.sourceId=null,this.internalMonitor=n.getMonitor()}}let Ju=!1;class nD{receiveHandlerId(n){this.targetId=n}getHandlerId(){return this.targetId}subscribeToStateChange(n,r){return this.internalMonitor.subscribeToStateChange(n,r)}canDrop(){if(!this.targetId)return!1;Te(!Ju,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Ju=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Ju=!1}}isOver(n){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,n):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(n){this.targetId=null,this.internalMonitor=n.getMonitor()}}function rD(e,n,r){const i=r.getRegistry(),a=i.addTarget(e,n);return[a,()=>i.removeTarget(a)]}function oD(e,n,r){const i=r.getRegistry(),a=i.addSource(e,n);return[a,()=>i.removeSource(a)]}function Oc(e,n,r,i){let a;if(a!==void 0)return!!a;if(e===n)return!0;if(typeof e!="object"||!e||typeof n!="object"||!n)return!1;const l=Object.keys(e),c=Object.keys(n);if(l.length!==c.length)return!1;const d=Object.prototype.hasOwnProperty.bind(n);for(let g=0;g<l.length;g++){const v=l[g];if(!d(v))return!1;const w=e[v],m=n[v];if(a=void 0,a===!1||a===void 0&&w!==m)return!1}return!0}function Mc(e){return e!==null&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function iD(e){if(typeof e.type=="string")return;const n=e.type.displayName||e.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${n} into a <div>, or turn it into a drag source or a drop target itself.`)}function sD(e){return(n=null,r=null)=>{if(!p.isValidElement(n)){const l=n;return e(l,r),l}const i=n;return iD(i),lD(i,r?l=>e(l,r):e)}}function dw(e){const n={};return Object.keys(e).forEach(r=>{const i=e[r];if(r.endsWith("Ref"))n[r]=e[r];else{const a=sD(i);n[r]=()=>a}}),n}function Vh(e,n){typeof e=="function"?e(n):e.current=n}function lD(e,n){const r=e.ref;return Te(typeof r!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),r?p.cloneElement(e,{ref:i=>{Vh(r,i),Vh(n,i)}}):p.cloneElement(e,{ref:n})}class aD{receiveHandlerId(n){this.handlerId!==n&&(this.handlerId=n,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(n){this.dragSourceOptionsInternal=n}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(n){this.dragPreviewOptionsInternal=n}reconnect(){const n=this.reconnectDragSource();this.reconnectDragPreview(n)}reconnectDragSource(){const n=this.dragSource,r=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return r&&this.disconnectDragSource(),this.handlerId?n?(r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=n,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,n,this.dragSourceOptions)),r):(this.lastConnectedDragSource=n,r):r}reconnectDragPreview(n=!1){const r=this.dragPreview,i=n||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(i&&this.disconnectDragPreview(),!!this.handlerId){if(!r){this.lastConnectedDragPreview=r;return}i&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=r,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,r,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!Oc(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!Oc(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(n){this.hooks=dw({dragSource:(r,i)=>{this.clearDragSource(),this.dragSourceOptions=i||null,Mc(r)?this.dragSourceRef=r:this.dragSourceNode=r,this.reconnectDragSource()},dragPreview:(r,i)=>{this.clearDragPreview(),this.dragPreviewOptions=i||null,Mc(r)?this.dragPreviewRef=r:this.dragPreviewNode=r,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=n}}class uD{get connectTarget(){return this.dropTarget}reconnect(){const n=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();n&&this.disconnectDropTarget();const r=this.dropTarget;if(this.handlerId){if(!r){this.lastConnectedDropTarget=r;return}n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=r,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,r,this.dropTargetOptions))}}receiveHandlerId(n){n!==this.handlerId&&(this.handlerId=n,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(n){this.dropTargetOptionsInternal=n}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!Oc(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(n){this.hooks=dw({dropTarget:(r,i)=>{this.clearDropTarget(),this.dropTargetOptions=i,Mc(r)?this.dropTargetRef=r:this.dropTargetNode=r,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=n}}function fo(){const{dragDropManager:e}=p.useContext(tw);return Te(e!=null,"Expected drag drop context"),e}function cD(e,n){const r=fo(),i=p.useMemo(()=>new aD(r.getBackend()),[r]);return yr(()=>(i.dragSourceOptions=e||null,i.reconnect(),()=>i.disconnectDragSource()),[i,e]),yr(()=>(i.dragPreviewOptions=n||null,i.reconnect(),()=>i.disconnectDragPreview()),[i,n]),i}function dD(){const e=fo();return p.useMemo(()=>new tD(e),[e])}class fD{beginDrag(){const n=this.spec,r=this.monitor;let i=null;return typeof n.item=="object"?i=n.item:typeof n.item=="function"?i=n.item(r):i={},i??null}canDrag(){const n=this.spec,r=this.monitor;return typeof n.canDrag=="boolean"?n.canDrag:typeof n.canDrag=="function"?n.canDrag(r):!0}isDragging(n,r){const i=this.spec,a=this.monitor,{isDragging:l}=i;return l?l(a):r===n.getSourceId()}endDrag(){const n=this.spec,r=this.monitor,i=this.connector,{end:a}=n;a&&a(r.getItem(),r),i.reconnect()}constructor(n,r,i){this.spec=n,this.monitor=r,this.connector=i}}function pD(e,n,r){const i=p.useMemo(()=>new fD(e,n,r),[n,r]);return p.useEffect(()=>{i.spec=e},[e]),i}function gD(e){return p.useMemo(()=>{const n=e.type;return Te(n!=null,"spec.type must be defined"),n},[e])}function hD(e,n,r){const i=fo(),a=pD(e,n,r),l=gD(e);yr(function(){if(l!=null){const[d,g]=oD(l,a,i);return n.receiveHandlerId(d),r.receiveHandlerId(d),g}},[i,n,r,a,l])}function mD(e,n){const r=cw(e);Te(!r.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const i=dD(),a=cD(r.options,r.previewOptions);return hD(r,i,a),[uw(r.collect,i,a),JI(a),eD(a)]}function vD(e){return p.useMemo(()=>e.hooks.dropTarget(),[e])}function yD(e){const n=fo(),r=p.useMemo(()=>new uD(n.getBackend()),[n]);return yr(()=>(r.dropTargetOptions=e||null,r.reconnect(),()=>r.disconnectDropTarget()),[e]),r}function wD(){const e=fo();return p.useMemo(()=>new nD(e),[e])}function SD(e){const{accept:n}=e;return p.useMemo(()=>(Te(e.accept!=null,"accept must be defined"),Array.isArray(n)?n:[n]),[n])}class xD{canDrop(){const n=this.spec,r=this.monitor;return n.canDrop?n.canDrop(r.getItem(),r):!0}hover(){const n=this.spec,r=this.monitor;n.hover&&n.hover(r.getItem(),r)}drop(){const n=this.spec,r=this.monitor;if(n.drop)return n.drop(r.getItem(),r)}constructor(n,r){this.spec=n,this.monitor=r}}function CD(e,n){const r=p.useMemo(()=>new xD(e,n),[n]);return p.useEffect(()=>{r.spec=e},[e]),r}function ED(e,n,r){const i=fo(),a=CD(e,n),l=SD(e);yr(function(){const[d,g]=rD(l,a,i);return n.receiveHandlerId(d),r.receiveHandlerId(d),g},[i,n,a,r,l.map(c=>c.toString()).join("|")])}function RD(e,n){const r=cw(e),i=wD(),a=yD(r.options);return ED(r,i,a),[uw(r.collect,i,a),vD(a)]}const fw=new RegExp("^([0-9]+)(\\s)([kKmMbBtT])$"),nl=new RegExp("^\\d{4}$"),PD=new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}|\\d{4}-\\d{2}-\\d{2}$");function _D({column:e,table:n,numberOfColumns:r}){function i(m){if(!m)return null;const S=new Date(m),y=S.toISOString().split("T")[0],P=S.toTimeString().split(" ")[0];return`${y} ${P}`}const a=n.getPreFilteredRowModel().flatRows.map(m=>m.original[e.id]),l=a.every(m=>typeof m=="string"||m===null),c=a.every(m=>typeof m=="number"||fw.test(m)||m===null||m===""),d=a.some(m=>typeof m=="string"&&m.includes(" ")),g=e.getFilterValue();let v="date";const w=a.every(m=>{const S=m==null?void 0:m.toString().replace(/[^0-9]/g,"").trim();return PD.test(m==null?void 0:m.toString())&&(v="datetime-local"),nl.test(m==null?void 0:m.toString())&&(v="number"),(S==null?void 0:S.length)>=4&&(Ks(e.id)||e.id.toLowerCase()==="index"&&!d)});return w&&v==="number"?R.jsxs("div",{className:"flex gap-2 h-6",children:[R.jsx("input",{type:v,value:(g==null?void 0:g[0])??"",onChange:m=>{e.setFilterValue(S=>[`${m.target.value}`,`${S==null?void 0:S[1]}`])},min:a.reduce((m,S)=>Math.min(m,parseInt(S,10)),1/0),max:a.reduce((m,S)=>Math.max(m,parseInt(S,10)),-1/0),placeholder:"Start year",className:"_input",title:"Start year"}),R.jsx("input",{type:v,value:(g==null?void 0:g[1])??"",onChange:m=>{e.setFilterValue(S=>[`${S==null?void 0:S[0]}`,`${m.target.value}`])},min:a.reduce((m,S)=>Math.min(m,parseInt(S,10)),1/0),max:a.reduce((m,S)=>Math.max(m,parseInt(S,10)),-1/0),placeholder:"End year",className:"_input",title:"End year"})]}):w&&v!=="number"?R.jsxs("div",{className:"flex gap-2 h-6",children:[R.jsx("input",{type:v,value:i(g==null?void 0:g[0])??"",onChange:m=>{const S=new Date(m.target.value).getTime();e.setFilterValue(y=>[S,y==null?void 0:y[1]])},placeholder:"Start date",className:"_input",title:"Start date"}),R.jsx("input",{type:v,value:i(g==null?void 0:g[1])??"",onChange:m=>{const S=new Date(m.target.value).getTime();e.setFilterValue(y=>[y==null?void 0:y[0],S])},placeholder:"End date",className:"_input",title:"End date"})]}):c?R.jsxs("div",{className:"flex gap-0.5 h-6",children:[R.jsx("input",{type:"number",value:(g==null?void 0:g[0])??"",onChange:m=>e.setFilterValue(S=>[m.target.value,S==null?void 0:S[1]]),placeholder:"Min",className:"_input p-0.5",title:"Min"}),R.jsx("input",{type:"number",value:(g==null?void 0:g[1])??"",onChange:m=>e.setFilterValue(S=>[S==null?void 0:S[0],m.target.value]),placeholder:"Max",className:"_input p-0.5",title:"Max"})]}):l?R.jsx("div",{className:"h-6",children:R.jsx("input",{type:"text",value:g??"",onChange:m=>e.setFilterValue(m.target.value),placeholder:"Search...",className:"_input",title:"Search"})}):R.jsx("div",{className:"h-6"})}const TD=(e,n,r)=>(r.splice(r.indexOf(n),0,r.splice(r.indexOf(e),1)[0]),[...r]),ID=({header:e,table:n,advanced:r,idx:i,lockFirstColumn:a,setLockFirstColumn:l})=>{const{getState:c,setColumnOrder:d}=n,{columnOrder:g}=c(),{column:v}=e,[,w]=RD({accept:"column",drop:x=>{const E=TD(x.id,v.id,g);d(E)}}),[{isDragging:m},S,y]=mD({collect:x=>({isDragging:x.isDragging()}),item:()=>v,type:"column"}),P=()=>R.jsx("div",{ref:y,className:"flex gap-1 flex-col",children:e.isPlaceholder?null:R.jsxs(R.Fragment,{children:[R.jsxs("div",{className:"font-bold uppercase text-grey-700 dark:text-white tracking-widest flex gap-2 whitespace-nowrap justify-between",children:[R.jsxs("div",{onClick:v.getToggleSortingHandler(),className:vt("flex gap-1",{"cursor-pointer select-none":v.getCanSort()}),children:[dc(v.columnDef.header,e.getContext()),v.getCanSort()&&R.jsxs("div",{className:"flex flex-col gap-0.5 items-center justify-center",children:[R.jsx("button",{className:vt({"text-[#669DCB]":v.getIsSorted()==="asc","text-grey-600":v.getIsSorted()!=="asc"}),children:R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"6",height:"4",fill:"none",viewBox:"0 0 11 5",children:R.jsx("path",{fill:"currentColor",d:"M10.333 5l-5-5-5 5"})})}),R.jsx("button",{className:vt({"text-[#669DCB]":e.column.getIsSorted()==="desc","text-grey-600":e.column.getIsSorted()!=="desc"}),children:R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"6",height:"4",fill:"none",viewBox:"0 0 11 5",children:R.jsx("path",{fill:"currentColor",d:"M.333 0l5 5 5-5"})})})]})]}),r&&v.id!=="select"&&R.jsx("button",{ref:S,className:"text-grey-600 hover:text-grey-800 dark:hover:text-white",children:R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"11",fill:"none",viewBox:"0 0 17 16",children:R.jsx("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",d:"M3.667 6l-2 2 2 2M6.333 3.333l2-2 2 2M10.333 12.667l-2 2-2-2M13 6l2 2-2 2M1.667 8H15M8.333 1.333v13.334"})})})]}),r&&v.getCanFilter()?R.jsx("div",{children:R.jsx(_D,{column:v,table:n,numberOfColumns:(g==null?void 0:g.length)??0})}):null]})});return R.jsxs("th",{className:vt("h-[70px] p-4 sticky",{"left-0 z-50 bg-white dark:bg-grey-900":i===0&&a}),colSpan:e.colSpan,style:{width:e.getSize()+10,opacity:m?.5:1},ref:w,children:[i===0?R.jsxs(PT,{children:[R.jsx(_T,{asChild:!0,children:P()}),R.jsx(TT,{children:R.jsx(IT,{className:"bg-white text-black dark:text-white dark:bg-grey-900 border border-grey-200 dark:border-grey-800 rounded-md shadow-lg p-2 z-50 text-xs",children:R.jsx("div",{className:"flex flex-col gap-2",children:R.jsxs("button",{onClick:()=>{l(!a)},className:"hover:bg-grey-300 dark:hover:bg-grey-800 rounded-md p-2",children:[a?"Unlock":"Lock"," first column"]})})})})]}):P(),R.jsx("button",{className:"resizer bg-grey-300/20 dark:hover:bg-white absolute top-0 right-0 w-0.5 h-full",onMouseDown:e.getResizeHandler(),onTouchStart:e.getResizeHandler()})]})};function DD({open:e,close:n}){const r=window.download_path||"~/OpenBBUserData/exports";return R.jsxs(Pm,{open:e,onOpenChange:n,children:[R.jsxs("div",{id:"loading",className:"saving",children:[R.jsx("div",{id:"loading_text",className:"loading_text"}),R.jsx("div",{id:"loader",className:"loader"})]}),R.jsx(_m,{onClick:n,className:"_modal-overlay"}),R.jsxs(Tm,{className:"_modal",children:[R.jsx(ic,{children:R.jsx(qs,{})}),R.jsx(ic,{className:"_modal-close",onClick:n,style:{float:"right",marginTop:20},children:R.jsx(qs,{className:"w-6 h-6"})}),R.jsx(Im,{className:"_modal-title",children:"Success"}),R.jsxs("div",{id:"popup_title",className:"popup_content",style:{padding:"0px 2px 2px 5px",marginTop:5},children:[R.jsx("div",{style:{display:"flex",flexDirection:"column",gap:0,fontSize:14},children:R.jsx("div",{children:R.jsxs("label",{htmlFor:"title_text",children:[R.jsx("b",{children:window.title})," has been downloaded to",R.jsx("br",{}),R.jsx("br",{}),R.jsx("a",{style:{color:"#FFDD00",marginTop:15},href:`${r}`,onClick:i=>{i.preventDefault(),window.pywry.open_file(r)},children:r})]})})}),R.jsx("div",{style:{float:"right",marginTop:20},children:R.jsx("button",{className:"_btn",style:{padding:"8px 16px",width:"100%"},onClick:n,children:"Close"})})]})]})]})}function OD({columns:e,data:n,type:r,setType:i,downloadFinished:a}){const l=()=>{switch(r){case"csv":OE("csv",e,n,a);break;case"png":ME("table",a);break}};return R.jsxs("div",{className:"flex gap-2 items-center",children:[R.jsx(to,{labelType:"row",value:r,onChange:c=>{i(c)},label:"Type",placeholder:"Select type",groups:[{label:"Type",items:$c.map(c=>({label:c,value:c}))}]}),R.jsx("button",{onClick:l,className:"_btn",children:"Export"})]})}var _l="DropdownMenu",[MD,$O]=tr(_l,[yl]),wt=yl(),[kD,pw]=MD(_l),gw=e=>{const{__scopeDropdownMenu:n,children:r,dir:i,open:a,defaultOpen:l,onOpenChange:c,modal:d=!0}=e,g=wt(n),v=p.useRef(null),[w,m]=ol({prop:a,defaultProp:l??!1,onChange:c,caller:_l});return R.jsx(kD,{scope:n,triggerId:no(),triggerRef:v,contentId:no(),open:w,onOpenChange:m,onOpenToggle:p.useCallback(()=>m(S=>!S),[m]),modal:d,children:R.jsx($y,{...g,open:w,onOpenChange:m,dir:i,modal:d,children:r})})};gw.displayName=_l;var hw="DropdownMenuTrigger",mw=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,disabled:i=!1,...a}=e,l=pw(hw,r),c=wt(r);return R.jsx(Ny,{asChild:!0,...c,children:R.jsx($e.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":i?"":void 0,disabled:i,...a,ref:rl(n,l.triggerRef),onPointerDown:Se(e.onPointerDown,d=>{!i&&d.button===0&&d.ctrlKey===!1&&(l.onOpenToggle(),l.open||d.preventDefault())}),onKeyDown:Se(e.onKeyDown,d=>{i||(["Enter"," "].includes(d.key)&&l.onOpenToggle(),d.key==="ArrowDown"&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(d.key)&&d.preventDefault())})})})});mw.displayName=hw;var $D="DropdownMenuPortal",vw=e=>{const{__scopeDropdownMenu:n,...r}=e,i=wt(n);return R.jsx(Ay,{...i,...r})};vw.displayName=$D;var yw="DropdownMenuContent",ww=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=pw(yw,r),l=wt(r),c=p.useRef(!1);return R.jsx(by,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:n,onCloseAutoFocus:Se(e.onCloseAutoFocus,d=>{var g;c.current||(g=a.triggerRef.current)==null||g.focus(),c.current=!1,d.preventDefault()}),onInteractOutside:Se(e.onInteractOutside,d=>{const g=d.detail.originalEvent,v=g.button===0&&g.ctrlKey===!0,w=g.button===2||v;(!a.modal||w)&&(c.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ww.displayName=yw;var ND="DropdownMenuGroup",Sw=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Ly,{...a,...i,ref:n})});Sw.displayName=ND;var AD="DropdownMenuLabel",xw=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Fy,{...a,...i,ref:n})});xw.displayName=AD;var bD="DropdownMenuItem",Cw=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(jy,{...a,...i,ref:n})});Cw.displayName=bD;var LD="DropdownMenuCheckboxItem",FD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Vy,{...a,...i,ref:n})});FD.displayName=LD;var jD="DropdownMenuRadioGroup",VD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(zy,{...a,...i,ref:n})});VD.displayName=jD;var zD="DropdownMenuRadioItem",HD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Hy,{...a,...i,ref:n})});HD.displayName=zD;var UD="DropdownMenuItemIndicator",BD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Uy,{...a,...i,ref:n})});BD.displayName=UD;var WD="DropdownMenuSeparator",GD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(By,{...a,...i,ref:n})});GD.displayName=WD;var KD="DropdownMenuArrow",XD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Wy,{...a,...i,ref:n})});XD.displayName=KD;var qD="DropdownMenuSubTrigger",YD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Gy,{...a,...i,ref:n})});YD.displayName=qD;var QD="DropdownMenuSubContent",ZD=p.forwardRef((e,n)=>{const{__scopeDropdownMenu:r,...i}=e,a=wt(r);return R.jsx(Ky,{...a,...i,ref:n,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ZD.displayName=QD;var JD=gw,zh=mw,eO=vw,tO=ww,nO=Sw,rO=xw,ec=Cw;function oO(e,n){p.useEffect(()=>{const r=i=>{!e.current||e.current.contains(i.target)||n(i)};return document.addEventListener("mousedown",r),document.addEventListener("touchstart",r),()=>{document.removeEventListener("mousedown",r),document.removeEventListener("touchstart",r)}},[e,n])}function Hh({label:e,table:n,onlyIconTrigger:r=!1}){const[i,a]=p.useState(!1),l=p.useRef(null);oO(l,()=>a(!1)),p.useEffect(()=>{const d=g=>{g.key==="Escape"&&a(!1)};return document.addEventListener("keydown",d),()=>document.removeEventListener("keydown",d)},[]);function c(){n.resetColumnFilters(),a(!1)}return R.jsxs(JD,{open:i,children:[r?R.jsx(zh,{title:"Filter columns",onClick:()=>a(!i),children:R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-7 h-7",children:R.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z"})})}):R.jsxs(nO,{className:"flex flex-row items-center gap-2",children:[R.jsx(rO,{className:"whitespace-nowrap",children:e}),R.jsxs(zh,{onClick:()=>a(!i),className:"bg-white text-black dark:bg-grey-900 dark:text-white whitespace-nowrap h-[36px] border-[1.5px] border-grey-700 rounded p-3 inline-flex items-center justify-center leading-none gap-[5px] shadow-[0_2px_10px] shadow-black/10 focus:shadow-[0_0_0_2px] focus:shadow-black data-[placeholder]:text-white outline-none","aria-label":e,children:[R.jsx("span",{children:"Filter columns"}),R.jsx(Cc,{className:vt({"transform rotate-180 duration-200 transition":i})})]})]}),R.jsx(eO,{children:R.jsxs(tO,{sideOffset:10,ref:l,className:"z-50 bg-white/80 dark:bg-grey-900/80 backdrop-filter backdrop-blur flex flex-col gap-4 overflow-auto border-[1.5px] border-grey-700 rounded p-3 max-h-[500px]  text-black dark:text-white",children:[R.jsx(ec,{children:R.jsx("button",{className:"_btn w-full",onClick:c,children:"Clear Filters"})}),R.jsx(ec,{children:R.jsxs("label",{className:"flex items-center gap-2",children:[R.jsx("input",{type:"checkbox",checked:n.getIsAllColumnsVisible(),onChange:n.getToggleAllColumnsVisibilityHandler()}),"Toggle All"]})}),n.getAllLeafColumns().filter(d=>d.id!=="select").map(d=>R.jsx(ec,{children:R.jsxs("label",{className:"flex items-center gap-2",children:[R.jsx("input",{type:"checkbox",checked:d.getIsVisible(),onChange:d.getToggleVisibilityHandler()}),d.id]})},d.id))]})})]})}function Ew(e){return typeof e!="number"?typeof e=="string"&&e.includes("All")?e:kc:e<1?kc:e}function iO({table:e,currentPage:n,setCurrentPage:r}){const i=e.getFilteredRowModel().rows.length||0;return R.jsxs("div",{className:"hidden md:flex items-center gap-3",children:[R.jsx(to,{value:n,onChange:a=>{const l=Ew(a);r(l),l.toString().includes("All")?e.setPageSize(i):e.setPageSize(l)},labelType:"row",label:"Rows per page",placeholder:"Select rows per page",groups:[{label:"Rows per page",items:[10,20,30,40,50,`All (${i})`].map(a=>({label:`${a}`,value:a}))}]}),R.jsxs("span",{className:"flex items-center gap-1",children:[R.jsx("strong",{children:e.getState().pagination.pageIndex+1}),"of",R.jsx("strong",{children:e.getPageCount()})]}),R.jsxs("div",{className:"hidden mdl:block",children:[R.jsx("button",{className:vt("px-2",{"text-grey-400 dark:text-grey-700":!e.getCanPreviousPage(),"dark:text-white":e.getCanPreviousPage()}),onClick:()=>e.setPageIndex(0),disabled:!e.getCanPreviousPage(),children:"<<"}),R.jsx("button",{className:vt("px-2",{"text-grey-400 dark:text-grey-700":!e.getCanPreviousPage(),"dark:text-white":e.getCanPreviousPage()}),onClick:()=>e.previousPage(),disabled:!e.getCanPreviousPage(),children:"<"}),R.jsx("button",{className:vt("px-2",{"text-grey-400 dark:text-grey-700":!e.getCanNextPage(),"dark:text-white":e.getCanNextPage()}),onClick:()=>e.nextPage(),disabled:!e.getCanNextPage(),children:">"}),R.jsx("button",{className:vt("px-2",{"text-grey-400 dark:text-grey-700":!e.getCanNextPage(),"dark:text-white":e.getCanNextPage()}),onClick:()=>e.setPageIndex(e.getPageCount()-1),disabled:!e.getCanNextPage(),children:">>"})]})]})}const sO=new Date,lO=50,kc=30;function aO(e,n){try{const r=e.hasOwnProperty("index")?"index":e.hasOwnProperty("Index")?"Index":null,i=r?e[r]:null,a=e[n],l=typeof a,c=a==null?void 0:a.toString().replace(/[^0-9]/g,""),d=(c==null?void 0:c.length)>=4&&(Ks(n)||n.toLowerCase()==="index"||i&&typeof i=="string"&&(i.toLowerCase().includes("date")||i.toLowerCase().includes("day")||i.toLowerCase().includes("time")||i.toLowerCase().includes("timestamp")||i.toLowerCase().includes("year")||i.toLowerCase().includes("month")||i.toLowerCase().includes("week")||i.toLowerCase().includes("hour")||i.toLowerCase().includes("minute")));if(l==="string"&&a.startsWith("http")||!d)return(a==null?void 0:a.toString().length)??0;if(d&&!isNaN(new Date(a).getTime())&&!nl.test(a==null?void 0:a.toString())){if(typeof a=="string")return(a==null?void 0:a.toString().length)??0;try{const v=new Date(a);let w="";return v.getUTCHours()===0&&v.getUTCMinutes()===0&&v.getUTCSeconds()===0&&v.getMilliseconds()===0?w=v.toISOString().split("T")[0]:(w=v.toISOString(),w=`${w.split("T")[0]} ${w.split("T")[1].split(".")[0]}`),(w==null?void 0:w.toString().length)??0}catch{return(a==null?void 0:a.toString().length)??0}}return(a==null?void 0:a.toString().length)??0}catch{return 0}}const $c=["csv","png"];function uO({data:e,columns:n,title:r,initialTheme:i,cmd:a=""}){const[l,c]=Jo("exportType",$c[0]),[d,g]=p.useState(!1),[v,w]=dE(i),[m,S]=p.useState(v==="dark"),y=q=>{w(v),S(q)},[P,x]=Jo("rowsPerPage",kc,Ew),[E,_]=Jo("advanced",!1),[T,I]=Jo("colors",!1),[$,L]=p.useState([]),[B,N]=p.useState(""),[b,K]=Jo("fontSize","1"),[J,ne]=p.useState(!1),te=n.reduce((q,fe,pe)=>(q[fe]=pe<lO,q),{}),[Z,G]=p.useState(te),ae=(q,fe,pe)=>{const ce=Math.max(...q.map(Ne=>aO(Ne,fe)),pe!=null&&pe.length?(pe==null?void 0:pe.length)+8:0);return Math.min(200,ce*12)},W=p.useMemo(()=>[...n.map((q,fe)=>({accessorKey:q,accessorFn:pe=>{var rt,rn,st;const Pe=pe.hasOwnProperty("index")?"index":pe.hasOwnProperty("Index")?"Index":n[0],we=Pe?pe[Pe]:null,ce=pe[q],Ne=((st=(rn=(rt=ce==null?void 0:ce.toString())==null?void 0:rt.split("."))==null?void 0:rn[0])==null?void 0:st.replace(/[^0-9]/g,""))??"",nt=(Ne==null?void 0:Ne.length)>=4&&(Ks(q)||q.toLowerCase()==="index"||we&&typeof we=="string"&&(we.toLowerCase().includes("date")||we.toLowerCase().includes("time")||we.toLowerCase().includes("timestamp")||we.toLowerCase().includes("year")||we.toLowerCase().includes("month")||we.toLowerCase().includes("week")||we.toLowerCase().includes("hour")||we.toLowerCase().includes("minute")));return nt&&(ce==null?void 0:ce.length)===4&&nl.test(ce==null?void 0:ce.toString())?ce:nt?typeof ce=="number"?ce:new Date(ce).getTime():ce},id:q,header:q,size:ae(e,q,q),footer:q,cell:({row:pe})=>{var st,It,pn,Rr;const Pe=pe.original.hasOwnProperty("index")?"index":pe.original.hasOwnProperty("Index")?"Index":n[0],we=Pe?pe.original[Pe]:null,ce=pe.original[q],Ne=typeof ce,nt=((pn=(It=(st=ce==null?void 0:ce.toString())==null?void 0:st.split("."))==null?void 0:It[0])==null?void 0:pn.replace(/[^0-9]/g,""))??"",rt=(nt==null?void 0:nt.length)>=4&&(Ks(q)||q.toLowerCase()==="index"||we&&typeof we=="string"&&(we.toLowerCase().includes("date")||we.toLowerCase().includes("time")||we.toLowerCase().includes("timestamp")||we.toLowerCase().includes("year")));if(Ne==="string"&&ce.startsWith("http"))return R.jsx("a",{className:"_hyper-link",href:ce,target:"_blank",rel:"noreferrer",children:(ce==null?void 0:ce.length)>25?`${ce.substring(0,25)}...`:ce});if(rt&&(ce==null?void 0:ce.length)===4&&nl.test(ce==null?void 0:ce.toString()))return R.jsx("p",{children:ce});if(rt&&!isNaN(new Date(ce).getTime())){if(typeof ce=="string"){const Ue=ce.split("T")[0],Qe=(Rr=ce.split("T")[1])==null?void 0:Rr.split(".")[0];return Qe==="00:00:00"?R.jsx("p",{children:Ue}):R.jsxs("p",{children:[Ue," ",Qe]})}try{const Ue=new Date(ce);let Qe="";return Ue.getUTCHours()===0&&Ue.getUTCMinutes()===0&&Ue.getUTCSeconds()===0&&Ue.getMilliseconds()===0?Qe=Ue.toISOString().split("T")[0]:(Qe=Ue.toISOString(),Qe=`${Qe.split("T")[0]} ${Qe.split("T")[1].split(".")[0]}`),R.jsx("p",{children:Qe})}catch{return R.jsx("p",{children:ce})}}if(Ne==="number"||fw.test(ce==null?void 0:ce.toString())){let Ue=RE(ce,q);const Qe=Number(pc(ce));if(typeof we=="string"&&Wm(we)){Ue=Number(pc(ce));const mi=Ue<2?4:2;Ue=Ue.toLocaleString("en-US",{maximumFractionDigits:mi,minimumFractionDigits:2})}return R.jsx("p",{className:vt("whitespace-nowrap",{"text-black dark:text-white":!T,"text-[#16A34A]":Qe>0&&T,"text-[#F87171]":Qe<0&&T,"text-[#404040]":Qe===0&&T}),title:Bm(ce).toString()??"",children:Qe!==0?Qe>0?`${Ue}`:`${Ue}`:Ue})}else if(Ne==="string")return R.jsx("div",{dangerouslySetInnerHTML:{__html:cE(ce)}});return R.jsx("p",{children:ce})}}))],[E,T]),[Y,A]=p.useState(!1),[F,U]=p.useState(W.map(q=>q.id)),O=()=>U(n.map(q=>q.id)),z=p.useMemo(()=>{const q=F.map(pe=>pe),fe=W.map(pe=>pe.id);return!PE(q,fe)},[F,W]),ue=oE({data:e,columns:W,getCoreRowModel:KC(),getSortedRowModel:eE(),getFilteredRowModel:ZC(),getPaginationRowModel:JC(),columnResizeMode:"onChange",onColumnVisibilityChange:G,onColumnOrderChange:U,onSortingChange:L,onGlobalFilterChange:N,globalFilterFn:_E,state:{sorting:$,globalFilter:B,columnOrder:F,columnVisibility:Z},initialState:{pagination:{pageIndex:0,pageSize:typeof P=="string"?P.includes("All")?e==null?void 0:e.length:parseInt(P):P}}}),de=p.useRef(null),{rows:X}=ue.getRowModel(),se=ue.getVisibleFlatColumns(),[Q,oe]=p.useState(!1);return p.useEffect(()=>{d&&(g(!1),oe(!0))},[d]),R.jsxs(R.Fragment,{children:[R.jsx(JP,{toast:{id:"max-columns",title:"Max 12 columns are visible by default",description:"You can change this by clicking on advanced and then top right 'Filter' button",status:"info"},open:J,setOpen:ne}),R.jsx(DD,{open:Q,close:()=>oe(!1)}),R.jsxs("div",{ref:de,className:vt("overflow-x-hidden h-screen"),children:[R.jsxs("div",{className:"relative p-4",id:"table",children:[R.jsx("div",{className:"absolute -inset-0.5 bg-gradient-to-r rounded-md blur-md from-[#072e49]/30 via-[#0d345f]/30 to-[#0d3362]/30"}),R.jsxs("div",{className:"border border-grey-500/60 dark:border-grey-200/60 bg-white dark:bg-grey-900 rounded overflow-hidden relative z-20",children:[R.jsxs("div",{className:"_header relative gap-4 py-2 text-center text-xs flex items-center justify-between px-4 text-white",style:{fontSize:`${Number(b)*90}%`},children:[R.jsx("div",{className:"w-1/3",children:R.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"64",height:"40",fill:"none",viewBox:"0 0 64 40",children:R.jsx("path",{fill:"#fff",d:"M61.283 3.965H33.608v27.757h25.699V19.826H37.561v-3.965H63.26V3.965h-1.977zM39.538 23.792h15.815v3.965H37.561v-3.965h1.977zM59.306 9.913v1.983H37.561V7.931h21.745v1.982zM33.606 0h-3.954v3.965H33.606V0zM25.7 3.966H0V15.86h25.7v3.965H3.953v11.896h25.7V3.966h-3.955zm0 21.808v1.983H7.907v-3.965h17.791v1.982zm0-15.86v1.982H3.953V7.931h21.745v1.982zM37.039 35.693v2.952l-.246-.246-.245-.245-.245-.247-.245-.246-.246-.246-.245-.245-.245-.247-.247-.246-.245-.246-.245-.246-.245-.246-.246-.246h-.49v3.936h.49v-3.198l.246.246.245.246.245.246.245.246.246.246.246.246.245.247.246.245.245.246.245.247.245.246.246.245.245.246h.245v-3.936h-.49zM44.938 37.17h-.491v-1.477h-2.944v3.937h3.93v-2.46h-.495zm-2.944-.246v-.739h1.962v.984h-1.962v-.245zm2.944.984v1.23h-2.944V37.66h2.944v.247zM52.835 37.17h-.49v-1.477h-2.946v3.937h3.925v-2.46h-.489zm-2.944-.246v-.739h1.963v.984h-1.965l.002-.245zm2.944.984v1.23H49.89V37.66h2.946v.247zM29.174 35.693H25.739v3.936H29.663v-.491H26.229v-.984h2.943v-.493H26.229v-1.476h3.434v-.492h-.489zM13.37 35.693H9.934v3.937h3.925v-3.937h-.49zm0 .738v2.709h-2.945v-2.955h2.943l.001.246zM21.276 35.693h-3.435v3.937h.491v-1.476h3.434v-2.461h-.49zm0 .738v1.23h-2.944v-1.476h2.944v.246z"})})}),R.jsx("p",{className:"font-bold w-1/3 flex flex-col gap-0.5 items-center",children:r}),R.jsxs("p",{className:"w-1/3 text-right text-xs",children:[new Intl.DateTimeFormat("en-GB",{dateStyle:"full",timeStyle:"long"}).format(sO).replace(/:\d\d /," "),R.jsx("br",{}),R.jsx("span",{className:"text-grey-400",children:a})]})]}),R.jsx("div",{className:"overflow-auto max-h-[calc(100vh-170px)] smh:max-h-[calc(100vh-95px)]",children:R.jsxs("table",{className:"text-sm relative",children:[R.jsx("thead",{className:"sticky top-0 bg-white dark:bg-grey-900",style:{fontSize:`${Number(b)*100}%`},children:ue.getHeaderGroups().map((q,fe)=>R.jsx("tr",{children:q.headers.map((pe,Pe)=>R.jsx(ID,{setLockFirstColumn:A,lockFirstColumn:Y,idx:Pe,advanced:E,header:pe,table:ue},pe.id))},q.id))}),R.jsx("tbody",{children:ue.getRowModel().rows.map((q,fe)=>R.jsx("tr",{className:"!h-[64px] border-b border-grey-400",style:{fontSize:`${Number(b)*100}%`},children:q.getVisibleCells().map((pe,Pe)=>R.jsx("td",{className:vt("whitespace-normal p-4 text-black dark:text-white",{"bg-white dark:bg-grey-850":fe%2===0,"bg-grey-100 dark:bg-[#202020]":fe%2===1,"sticky left-0 z-10":Pe===0&&Y}),style:{width:pe.column.getSize()},children:dc(pe.column.columnDef.cell,pe.getContext())},pe.id))},q.id))}),(X==null?void 0:X.length)>30&&(se==null?void 0:se.length)>4&&R.jsx("tfoot",{children:ue.getFooterGroups().map(q=>R.jsx("tr",{children:q.headers.map(fe=>R.jsx("th",{colSpan:fe.colSpan,className:"text-grey-500 bg-grey-100 dark:bg-grey-850 font-normal text-left text-sm h-10 p-4",style:{width:fe.getSize(),fontSize:`${Number(b)*100}%`},children:fe.isPlaceholder?null:dc(fe.column.columnDef.footer,fe.getContext())},fe.id))},q.id))})]})})]})]}),R.jsxs("div",{className:"smh:hidden flex max-h-[68px] overflow-x-auto bg-white/70 dark:bg-grey-900/70 backdrop-filter backdrop-blur z-20 bottom-0 left-0 w-full gap-10 justify-between py-4 px-4 text-sm",children:[R.jsxs("div",{className:"flex items-center gap-10",children:[R.jsxs(Pm,{children:[R.jsx(sC,{className:"_btn",children:"Settings"}),R.jsxs(lC,{children:[R.jsx(_m,{className:"_modal-overlay"}),R.jsxs(Tm,{className:"_modal",children:[R.jsx(ic,{className:"absolute top-[40px] right-[46px] text-grey-200 hover:text-white rounded-[4px] focus:outline focus:outline-2 focus:outline-grey-500",children:R.jsx(qs,{className:"w-6 h-6"})}),R.jsx(Im,{className:"uppercase font-bold tracking-widest",children:"Settings"}),R.jsxs("div",{className:"grid grid-cols-2 gap-2 mt-10 text-sm",children:[z&&R.jsx("button",{onClick:()=>O(),className:"_btn h-9",children:"Reset Order"}),R.jsx(to,{labelType:"row",value:m?"light":"dark",onChange:q=>{y(q!=="dark")},label:"Theme",placeholder:"Select theme",groups:[{label:"Theme",items:[{label:"Dark",value:"dark"},{label:"Light",value:"light"}]}]}),R.jsx(to,{labelType:"row",value:l,onChange:q=>{c(q)},label:"Export type",placeholder:"Select export type",groups:[{label:"Export type",items:$c.map(q=>({label:q,value:q}))}]}),R.jsx(to,{labelType:"row",value:b,onChange:K,label:"Font size",placeholder:"Select font size",groups:[{label:"Font size",items:[{label:"50%",value:"0.5"},{label:"75%",value:"0.75"},{label:"100%",value:"1"},{label:"125%",value:"1.25"},{label:"150%",value:"1.5"},{label:"175%",value:"1.75"},{label:"200%",value:"2"}]}]}),R.jsx(Hh,{table:ue,label:"Filter"}),R.jsx("div",{className:"flex gap-2 items-center",children:R.jsx(to,{labelType:"row",value:E?"advanced":"simple",onChange:q=>{_(q==="advanced")},label:"Type",placeholder:"Select type",groups:[{label:"Type",items:[{label:"Simple",value:"simple"},{label:"Advanced",value:"advanced"}]}]})}),R.jsxs("div",{className:"flex gap-2 items-center",children:[R.jsx("label",{htmlFor:"colors",children:"Colors"}),R.jsx("input",{id:"colors",type:"checkbox",checked:T,onChange:()=>I(!T)})]})]})]})]})]}),R.jsx(Hh,{onlyIconTrigger:!0,table:ue,label:""})]}),R.jsx(iO,{currentPage:P,setCurrentPage:x,table:ue}),R.jsx(OD,{setType:c,type:l,columns:n,data:e,downloadFinished:g})]})]})]})}function Rw(e){let n=null;return()=>(n==null&&(n=e()),n)}function cO(e,n){return e.filter(r=>r!==n)}function dO(e,n){const r=new Set,i=l=>r.add(l);e.forEach(i),n.forEach(i);const a=[];return r.forEach(l=>a.push(l)),a}class fO{enter(n){const r=this.entered.length,i=a=>this.isNodeInDocument(a)&&(!a.contains||a.contains(n));return this.entered=dO(this.entered.filter(i),[n]),r===0&&this.entered.length>0}leave(n){const r=this.entered.length;return this.entered=cO(this.entered.filter(this.isNodeInDocument),n),r>0&&this.entered.length===0}reset(){this.entered=[]}constructor(n){this.entered=[],this.isNodeInDocument=n}}class pO{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(n=>{Object.defineProperty(this.item,n,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${n}" until the drop event.`),null}})})}loadDataTransfer(n){if(n){const r={};Object.keys(this.config.exposeProperties).forEach(i=>{const a=this.config.exposeProperties[i];a!=null&&(r[i]={value:a(n,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,r)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(n,r){return r===n.getSourceId()}endDrag(){}constructor(n){this.config=n,this.item={},this.initializeExposedProperties()}}const Pw="__NATIVE_FILE__",_w="__NATIVE_URL__",Tw="__NATIVE_TEXT__",Iw="__NATIVE_HTML__",Uh=Object.freeze(Object.defineProperty({__proto__:null,FILE:Pw,HTML:Iw,TEXT:Tw,URL:_w},Symbol.toStringTag,{value:"Module"}));function tc(e,n,r){const i=n.reduce((a,l)=>a||e.getData(l),"");return i??r}const Nc={[Pw]:{exposeProperties:{files:e=>Array.prototype.slice.call(e.files),items:e=>e.items,dataTransfer:e=>e},matchesTypes:["Files"]},[Iw]:{exposeProperties:{html:(e,n)=>tc(e,n,""),dataTransfer:e=>e},matchesTypes:["Html","text/html"]},[_w]:{exposeProperties:{urls:(e,n)=>tc(e,n,"").split(`
`),dataTransfer:e=>e},matchesTypes:["Url","text/uri-list"]},[Tw]:{exposeProperties:{text:(e,n)=>tc(e,n,""),dataTransfer:e=>e},matchesTypes:["Text","text/plain"]}};function gO(e,n){const r=Nc[e];if(!r)throw new Error(`native type ${e} has no configuration`);const i=new pO(r);return i.loadDataTransfer(n),i}function nc(e){if(!e)return null;const n=Array.prototype.slice.call(e.types||[]);return Object.keys(Nc).filter(r=>{const i=Nc[r];return i!=null&&i.matchesTypes?i.matchesTypes.some(a=>n.indexOf(a)>-1):!1})[0]||null}const hO=Rw(()=>/firefox/i.test(navigator.userAgent)),Dw=Rw(()=>!!window.safari);class Bh{interpolate(n){const{xs:r,ys:i,c1s:a,c2s:l,c3s:c}=this;let d=r.length-1;if(n===r[d])return i[d];let g=0,v=c.length-1,w;for(;g<=v;){w=Math.floor(.5*(g+v));const y=r[w];if(y<n)g=w+1;else if(y>n)v=w-1;else return i[w]}d=Math.max(0,v);const m=n-r[d],S=m*m;return i[d]+a[d]*m+l[d]*S+c[d]*m*S}constructor(n,r){const{length:i}=n,a=[];for(let y=0;y<i;y++)a.push(y);a.sort((y,P)=>n[y]<n[P]?-1:1);const l=[],c=[];let d,g;for(let y=0;y<i-1;y++)d=n[y+1]-n[y],g=r[y+1]-r[y],l.push(d),c.push(g/d);const v=[c[0]];for(let y=0;y<l.length-1;y++){const P=c[y],x=c[y+1];if(P*x<=0)v.push(0);else{d=l[y];const E=l[y+1],_=d+E;v.push(3*_/((_+E)/P+(_+d)/x))}}v.push(c[c.length-1]);const w=[],m=[];let S;for(let y=0;y<v.length-1;y++){S=c[y];const P=v[y],x=1/l[y],E=P+v[y+1]-S-S;w.push((S-P-E)*x),m.push(E*x*x)}this.xs=n,this.ys=r,this.c1s=v,this.c2s=w,this.c3s=m}}const mO=1;function Ow(e){const n=e.nodeType===mO?e:e.parentElement;if(!n)return null;const{top:r,left:i}=n.getBoundingClientRect();return{x:i,y:r}}function Us(e){return{x:e.clientX,y:e.clientY}}function vO(e){var n;return e.nodeName==="IMG"&&(hO()||!(!((n=document.documentElement)===null||n===void 0)&&n.contains(e)))}function yO(e,n,r,i){let a=e?n.width:r,l=e?n.height:i;return Dw()&&e&&(l/=window.devicePixelRatio,a/=window.devicePixelRatio),{dragPreviewWidth:a,dragPreviewHeight:l}}function wO(e,n,r,i,a){const l=vO(n),d=Ow(l?e:n),g={x:r.x-d.x,y:r.y-d.y},{offsetWidth:v,offsetHeight:w}=e,{anchorX:m,anchorY:S}=i,{dragPreviewWidth:y,dragPreviewHeight:P}=yO(l,n,v,w),x=()=>{let B=new Bh([0,.5,1],[g.y,g.y/w*P,g.y+P-w]).interpolate(S);return Dw()&&l&&(B+=(window.devicePixelRatio-1)*P),B},E=()=>new Bh([0,.5,1],[g.x,g.x/v*y,g.x+y-v]).interpolate(m),{offsetX:_,offsetY:T}=a,I=_===0||_,$=T===0||T;return{x:I?_:E(),y:$?T:x()}}class SO{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var n;return!((n=this.globalContext)===null||n===void 0)&&n.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var n;return((n=this.optionsArgs)===null||n===void 0?void 0:n.rootElement)||this.window}constructor(n,r){this.ownerDocument=null,this.globalContext=n,this.optionsArgs=r}}function xO(e,n,r){return n in e?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e}function Wh(e){for(var n=1;n<arguments.length;n++){var r=arguments[n]!=null?arguments[n]:{},i=Object.keys(r);typeof Object.getOwnPropertySymbols=="function"&&(i=i.concat(Object.getOwnPropertySymbols(r).filter(function(a){return Object.getOwnPropertyDescriptor(r,a).enumerable}))),i.forEach(function(a){xO(e,a,r[a])})}return e}class CO{profile(){var n,r;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((n=this.dragStartSourceIds)===null||n===void 0?void 0:n.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((r=this.dragOverTargetIds)===null||r===void 0?void 0:r.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const n=this.rootElement;if(n!==void 0){if(n.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");n.__isReactDndBackendSetUp=!0,this.addEventListeners(n)}}teardown(){const n=this.rootElement;if(n!==void 0&&(n.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var r;(r=this.window)===null||r===void 0||r.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(n,r,i){return this.sourcePreviewNodeOptions.set(n,i),this.sourcePreviewNodes.set(n,r),()=>{this.sourcePreviewNodes.delete(n),this.sourcePreviewNodeOptions.delete(n)}}connectDragSource(n,r,i){this.sourceNodes.set(n,r),this.sourceNodeOptions.set(n,i);const a=c=>this.handleDragStart(c,n),l=c=>this.handleSelectStart(c);return r.setAttribute("draggable","true"),r.addEventListener("dragstart",a),r.addEventListener("selectstart",l),()=>{this.sourceNodes.delete(n),this.sourceNodeOptions.delete(n),r.removeEventListener("dragstart",a),r.removeEventListener("selectstart",l),r.setAttribute("draggable","false")}}connectDropTarget(n,r){const i=c=>this.handleDragEnter(c,n),a=c=>this.handleDragOver(c,n),l=c=>this.handleDrop(c,n);return r.addEventListener("dragenter",i),r.addEventListener("dragover",a),r.addEventListener("drop",l),()=>{r.removeEventListener("dragenter",i),r.removeEventListener("dragover",a),r.removeEventListener("drop",l)}}addEventListeners(n){n.addEventListener&&(n.addEventListener("dragstart",this.handleTopDragStart),n.addEventListener("dragstart",this.handleTopDragStartCapture,!0),n.addEventListener("dragend",this.handleTopDragEndCapture,!0),n.addEventListener("dragenter",this.handleTopDragEnter),n.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),n.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),n.addEventListener("dragover",this.handleTopDragOver),n.addEventListener("dragover",this.handleTopDragOverCapture,!0),n.addEventListener("drop",this.handleTopDrop),n.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(n){n.removeEventListener&&(n.removeEventListener("dragstart",this.handleTopDragStart),n.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),n.removeEventListener("dragend",this.handleTopDragEndCapture,!0),n.removeEventListener("dragenter",this.handleTopDragEnter),n.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),n.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),n.removeEventListener("dragover",this.handleTopDragOver),n.removeEventListener("dragover",this.handleTopDragOverCapture,!0),n.removeEventListener("drop",this.handleTopDrop),n.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const n=this.monitor.getSourceId(),r=this.sourceNodeOptions.get(n);return Wh({dropEffect:this.altKeyPressed?"copy":"move"},r||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const n=this.monitor.getSourceId(),r=this.sourcePreviewNodeOptions.get(n);return Wh({anchorX:.5,anchorY:.5,captureDraggingState:!1},r||{})}isDraggingNativeItem(){const n=this.monitor.getItemType();return Object.keys(Uh).some(r=>Uh[r]===n)}beginDragNativeItem(n,r){this.clearCurrentDragSourceNode(),this.currentNativeSource=gO(n,r),this.currentNativeHandle=this.registry.addSource(n,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(n){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=n;const r=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var i;return(i=this.rootElement)===null||i===void 0?void 0:i.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},r)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var n;(n=this.window)===null||n===void 0||n.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(n,r){n.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(r))}handleDragEnter(n,r){this.dragEnterTargetIds.unshift(r)}handleDragOver(n,r){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(r)}handleDrop(n,r){this.dropTargetIds.unshift(r)}constructor(n,r,i){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=a=>{const l=this.sourceNodes.get(a);return l&&Ow(l)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=a=>!!(a&&this.document&&this.document.body&&this.document.body.contains(a)),this.endDragIfSourceWasRemovedFromDOM=()=>{const a=this.currentDragSourceNode;a==null||this.isNodeInDocument(a)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=a=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(a||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=a=>{if(a.defaultPrevented)return;const{dragStartSourceIds:l}=this;this.dragStartSourceIds=null;const c=Us(a);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(l||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:c});const{dataTransfer:d}=a,g=nc(d);if(this.monitor.isDragging()){if(d&&typeof d.setDragImage=="function"){const w=this.monitor.getSourceId(),m=this.sourceNodes.get(w),S=this.sourcePreviewNodes.get(w)||m;if(S){const{anchorX:y,anchorY:P,offsetX:x,offsetY:E}=this.getCurrentSourcePreviewNodeOptions(),I=wO(m,S,c,{anchorX:y,anchorY:P},{offsetX:x,offsetY:E});d.setDragImage(S,I.x,I.y)}}try{d==null||d.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(a.target);const{captureDraggingState:v}=this.getCurrentSourcePreviewNodeOptions();v?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(g)this.beginDragNativeItem(g);else{if(d&&!d.types&&(a.target&&!a.target.hasAttribute||!a.target.hasAttribute("draggable")))return;a.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=a=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var l;(l=this.currentNativeSource)===null||l===void 0||l.loadDataTransfer(a.dataTransfer)}if(!this.enterLeaveCounter.enter(a.target)||this.monitor.isDragging())return;const{dataTransfer:d}=a,g=nc(d);g&&this.beginDragNativeItem(g,d)},this.handleTopDragEnter=a=>{const{dragEnterTargetIds:l}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=a.altKey,l.length>0&&this.actions.hover(l,{clientOffset:Us(a)}),l.some(d=>this.monitor.canDropOnTarget(d))&&(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=a=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var l;(l=this.currentNativeSource)===null||l===void 0||l.loadDataTransfer(a.dataTransfer)}},this.handleTopDragOver=a=>{const{dragOverTargetIds:l}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect="none");return}this.altKeyPressed=a.altKey,this.lastClientOffset=Us(a),this.scheduleHover(l),(l||[]).some(d=>this.monitor.canDropOnTarget(d))?(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?a.preventDefault():(a.preventDefault(),a.dataTransfer&&(a.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=a=>{this.isDraggingNativeItem()&&a.preventDefault(),this.enterLeaveCounter.leave(a.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=a=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var l;a.preventDefault(),(l=this.currentNativeSource)===null||l===void 0||l.loadDataTransfer(a.dataTransfer)}else nc(a.dataTransfer)&&a.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=a=>{const{dropTargetIds:l}=this;this.dropTargetIds=[],this.actions.hover(l,{clientOffset:Us(a)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=a=>{const l=a.target;typeof l.dragDrop=="function"&&(l.tagName==="INPUT"||l.tagName==="SELECT"||l.tagName==="TEXTAREA"||l.isContentEditable||(a.preventDefault(),l.dragDrop()))},this.options=new SO(r,i),this.actions=n.getActions(),this.monitor=n.getMonitor(),this.registry=n.getRegistry(),this.enterLeaveCounter=new fO(this.isNodeInDocument)}}const EO=function(n,r,i){return new CO(n,r,i)};function RO(){const[e,n]=p.useState(null),[r,i]=p.useState("Interactive Table");p.useEffect(()=>{const c=setInterval(()=>{if(window.json_data){const d=JSON.parse(window.json_data);console.log(d),n(d),d.title&&typeof d.title=="string"&&i(d.title),clearInterval(c)}},100);return()=>clearInterval(c)},[]);const l=(c=>{var y;if(!c)return null;const d=(y=c.title)==null?void 0:y.replace(/<b>|<\/b>/g,"").replace(/ /g,"_"),g=new Date().toISOString().slice(0,10).replace(/-/g,""),v=new Date().toISOString().slice(11,19).replace(/:/g,"");window.title=`openbb_${d}_${g}_${v}`;const w=c.columns;c.index;const S=c.data.map((P,x)=>{const E={};return P.forEach((_,T)=>{E[w[T]]=_||(_===0?0:"")}),E});return{columns:w,data:S}})(e);return R.jsx("div",{className:"relative h-full bg-white dark:bg-black text-black dark:text-white",children:R.jsx(WI,{backend:EO,children:l&&R.jsx(uO,{title:r,data:l.data,columns:l.columns,initialTheme:e.theme&&typeof e.theme=="string"&&e.theme==="dark"?"dark":"light",cmd:(e==null?void 0:e.command_location)??""})})})}const PO=bS.createRoot(document.getElementById("root"));PO.render(R.jsx(qe.StrictMode,{children:R.jsx(RO,{})}));

</script>
    <style>
*,:before,:after{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x: 0;--tw-border-spacing-y: 0;--tw-translate-x: 0;--tw-translate-y: 0;--tw-rotate: 0;--tw-skew-x: 0;--tw-skew-y: 0;--tw-scale-x: 1;--tw-scale-y: 1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness: proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width: 0px;--tw-ring-offset-color: #fff;--tw-ring-color: rgb(59 130 246 / .5);--tw-ring-offset-shadow: 0 0 #0000;--tw-ring-shadow: 0 0 #0000;--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }*,:before,:after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}:before,:after{--tw-content: ""}html,:host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}body{margin:0;line-height:inherit}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}button,input,optgroup,select,textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dl,dd,h1,h2,h3,h4,h5,h6,hr,figure,p,pre{margin:0}fieldset{margin:0;padding:0}legend{padding:0}ol,ul,menu{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{opacity:1;color:#9ca3af}input::placeholder,textarea::placeholder{opacity:1;color:#9ca3af}button,[role=button]{cursor:pointer}:disabled{cursor:default}img,svg,video,canvas,audio,iframe,embed,object{display:block;vertical-align:middle}img,video{max-width:100%;height:auto}[hidden]:where(:not([hidden=until-found])){display:none}._input{height:25px;width:100%;border-radius:.25rem;border-width:1.5px;border-color:#c8c8c8;background-color:#f6f6f6;padding:.25rem .5rem;font-size:.75rem;line-height:1rem;font-weight:400;outline:2px solid transparent;outline-offset:2px}._input:focus{border-color:gray}._input:active{border-color:gray}._input:disabled{border-color:#5a5a5a}._input:is(.dark *){border-color:#474747;background-color:#070707;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));outline-width:1px}._input:hover:is(.dark *){--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}._input:focus:is(.dark *){--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1));--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}._input:active:is(.dark *){--tw-border-opacity: 1;border-color:rgb(255 255 255 / var(--tw-border-opacity, 1))}._btn{display:inline-flex;width:100%;align-items:center;justify-content:center;gap:.5rem;white-space:nowrap;border-radius:.25rem;padding:.5rem 1.5rem;text-align:center;text-decoration-line:none!important;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s;transition-timing-function:cubic-bezier(0,0,.2,1)}@media (min-width: 768px){._btn{width:-moz-fit-content;width:fit-content}}._btn{background-color:#2a2a2a;font-size:.875rem;line-height:1.25rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}._btn:hover{background-color:#5a5a5a}._btn:focus{outline-style:solid;outline-width:2px;outline-color:gray}._btn:disabled{background-color:#c8c8c8;color:gray}._btn:disabled:active{background-color:#c8c8c8;color:gray}._btn:is(.dark *){background-color:#eaeaea;color:#2a2a2a}._btn:hover:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}._btn:active:is(.dark *){background-color:#dcdcdc}._btn:active:hover:is(.dark *){color:#070707}._btn-tertiary{display:inline-flex;width:100%;align-items:center;justify-content:center;gap:.5rem;white-space:nowrap;border-radius:.25rem;padding:.5rem 1.5rem;text-align:center;text-decoration-line:none!important;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s;transition-timing-function:cubic-bezier(0,0,.2,1)}@media (min-width: 768px){._btn-tertiary{width:-moz-fit-content;width:fit-content}}._btn-tertiary{background-color:#070707;color:#f6f6f6}._btn-tertiary:hover{background-color:#2a2a2a;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}._btn-tertiary:focus{background-color:#070707;outline-style:solid;outline-width:2px;outline-color:gray}._btn-tertiary:active{background-color:#474747;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}._btn-tertiary:disabled{background-color:gray;color:#474747}._hyper-link{--tw-text-opacity: 1;color:rgb(147 197 253 / var(--tw-text-opacity, 1));text-decoration-line:underline}._hyper-link:hover{--tw-text-opacity: 1;color:rgb(96 165 250 / var(--tw-text-opacity, 1))}._hyper-link:active{--tw-text-opacity: 1;color:rgb(59 130 246 / var(--tw-text-opacity, 1))}._hyper-link:disabled{color:#5a5a5a}._modal{border-radius:.25rem;border-width:1px;border-color:#5a5a5a;--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow);position:fixed;top:50%;left:50%;z-index:50;max-height:608px;width:90%;max-width:568px;--tw-translate-x: -50%;--tw-translate-y: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));overflow:auto;background-color:#070707;padding:.5rem;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1));transition-duration:.3s}._modal:focus{outline:2px solid transparent;outline-offset:2px}@media (min-width: 768px){._modal{padding:2.5rem}}._modal-overlay{position:fixed;top:0;right:0;bottom:0;left:0;z-index:40;background-color:#070707;opacity:.6}._modal-title{font-size:1.125rem;line-height:1.75rem;font-weight:700;text-transform:uppercase;letter-spacing:.1em;--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.pointer-events-none{pointer-events:none}.visible{visibility:visible}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.sticky{position:sticky}.-inset-0\.5{top:-.125rem;right:-.125rem;bottom:-.125rem;left:-.125rem}.inset-x-4{left:1rem;right:1rem}.inset-y-0{top:0;bottom:0}.bottom-0{bottom:0}.bottom-4{bottom:1rem}.left-0{left:0}.left-\[8px\]{left:8px}.right-0{right:0}.right-4{right:1rem}.right-5{right:1.25rem}.right-\[46px\]{right:46px}.top-0{top:0}.top-7{top:1.75rem}.top-\[25px\]{top:25px}.top-\[40px\]{top:40px}.z-10{z-index:10}.z-20{z-index:20}.z-50{z-index:50}.mr-1{margin-right:.25rem}.mt-10{margin-top:2.5rem}.mt-2{margin-top:.5rem}.flex{display:flex}.inline-flex{display:inline-flex}.\!table{display:table!important}.table{display:table}.grid{display:grid}.hidden{display:none}.\!h-\[64px\]{height:64px!important}.h-10{height:2.5rem}.h-4{height:1rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-7{height:1.75rem}.h-8{height:2rem}.h-9{height:2.25rem}.h-\[25px\]{height:25px}.h-\[36px\]{height:36px}.h-\[70px\]{height:70px}.h-\[72px\]{height:72px}.h-full{height:100%}.h-screen{height:100vh}.max-h-\[500px\]{max-height:500px}.max-h-\[68px\]{max-height:68px}.max-h-\[calc\(100vh-170px\)\]{max-height:calc(100vh - 170px)}.w-0\.5{width:.125rem}.w-1\/3{width:33.333333%}.w-4{width:1rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-7{width:1.75rem}.w-\[25px\]{width:25px}.w-auto{width:auto}.w-full{width:100%}.max-w-\[216px\]{max-width:216px}.rotate-180{--tw-rotate: 180deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-default{cursor:default}.cursor-pointer{cursor:pointer}.select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.flex-row{flex-direction:row}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}.justify-between{justify-content:space-between}.gap-0\.5{gap:.125rem}.gap-1{gap:.25rem}.gap-10{gap:2.5rem}.gap-2{gap:.5rem}.gap-3{gap:.75rem}.gap-4{gap:1rem}.gap-\[5px\]{gap:5px}.overflow-auto{overflow:auto}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-x-hidden{overflow-x:hidden}.whitespace-normal{white-space:normal}.whitespace-nowrap{white-space:nowrap}.rounded{border-radius:.25rem}.rounded-\[3px\]{border-radius:3px}.rounded-\[4px\]{border-radius:4px}.rounded-md{border-radius:.375rem}.border{border-width:1px}.border-\[1\.5px\]{border-width:1.5px}.border-b{border-bottom-width:1px}.border-black{--tw-border-opacity: 1;border-color:rgb(0 0 0 / var(--tw-border-opacity, 1))}.border-blue-600{--tw-border-opacity: 1;border-color:rgb(37 99 235 / var(--tw-border-opacity, 1))}.border-green-600{--tw-border-opacity: 1;border-color:rgb(22 163 74 / var(--tw-border-opacity, 1))}.border-grey-200{border-color:#dcdcdc}.border-grey-400{border-color:#a2a2a2}.border-grey-500\/60{border-color:#80808099}.border-grey-700{border-color:#474747}.border-orange-600{--tw-border-opacity: 1;border-color:rgb(234 88 12 / var(--tw-border-opacity, 1))}.border-red-600{--tw-border-opacity: 1;border-color:rgb(220 38 38 / var(--tw-border-opacity, 1))}.bg-blue-100{--tw-bg-opacity: 1;background-color:rgb(219 234 254 / var(--tw-bg-opacity, 1))}.bg-green-100{--tw-bg-opacity: 1;background-color:rgb(220 252 231 / var(--tw-bg-opacity, 1))}.bg-grey-100{background-color:#eaeaea}.bg-grey-200{background-color:#dcdcdc}.bg-grey-300\/20{background-color:#c8c8c833}.bg-grey-850{background-color:#131313}.bg-grey-900{background-color:#070707}.bg-orange-200{--tw-bg-opacity: 1;background-color:rgb(254 215 170 / var(--tw-bg-opacity, 1))}.bg-red-200{--tw-bg-opacity: 1;background-color:rgb(254 202 202 / var(--tw-bg-opacity, 1))}.bg-white{--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.bg-white\/70{background-color:#ffffffb3}.bg-white\/80{background-color:#fffc}.bg-gradient-to-r{background-image:linear-gradient(to right,var(--tw-gradient-stops))}.from-\[\#072e49\]\/30{--tw-gradient-from: rgb(7 46 73 / .3) var(--tw-gradient-from-position);--tw-gradient-to: rgb(7 46 73 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to)}.via-\[\#0d345f\]\/30{--tw-gradient-to: rgb(13 52 95 / 0) var(--tw-gradient-to-position);--tw-gradient-stops: var(--tw-gradient-from), rgb(13 52 95 / .3) var(--tw-gradient-via-position), var(--tw-gradient-to)}.to-\[\#0d3362\]\/30{--tw-gradient-to: rgb(13 51 98 / .3) var(--tw-gradient-to-position)}.p-0\.5{padding:.125rem}.p-2{padding:.5rem}.p-3{padding:.75rem}.p-4{padding:1rem}.p-\[5px\]{padding:5px}.px-1{padding-left:.25rem;padding-right:.25rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-4{padding-left:1rem;padding-right:1rem}.px-\[40px\]{padding-left:40px;padding-right:40px}.py-2{padding-top:.5rem;padding-bottom:.5rem}.py-4{padding-top:1rem;padding-bottom:1rem}.py-6{padding-top:1.5rem;padding-bottom:1.5rem}.pl-10{padding-left:2.5rem}.pl-3{padding-left:.75rem}.pl-\[25px\]{padding-left:25px}.pr-\[35px\]{padding-right:35px}.text-left{text-align:left}.text-center{text-align:center}.text-right{text-align:right}.text-\[10px\]{font-size:10px}.text-\[13px\]{font-size:13px}.text-\[8px\]{font-size:8px}.text-sm{font-size:.875rem;line-height:1.25rem}.text-xs{font-size:.75rem;line-height:1rem}.font-bold{font-weight:700}.font-normal{font-weight:400}.uppercase{text-transform:uppercase}.leading-\[25px\]{line-height:25px}.leading-none{line-height:1}.tracking-widest{letter-spacing:.1em}.text-\[\#16A34A\]{--tw-text-opacity: 1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-\[\#404040\]{--tw-text-opacity: 1;color:rgb(64 64 64 / var(--tw-text-opacity, 1))}.text-\[\#669DCB\]{--tw-text-opacity: 1;color:rgb(102 157 203 / var(--tw-text-opacity, 1))}.text-\[\#F87171\]{--tw-text-opacity: 1;color:rgb(248 113 113 / var(--tw-text-opacity, 1))}.text-black{--tw-text-opacity: 1;color:rgb(0 0 0 / var(--tw-text-opacity, 1))}.text-blue-700{--tw-text-opacity: 1;color:rgb(29 78 216 / var(--tw-text-opacity, 1))}.text-green-600{--tw-text-opacity: 1;color:rgb(22 163 74 / var(--tw-text-opacity, 1))}.text-grey-200{color:#dcdcdc}.text-grey-400{color:#a2a2a2}.text-grey-500{color:gray}.text-grey-600{color:#5a5a5a}.text-grey-700{color:#474747}.text-grey-800{color:#2a2a2a}.text-grey-900{color:#070707}.text-orange-600{--tw-text-opacity: 1;color:rgb(234 88 12 / var(--tw-text-opacity, 1))}.text-red-600{--tw-text-opacity: 1;color:rgb(220 38 38 / var(--tw-text-opacity, 1))}.text-white{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.shadow{--tw-shadow: 0 1px 3px 0 rgb(0 0 0 / .1), 0 1px 2px -1px rgb(0 0 0 / .1);--tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-\[0_2px_10px\]{--tw-shadow: 0 2px 10px;--tw-shadow-colored: 0 2px 10px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-\[0px_10px_38px_-10px_rgba\(22\,_23\,_24\,_0\.35\)\,0px_10px_20px_-15px_rgba\(22\,_23\,_24\,_0\.2\)\]{--tw-shadow: 0px 10px 38px -10px rgba(22, 23, 24, .35),0px 10px 20px -15px rgba(22, 23, 24, .2);--tw-shadow-colored: 0px 10px 38px -10px var(--tw-shadow-color), 0px 10px 20px -15px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.shadow-black\/10{--tw-shadow-color: rgb(0 0 0 / .1);--tw-shadow: var(--tw-shadow-colored)}.outline-none{outline:2px solid transparent;outline-offset:2px}.blur{--tw-blur: blur(8px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.blur-md{--tw-blur: blur(12px);filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.backdrop-blur{--tw-backdrop-blur: blur(8px);-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.backdrop-filter{-webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.duration-200{transition-duration:.2s}.duration-300{transition-duration:.3s}._header{background:#062d48;background:linear-gradient(90deg,#062d48,#0b3054 15%,#0e386c 45%,#0b203d 64%,#06101a 82%,#060709)}body{font-family:Arial,monospace}table{width:100%;table-layout:auto}table thead,table tfoot,table tbody tr{display:table;width:100%;table-layout:fixed;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}th,td{word-wrap:break-word;overflow:hidden;text-overflow:ellipsis}.resizer{cursor:col-resize;-webkit-user-select:none;-moz-user-select:none;user-select:none;touch-action:none;transition:opacity .2s ease-in-out}.resizer.isResizing{background:#fff;opacity:1}@media (hover: hover){.resizer{opacity:0}*:hover>.resizer{opacity:1}}::-webkit-scrollbar{width:20px}::-webkit-scrollbar-track{background-color:transparent}::-webkit-scrollbar-thumb{background-color:#d6dee1;border-radius:20px;border:6px solid transparent;background-clip:content-box}::-webkit-scrollbar-thumb:hover{background-color:#a8bbbf}::-webkit-scrollbar-corner{background:#0000}table thead th{position:sticky;top:0}.saving{position:absolute;z-index:9999999;top:50%;left:50%;transform:translate(-50%,-50%);height:100px;width:200px;display:none;justify-content:center;align-items:center;background-color:#000000b3;color:#fff;box-shadow:0 0 10px #00000080;border:1px solid rgba(250,250,250,.5);font-size:.9em;border-radius:20px;animation:popup .3s ease-in-out}.saving.show{display:flex}.loader{position:relative;bottom:0;right:-5px;border:4px solid #f3f3f3;border-radius:50%;border-top:4px solid #00acff;width:20px;height:20px;animation:spin 1.5s linear infinite;opacity:1}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.hover\:bg-grey-300:hover{background-color:#c8c8c8}.hover\:text-grey-800:hover{color:#2a2a2a}.hover\:text-white:hover{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.focus\:shadow-\[0_0_0_2px\]:focus{--tw-shadow: 0 0 0 2px;--tw-shadow-colored: 0 0 0 2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.focus\:shadow-black:focus{--tw-shadow-color: #000;--tw-shadow: var(--tw-shadow-colored)}.focus\:outline-none:focus{outline:2px solid transparent;outline-offset:2px}.focus\:outline:focus{outline-style:solid}.focus\:outline-2:focus{outline-width:2px}.focus\:outline-grey-500:focus{outline-color:gray}.focus-visible\:ring:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.focus-visible\:ring-purple-500:focus-visible{--tw-ring-opacity: 1;--tw-ring-color: rgb(168 85 247 / var(--tw-ring-opacity, 1))}.focus-visible\:ring-opacity-75:focus-visible{--tw-ring-opacity: .75}.data-\[disabled\]\:pointer-events-none[data-disabled]{pointer-events:none}.data-\[highlighted\]\:bg-grey-600[data-highlighted]{background-color:#5a5a5a}.data-\[disabled\]\:text-grey-400[data-disabled]{color:#a2a2a2}.data-\[highlighted\]\:text-white[data-highlighted],.data-\[placeholder\]\:text-white[data-placeholder]{--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.data-\[highlighted\]\:outline-none[data-highlighted]{outline:2px solid transparent;outline-offset:2px}.dark\:border-grey-200\/60:is(.dark *){border-color:#dcdcdc99}.dark\:border-grey-800:is(.dark *){border-color:#2a2a2a}.dark\:bg-\[\#202020\]:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(32 32 32 / var(--tw-bg-opacity, 1))}.dark\:bg-black:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(0 0 0 / var(--tw-bg-opacity, 1))}.dark\:bg-grey-800:is(.dark *){background-color:#2a2a2a}.dark\:bg-grey-850:is(.dark *){background-color:#131313}.dark\:bg-grey-900:is(.dark *){background-color:#070707}.dark\:bg-grey-900\/70:is(.dark *){background-color:#070707b3}.dark\:bg-grey-900\/80:is(.dark *){background-color:#070707cc}.dark\:text-grey-700:is(.dark *){color:#474747}.dark\:text-white:is(.dark *){--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}.dark\:hover\:bg-grey-800:hover:is(.dark *){background-color:#2a2a2a}.dark\:hover\:bg-white:hover:is(.dark *){--tw-bg-opacity: 1;background-color:rgb(255 255 255 / var(--tw-bg-opacity, 1))}.dark\:hover\:text-white:hover:is(.dark *){--tw-text-opacity: 1;color:rgb(255 255 255 / var(--tw-text-opacity, 1))}@media (min-width: 768px){.md\:left-1\/2{left:50%}.md\:left-\[25px\]{left:25px}.md\:right-7{right:1.75rem}.md\:flex{display:flex}.md\:max-w-\[658px\]{max-width:658px}.md\:-translate-x-\[50\%\]{--tw-translate-x: -50%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.md\:px-\[58px\]{padding-left:58px;padding-right:58px}.md\:text-xs{font-size:.75rem;line-height:1rem}}@media (min-width: 1024px){.lg\:flex{display:flex}}@media (max-height: 450px){.smh\:hidden{display:none}.smh\:max-h-\[calc\(100vh-95px\)\]{max-height:calc(100vh - 95px)}}@media (min-width: 890px){.mdl\:block{display:block}}

</style>
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
