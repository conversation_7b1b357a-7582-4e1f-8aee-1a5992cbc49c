#!/usr/bin/env python3
"""
基于DeepSeek的AI模型智能评估系统
让AI来帮我们选择最适合的AI模型
"""

import asyncio
import json
import time
from typing import Dict, List, Tuple
from dataclasses import dataclass
from ai_engine.local_llm import LocalLLMClient

@dataclass
class ModelCandidate:
    """候选模型信息"""
    name: str
    description: str
    size: str
    memory_requirement: str
    strengths: List[str]
    weaknesses: List[str]
    use_cases: List[str]
    performance_metrics: Dict = None

class DeepSeekModelEvaluator:
    """基于DeepSeek的模型评估器"""
    
    def __init__(self):
        # 使用DeepSeek作为评估专家
        self.evaluator = LocalLLMClient(model_name="deepseek-coder:6.7b-instruct")
        
        # 候选模型库
        self.candidates = [
            ModelCandidate(
                name="qwen2.5:7b-instruct",
                description="阿里巴巴Qwen2.5-7B指令优化版",
                size="4.7GB",
                memory_requirement="8GB",
                strengths=["中文理解优秀", "金融知识丰富", "推理能力强", "指令遵循好"],
                weaknesses=["英文能力相对较弱", "模型较新生态待完善"],
                use_cases=["中文金融分析", "股票研究", "投资建议", "中文对话"]
            ),
            
            ModelCandidate(
                name="llama3.1:8b-instruct",
                description="Meta开源Llama3.1-8B指令版",
                size="4.7GB", 
                memory_requirement="8GB",
                strengths=["Meta官方支持", "英文能力强", "生态成熟", "性能稳定"],
                weaknesses=["中文能力一般", "金融专业知识有限"],
                use_cases=["英文分析", "通用对话", "代码生成", "逻辑推理"]
            ),
            
            ModelCandidate(
                name="deepseek-coder:6.7b-instruct",
                description="DeepSeek代码专家模型",
                size="3.8GB",
                memory_requirement="8GB", 
                strengths=["代码理解顶级", "技术分析专业", "逻辑推理强", "数学计算准确"],
                weaknesses=["通用对话能力有限", "金融知识相对较少"],
                use_cases=["技术分析", "量化策略", "代码生成", "算法优化"]
            ),
            
            ModelCandidate(
                name="deepseek-math:7b-instruct", 
                description="DeepSeek数学专家模型",
                size="4.1GB",
                memory_requirement="8GB",
                strengths=["数学计算精确", "量化分析专业", "逻辑推理强", "统计分析好"],
                weaknesses=["语言表达相对简单", "创意性较低"],
                use_cases=["量化分析", "风险计算", "统计建模", "数据分析"]
            ),
            
            ModelCandidate(
                name="yi:6b-chat",
                description="零一万物Yi-6B对话版",
                size="3.5GB",
                memory_requirement="6GB",
                strengths=["中文友好", "轻量高效", "对话自然", "国产可控"],
                weaknesses=["模型较小能力有限", "专业知识深度不足"],
                use_cases=["轻量对话", "中文交互", "资源受限环境", "快速响应"]
            ),
            
            ModelCandidate(
                name="phi3:3.8b",
                description="微软Phi3-3.8B轻量模型",
                size="2.3GB",
                memory_requirement="4GB",
                strengths=["超轻量级", "响应极快", "资源友好", "微软支持"],
                weaknesses=["能力相对有限", "复杂推理较弱"],
                use_cases=["边缘计算", "快速响应", "资源受限", "简单任务"]
            ),
            
            ModelCandidate(
                name="gemma:7b-instruct",
                description="Google Gemma-7B指令版",
                size="4.8GB", 
                memory_requirement="8GB",
                strengths=["Google技术", "多语言支持", "安全性好", "平衡性能"],
                weaknesses=["中文能力一般", "金融专业性不足"],
                use_cases=["多语言处理", "安全要求高", "通用分析", "平衡需求"]
            ),
            
            ModelCandidate(
                name="qwen2.5:14b-instruct",
                description="阿里巴巴Qwen2.5-14B高性能版",
                size="8.2GB",
                memory_requirement="16GB", 
                strengths=["性能强劲", "中文顶级", "金融知识深厚", "推理能力强"],
                weaknesses=["资源需求高", "响应较慢"],
                use_cases=["专业金融分析", "复杂推理", "高质量对话", "深度研究"]
            )
        ]
    
    async def evaluate_model_for_task(self, model: ModelCandidate, task_description: str) -> Dict:
        """让DeepSeek评估特定模型对特定任务的适合度"""
        
        evaluation_prompt = f"""
作为AI模型评估专家，请分析以下模型对指定任务的适合度：

任务描述：{task_description}

模型信息：
- 名称：{model.name}
- 描述：{model.description}
- 大小：{model.size}
- 内存需求：{model.memory_requirement}
- 优势：{', '.join(model.strengths)}
- 劣势：{', '.join(model.weaknesses)}
- 适用场景：{', '.join(model.use_cases)}

请从以下维度评估（1-10分）：
1. 任务匹配度：模型能力与任务需求的匹配程度
2. 性能表现：预期的执行效果和质量
3. 资源效率：资源消耗与性能的平衡
4. 稳定性：模型的可靠性和一致性
5. 易用性：部署和使用的便利程度

请按以下格式回答：
任务匹配度：X分 - 理由
性能表现：X分 - 理由  
资源效率：X分 - 理由
稳定性：X分 - 理由
易用性：X分 - 理由
综合评分：X分
推荐指数：★★★★★ (1-5星)
关键优势：列出2-3个最重要的优势
主要风险：列出1-2个需要注意的问题
使用建议：给出具体的使用建议

请客观、专业地分析，基于技术事实而非偏见。
"""
        
        try:
            response = self.evaluator.generate(evaluation_prompt, temperature=0.3)
            return self._parse_evaluation_response(response, model.name)
        except Exception as e:
            return {"error": str(e), "model": model.name}
    
    def _parse_evaluation_response(self, response: str, model_name: str) -> Dict:
        """解析DeepSeek的评估响应"""
        try:
            lines = response.strip().split('\n')
            evaluation = {"model": model_name, "raw_response": response}
            
            scores = {}
            for line in lines:
                line = line.strip()
                if '：' in line and '分' in line:
                    parts = line.split('：')
                    if len(parts) >= 2:
                        key = parts[0].strip()
                        score_part = parts[1].split('-')[0].strip()
                        try:
                            score = float([s for s in score_part.split() if s.replace('.', '').isdigit()][0])
                            scores[key] = score
                        except:
                            pass
                
                elif '推荐指数' in line:
                    stars = line.count('★')
                    evaluation['recommendation_stars'] = stars
                
                elif '关键优势' in line:
                    evaluation['key_advantages'] = line.split('：')[1] if '：' in line else ""
                
                elif '主要风险' in line:
                    evaluation['main_risks'] = line.split('：')[1] if '：' in line else ""
                
                elif '使用建议' in line:
                    evaluation['usage_advice'] = line.split('：')[1] if '：' in line else ""
            
            evaluation['scores'] = scores
            evaluation['overall_score'] = scores.get('综合评分', 0)
            
            return evaluation
            
        except Exception as e:
            return {
                "model": model_name,
                "error": f"解析失败: {e}",
                "raw_response": response
            }
    
    async def comprehensive_evaluation(self, task_description: str) -> List[Dict]:
        """对所有候选模型进行综合评估"""
        print(f"🤖 DeepSeek正在评估所有模型...")
        print(f"📋 任务：{task_description}")
        print("=" * 60)
        
        evaluations = []
        
        for i, model in enumerate(self.candidates, 1):
            print(f"🔍 评估模型 {i}/{len(self.candidates)}: {model.name}")
            
            evaluation = await self.evaluate_model_for_task(model, task_description)
            evaluations.append(evaluation)
            
            # 显示简要结果
            if 'overall_score' in evaluation:
                stars = evaluation.get('recommendation_stars', 0)
                print(f"   综合评分: {evaluation['overall_score']}/10")
                print(f"   推荐指数: {'★' * stars}{'☆' * (5-stars)}")
            
            print()
            await asyncio.sleep(1)  # 避免请求过于频繁
        
        return evaluations
    
    def generate_recommendation_report(self, evaluations: List[Dict], task_description: str):
        """生成推荐报告"""
        print("\n" + "=" * 80)
        print("📊 DeepSeek AI模型评估报告")
        print("=" * 80)
        print(f"📋 评估任务: {task_description}")
        print(f"🤖 评估专家: DeepSeek-Coder")
        print(f"📅 评估时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 过滤有效评估
        valid_evaluations = [e for e in evaluations if 'overall_score' in e and not e.get('error')]
        
        if not valid_evaluations:
            print("\n❌ 没有有效的评估结果")
            return
        
        # 按综合评分排序
        valid_evaluations.sort(key=lambda x: x['overall_score'], reverse=True)
        
        print(f"\n🏆 模型排名 (共{len(valid_evaluations)}个模型):")
        print("-" * 80)
        
        for i, eval_result in enumerate(valid_evaluations, 1):
            model_name = eval_result['model']
            overall_score = eval_result['overall_score']
            stars = eval_result.get('recommendation_stars', 0)
            
            print(f"{i}. {model_name}")
            print(f"   综合评分: {overall_score}/10")
            print(f"   推荐指数: {'★' * stars}{'☆' * (5-stars)}")
            
            if 'scores' in eval_result:
                scores = eval_result['scores']
                print(f"   详细评分: ", end="")
                score_items = []
                for key, value in scores.items():
                    if key != '综合评分':
                        score_items.append(f"{key}({value})")
                print(" | ".join(score_items))
            
            if eval_result.get('key_advantages'):
                print(f"   关键优势: {eval_result['key_advantages']}")
            
            if eval_result.get('main_risks'):
                print(f"   主要风险: {eval_result['main_risks']}")
            
            print()
        
        # DeepSeek的最终推荐
        best_model = valid_evaluations[0]
        print("🎯 DeepSeek最终推荐:")
        print("-" * 40)
        print(f"🥇 首选模型: {best_model['model']}")
        print(f"📊 综合评分: {best_model['overall_score']}/10")
        print(f"⭐ 推荐指数: {'★' * best_model.get('recommendation_stars', 0)}")
        
        if best_model.get('usage_advice'):
            print(f"💡 使用建议: {best_model['usage_advice']}")
        
        # 配置建议
        print(f"\n🔧 配置方法:")
        print(f"在 ai_engine/local_llm.py 中设置:")
        print(f"LocalLLMClient(model_name='{best_model['model']}')")
        
        # 安装命令
        print(f"\n📥 安装命令:")
        print(f"ollama pull {best_model['model']}")

async def main():
    """主函数"""
    print("🤖 基于DeepSeek的AI模型智能评估系统")
    print("=" * 50)
    
    # 定义评估任务
    task_description = """
    智能股票分析和投资建议系统，需要AI模型具备以下能力：
    1. 理解和分析股票技术指标（RSI、MACD、布林带等）
    2. 解读基本面数据（PE、ROE、营收增长等）
    3. 综合多种因素给出投资建议（买入/卖出/观望）
    4. 提供清晰的分析理由和风险提示
    5. 支持中文交互，适合中国股市分析
    6. 响应速度适中，支持实时分析
    7. 在8-16GB内存环境下稳定运行
    """
    
    evaluator = DeepSeekModelEvaluator()
    
    # 检查DeepSeek是否可用
    try:
        test_response = evaluator.evaluator.generate("你好，请简单介绍一下你自己。", temperature=0.3)
        if not test_response:
            print("❌ DeepSeek模型不可用，请先安装:")
            print("ollama pull deepseek-coder:6.7b-instruct")
            return
        print("✅ DeepSeek评估专家已就绪")
    except Exception as e:
        print(f"❌ DeepSeek连接失败: {e}")
        print("请确保已安装并启动DeepSeek模型")
        return
    
    # 执行评估
    evaluations = await evaluator.comprehensive_evaluation(task_description)
    
    # 生成报告
    evaluator.generate_recommendation_report(evaluations, task_description)
    
    # 保存详细结果
    with open("deepseek_model_evaluation.json", "w", encoding="utf-8") as f:
        json.dump(evaluations, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 详细评估结果已保存到: deepseek_model_evaluation.json")

if __name__ == "__main__":
    asyncio.run(main())
