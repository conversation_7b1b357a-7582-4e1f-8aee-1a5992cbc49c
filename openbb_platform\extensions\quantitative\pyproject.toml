[tool.poetry]
name = "openbb-quantitative"
version = "1.4.4"
description = "Quantitative Analysis extension for OpenBB"
authors = ["OpenBB Team <<EMAIL>>"]
license = "AGPL-3.0-only"
readme = "README.md"
packages = [{ include = "openbb_quantitative" }]

[tool.poetry.dependencies]
python = ">=3.9.21,<3.13"
openbb-core = "^1.4.8"
pandas-ta-openbb = "^0.4.20"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.plugins."openbb_core_extension"]
quantitative = "openbb_quantitative.quantitative_router:router"
