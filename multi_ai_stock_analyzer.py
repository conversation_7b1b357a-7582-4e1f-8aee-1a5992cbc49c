#!/usr/bin/env python3
"""
多AI集成股票分析系统
整合多个免费AI服务，提供强大的股票分析能力
"""

import asyncio
import aiohttp
import json
import os
from typing import Dict, List, Optional
from datetime import datetime
import yfinance as yf

class MultiAIStockAnalyzer:
    """多AI股票分析器"""
    
    def __init__(self):
        self.ai_services = {
            "gemini": {
                "name": "Google Gemini Pro",
                "url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent",
                "free_tier": "60 requests/minute",
                "strength": "实时数据访问、多模态分析",
                "api_key_env": "GEMINI_API_KEY"
            },
            "openai": {
                "name": "OpenAI GPT-4o-mini",
                "url": "https://api.openai.com/v1/chat/completions",
                "free_tier": "$5 free credit",
                "strength": "金融分析专业、推理能力强",
                "api_key_env": "OPENAI_API_KEY"
            },
            "huggingface": {
                "name": "Hugging Face Inference",
                "url": "https://api-inference.huggingface.co/models/",
                "free_tier": "1000 requests/month",
                "strength": "开源模型、完全免费",
                "api_key_env": "HUGGINGFACE_API_KEY"
            },
            "cohere": {
                "name": "Cohere Command",
                "url": "https://api.cohere.ai/v1/generate",
                "free_tier": "100 requests/month",
                "strength": "文本生成、分析能力强",
                "api_key_env": "COHERE_API_KEY"
            }
        }
        
        self.available_services = self._check_available_services()
    
    def _check_available_services(self) -> List[str]:
        """检查可用的AI服务"""
        available = []
        for service_id, service_info in self.ai_services.items():
            api_key = os.getenv(service_info["api_key_env"])
            if api_key:
                available.append(service_id)
        return available
    
    def show_ai_services_info(self):
        """显示AI服务信息"""
        print("🤖 可用的免费AI服务")
        print("=" * 80)
        
        for service_id, info in self.ai_services.items():
            status = "✅ 已配置" if service_id in self.available_services else "⚠️  未配置"
            print(f"\n📊 {info['name']} {status}")
            print(f"   免费额度: {info['free_tier']}")
            print(f"   专长: {info['strength']}")
            print(f"   环境变量: {info['api_key_env']}")
        
        if not self.available_services:
            print(f"\n💡 配置建议:")
            self._show_setup_guide()
    
    def _show_setup_guide(self):
        """显示配置指南"""
        guides = {
            "gemini": {
                "url": "https://makersuite.google.com/app/apikey",
                "steps": [
                    "访问 Google AI Studio",
                    "登录Google账户",
                    "创建API密钥",
                    "设置环境变量: GEMINI_API_KEY"
                ]
            },
            "openai": {
                "url": "https://platform.openai.com/api-keys",
                "steps": [
                    "访问 OpenAI Platform",
                    "注册/登录账户",
                    "创建API密钥",
                    "设置环境变量: OPENAI_API_KEY"
                ]
            },
            "huggingface": {
                "url": "https://huggingface.co/settings/tokens",
                "steps": [
                    "访问 Hugging Face",
                    "注册/登录账户",
                    "创建Access Token",
                    "设置环境变量: HUGGINGFACE_API_KEY"
                ]
            }
        }
        
        for service, guide in guides.items():
            print(f"\n🔧 {self.ai_services[service]['name']} 配置:")
            for i, step in enumerate(guide['steps'], 1):
                print(f"   {i}. {step}")
            print(f"   🌐 {guide['url']}")
    
    async def analyze_with_gemini(self, prompt: str) -> Optional[str]:
        """使用Gemini分析"""
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            return None
        
        url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
        
        payload = {
            "contents": [{
                "parts": [{"text": prompt}]
            }],
            "generationConfig": {
                "temperature": 0.3,
                "maxOutputTokens": 2048
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['candidates'][0]['content']['parts'][0]['text']
                    else:
                        print(f"Gemini API错误: {response.status}")
                        return None
        except Exception as e:
            print(f"Gemini请求失败: {e}")
            return None
    
    async def analyze_with_openai(self, prompt: str) -> Optional[str]:
        """使用OpenAI分析"""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            return None
        
        url = "https://api.openai.com/v1/chat/completions"
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": "gpt-4o-mini",  # 免费额度更多
            "messages": [
                {"role": "system", "content": "你是一位专业的股票分析师。"},
                {"role": "user", "content": prompt}
            ],
            "temperature": 0.3,
            "max_tokens": 2048
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content']
                    else:
                        print(f"OpenAI API错误: {response.status}")
                        return None
        except Exception as e:
            print(f"OpenAI请求失败: {e}")
            return None
    
    async def analyze_with_huggingface(self, prompt: str) -> Optional[str]:
        """使用Hugging Face分析"""
        api_key = os.getenv("HUGGINGFACE_API_KEY")
        if not api_key:
            return None
        
        # 使用免费的开源模型
        model = "microsoft/DialoGPT-large"
        url = f"https://api-inference.huggingface.co/models/{model}"
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "inputs": prompt,
            "parameters": {
                "max_length": 1000,
                "temperature": 0.3
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        if isinstance(result, list) and len(result) > 0:
                            return result[0].get('generated_text', '')
                        return str(result)
                    else:
                        print(f"Hugging Face API错误: {response.status}")
                        return None
        except Exception as e:
            print(f"Hugging Face请求失败: {e}")
            return None
    
    def build_analysis_prompt(self, stock_data: Dict, technical_data: Dict) -> str:
        """构建分析提示词"""
        symbol = stock_data.get('symbol', 'Unknown')
        name = stock_data.get('name', symbol)
        price = stock_data.get('price', 0)
        change_percent = stock_data.get('change_percent', 0)
        
        prompt = f"""
请作为专业股票分析师，分析以下股票：

股票信息：
- 代码：{symbol}
- 名称：{name}
- 当前价格：{price:.2f}
- 涨跌幅：{change_percent:.2f}%

技术指标：
- RSI：{technical_data.get('rsi', 50):.1f}
- 趋势：{technical_data.get('trend', 'unknown')}
- 成交量比率：{technical_data.get('volume_ratio', 1):.2f}

请提供：
1. 投资建议（买入/观望/卖出）
2. 信心度（0-100分）
3. 核心理由（2-3点）
4. 主要风险（1-2点）

请用简洁专业的语言回答。
"""
        return prompt
    
    async def comprehensive_analysis(self, symbol: str) -> Dict:
        """综合AI分析"""
        # 获取股票数据
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="60d")
            info = ticker.info
            
            if hist.empty:
                return {"error": "无法获取股票数据"}
            
            latest = hist.iloc[-1]
            stock_data = {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'price': float(latest['Close']),
                'change_percent': float((latest['Close'] - latest['Open']) / latest['Open'] * 100)
            }
            
            # 简化的技术指标
            rsi = 50  # 简化计算
            technical_data = {
                'rsi': rsi,
                'trend': 'upward' if latest['Close'] > latest['Open'] else 'downward',
                'volume_ratio': 1.0
            }
            
        except Exception as e:
            return {"error": f"数据获取失败: {e}"}
        
        # 构建提示词
        prompt = self.build_analysis_prompt(stock_data, technical_data)
        
        # 尝试多个AI服务
        analyses = {}
        
        if "gemini" in self.available_services:
            print("🔍 使用Gemini分析...")
            gemini_result = await self.analyze_with_gemini(prompt)
            if gemini_result:
                analyses["gemini"] = gemini_result
        
        if "openai" in self.available_services:
            print("🔍 使用OpenAI分析...")
            openai_result = await self.analyze_with_openai(prompt)
            if openai_result:
                analyses["openai"] = openai_result
        
        if "huggingface" in self.available_services:
            print("🔍 使用Hugging Face分析...")
            hf_result = await self.analyze_with_huggingface(prompt)
            if hf_result:
                analyses["huggingface"] = hf_result
        
        if not analyses:
            return {"error": "所有AI服务都不可用"}
        
        return {
            "symbol": symbol,
            "stock_data": stock_data,
            "ai_analyses": analyses,
            "timestamp": datetime.now().isoformat()
        }

async def main():
    """主函数"""
    analyzer = MultiAIStockAnalyzer()
    
    print("🤖 多AI集成股票分析系统")
    print("=" * 60)
    
    # 显示服务状态
    analyzer.show_ai_services_info()
    
    if not analyzer.available_services:
        print("\n⚠️  请先配置至少一个AI服务的API密钥")
        return
    
    # 测试分析
    test_symbol = "AAPL"
    print(f"\n📊 测试分析: {test_symbol}")
    print("-" * 40)
    
    result = await analyzer.comprehensive_analysis(test_symbol)
    
    if "error" in result:
        print(f"❌ 错误: {result['error']}")
        return
    
    print(f"✅ 分析完成: {result['stock_data']['name']}")
    print(f"💰 价格: ${result['stock_data']['price']:.2f}")
    
    for ai_name, analysis in result['ai_analyses'].items():
        print(f"\n🤖 {analyzer.ai_services[ai_name]['name']} 分析:")
        print(f"   {analysis[:200]}...")

if __name__ == "__main__":
    asyncio.run(main())
