#!/usr/bin/env python3
"""
简单的AI连接测试脚本
快速检查AI服务是否正常工作
"""

import requests
import json
import sys
import os

def test_ollama_connection():
    """测试Ollama连接"""
    print("🔍 测试Ollama连接...")
    
    # 尝试不同的端口
    ports_to_try = [
        (11434, "本地标准端口"),
        (1753, "群晖Docker端口")
    ]
    
    for port, description in ports_to_try:
        try:
            url = f"http://localhost:{port}/api/tags"
            print(f"  尝试连接 {description} ({port})...", end=" ")
            
            response = requests.get(url, timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                print(f"✅ 成功! 已安装 {len(models)} 个模型")
                
                if models:
                    print("    已安装的模型:")
                    for model in models:
                        print(f"      - {model.get('name', 'Unknown')}")
                else:
                    print("    ⚠️  未安装任何模型")
                
                return port, models
            else:
                print(f"❌ HTTP错误: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print("❌ 连接被拒绝")
        except requests.exceptions.Timeout:
            print("❌ 连接超时")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    return None, []

def test_ai_model(port, model_name="qwen2.5:7b-instruct"):
    """测试AI模型响应"""
    print(f"\n🤖 测试AI模型: {model_name}")
    
    try:
        url = f"http://localhost:{port}/api/generate"
        
        payload = {
            "model": model_name,
            "prompt": "你好，请用一句话介绍你自己。",
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 50
            }
        }
        
        print("  发送测试请求...", end=" ")
        response = requests.post(url, json=payload, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            ai_response = data.get('response', '').strip()
            
            if ai_response:
                print("✅ 成功!")
                print(f"  AI回复: {ai_response}")
                return True
            else:
                print("❌ 响应为空")
                return False
        else:
            print(f"❌ HTTP错误: {response.status_code}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

def install_default_model(port):
    """安装默认模型"""
    print("\n📥 安装默认AI模型...")
    
    # 检测环境
    is_docker = os.path.exists('/.dockerenv')
    
    model_name = "qwen2.5:7b-instruct"
    
    try:
        if is_docker:
            # Docker环境
            import subprocess
            print(f"  在Docker环境中安装 {model_name}...")
            result = subprocess.run(
                ["docker-compose", "exec", "ollama", "ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=600
            )
        else:
            # 本地环境
            import subprocess
            print(f"  在本地环境中安装 {model_name}...")
            result = subprocess.run(
                ["ollama", "pull", model_name],
                capture_output=True,
                text=True,
                timeout=600
            )
        
        if result.returncode == 0:
            print(f"  ✅ 模型 {model_name} 安装成功!")
            return True
        else:
            print(f"  ❌ 模型安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⏰ 模型安装超时")
        return False
    except FileNotFoundError:
        print("  ❌ 命令不可用")
        return False
    except Exception as e:
        print(f"  ❌ 安装过程出错: {e}")
        return False

def test_python_ai_client():
    """测试Python AI客户端"""
    print("\n🐍 测试Python AI客户端...")
    
    try:
        # 导入我们的AI客户端
        sys.path.append('.')
        from ai_engine.local_llm import LocalLLMClient
        
        print("  创建AI客户端...", end=" ")
        client = LocalLLMClient()
        print("✅")
        
        print("  测试AI生成...", end=" ")
        response = client.generate("你好，请简单介绍一下你自己。", temperature=0.3)
        
        if response and len(response) > 5:
            print("✅")
            print(f"  AI回复: {response[:100]}...")
            return True
        else:
            print("❌ 响应为空或过短")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🤖 AI连接快速测试")
    print("=" * 30)
    
    # 1. 测试Ollama连接
    port, models = test_ollama_connection()
    
    if not port:
        print("\n❌ 无法连接到Ollama服务")
        print("\n🔧 修复建议:")
        print("1. 检查Ollama是否安装: https://ollama.ai/")
        print("2. 启动Ollama服务: ollama serve")
        print("3. 或运行修复工具: python ai_connection_fix.py")
        return 1
    
    # 2. 检查是否有模型
    if not models:
        print("\n⚠️  未安装任何AI模型")
        choice = input("是否安装默认模型? (y/N): ").strip().lower()
        
        if choice == 'y':
            if install_default_model(port):
                # 重新检查模型
                _, models = test_ollama_connection()
            else:
                print("模型安装失败，请手动安装")
                return 1
        else:
            print("跳过模型安装")
            return 1
    
    # 3. 测试AI模型
    if models:
        model_name = models[0]['name'] if isinstance(models[0], dict) else models[0]
        if test_ai_model(port, model_name):
            print("✅ AI模型测试成功!")
        else:
            print("❌ AI模型测试失败")
            return 1
    
    # 4. 测试Python客户端
    if test_python_ai_client():
        print("✅ Python AI客户端测试成功!")
    else:
        print("❌ Python AI客户端测试失败")
        return 1
    
    print("\n🎉 所有测试通过!")
    print("AI连接正常，可以使用智能选股系统了")
    
    print("\n📋 下一步:")
    print("1. 运行智能模型选择: python smart_model_installer.py")
    print("2. 启动选股系统: python start.py")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
