#!/usr/bin/env python3
"""
中国股市数据获取测试
验证系统是否能正常获取中国A股实时数据
"""

import asyncio
import sys
from datetime import datetime

# 添加项目路径
sys.path.append('.')

try:
    from openbb import obb
    OPENBB_AVAILABLE = True
except ImportError:
    OPENBB_AVAILABLE = False
    print("⚠️  OpenBB未安装，将使用备用数据源")

import yfinance as yf
import pandas as pd

class ChinaStockDataTester:
    """中国股市数据测试器"""
    
    def __init__(self):
        # 中国A股测试股票池
        self.test_stocks = {
            # 上交所主板
            "600519.SS": "贵州茅台",
            "600036.SS": "招商银行", 
            "600000.SS": "浦发银行",
            "601318.SS": "中国平安",
            
            # 深交所主板
            "000001.SZ": "平安银行",
            "000002.SZ": "万科A",
            "000858.SZ": "五粮液",
            
            # 创业板
            "300059.SZ": "东方财富",
            "300750.SZ": "宁德时代",
            
            # 科创板
            "688981.SS": "中芯国际",
        }
    
    def test_yfinance_china_stocks(self):
        """测试yfinance获取中国股票数据"""
        print("🧪 测试yfinance获取中国A股数据")
        print("-" * 50)
        
        success_count = 0
        total_count = len(self.test_stocks)
        
        for symbol, name in self.test_stocks.items():
            try:
                print(f"📊 获取 {name} ({symbol})...", end=" ")
                
                # 获取股票信息
                ticker = yf.Ticker(symbol)
                
                # 获取基本信息
                info = ticker.info
                
                # 获取最近1天的数据
                hist = ticker.history(period="1d")
                
                if not hist.empty:
                    latest = hist.iloc[-1]
                    price = latest['Close']
                    volume = latest['Volume']
                    
                    print(f"✅ 价格: ¥{price:.2f}, 成交量: {volume:,.0f}")
                    success_count += 1
                    
                    # 显示更多信息
                    market_cap = info.get('marketCap', 0)
                    pe_ratio = info.get('trailingPE', 0)
                    
                    if market_cap > 0:
                        print(f"    市值: {market_cap/100000000:.0f}亿, PE: {pe_ratio:.1f}")
                else:
                    print("❌ 无历史数据")
                    
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        print(f"\n📈 yfinance测试结果: {success_count}/{total_count} 成功")
        return success_count > 0
    
    async def test_openbb_china_stocks(self):
        """测试OpenBB获取中国股票数据"""
        if not OPENBB_AVAILABLE:
            print("⚠️  OpenBB不可用，跳过测试")
            return False
        
        print("\n🧪 测试OpenBB获取中国A股数据")
        print("-" * 50)
        
        success_count = 0
        total_count = min(3, len(self.test_stocks))  # 只测试前3个，避免API限制
        
        test_symbols = list(self.test_stocks.items())[:total_count]
        
        for symbol, name in test_symbols:
            try:
                print(f"📊 获取 {name} ({symbol})...", end=" ")
                
                # 使用OpenBB获取报价
                quote_data = obb.equity.price.quote(symbol=symbol, provider="yfinance")
                
                if quote_data.results:
                    result = quote_data.results[0]
                    price = getattr(result, 'last_price', 0) or getattr(result, 'price', 0)
                    volume = getattr(result, 'volume', 0)
                    
                    print(f"✅ 价格: ¥{price:.2f}, 成交量: {volume:,.0f}")
                    success_count += 1
                else:
                    print("❌ 无数据返回")
                    
            except Exception as e:
                print(f"❌ 错误: {e}")
        
        print(f"\n📈 OpenBB测试结果: {success_count}/{total_count} 成功")
        return success_count > 0
    
    def test_technical_indicators(self):
        """测试技术指标计算"""
        print("\n🧪 测试技术指标计算")
        print("-" * 50)
        
        # 选择一个活跃股票进行测试
        symbol = "000001.SZ"  # 平安银行
        name = self.test_stocks[symbol]
        
        try:
            print(f"📊 计算 {name} ({symbol}) 技术指标...")
            
            # 获取60天历史数据
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="60d")
            
            if len(hist) < 20:
                print("❌ 历史数据不足")
                return False
            
            # 计算RSI
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.iloc[-1]
            
            # 计算移动平均线
            ma20 = hist['Close'].rolling(window=20).mean().iloc[-1]
            ma60 = hist['Close'].rolling(window=60).mean().iloc[-1] if len(hist) >= 60 else 0
            
            # 计算RSI
            rsi = calculate_rsi(hist['Close'])
            
            # 计算成交量比率
            avg_volume = hist['Volume'].rolling(window=20).mean().iloc[-1]
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            print(f"✅ 技术指标计算成功:")
            print(f"    RSI: {rsi:.1f}")
            print(f"    MA20: ¥{ma20:.2f}")
            print(f"    MA60: ¥{ma60:.2f}")
            print(f"    成交量比率: {volume_ratio:.2f}")
            
            return True
            
        except Exception as e:
            print(f"❌ 技术指标计算失败: {e}")
            return False
    
    def test_ai_analysis_simulation(self):
        """模拟AI分析过程"""
        print("\n🤖 模拟AI分析过程")
        print("-" * 50)
        
        # 模拟股票数据
        stock_data = {
            'symbol': '000001.SZ',
            'name': '平安银行',
            'price': 12.50,
            'volume': 50000000,
            'rsi': 45.5,
            'ma20': 12.20,
            'ma60': 11.80,
            'pe': 8.5,
            'roe': 12.5,
            'volume_ratio': 1.2
        }
        
        print(f"📊 分析股票: {stock_data['name']} ({stock_data['symbol']})")
        print(f"    当前价格: ¥{stock_data['price']}")
        print(f"    RSI: {stock_data['rsi']}")
        print(f"    PE比率: {stock_data['pe']}")
        
        # 模拟AI分析逻辑
        analysis_prompt = f"""
        请分析以下股票数据：
        股票：{stock_data['name']}({stock_data['symbol']})
        价格：¥{stock_data['price']}
        RSI：{stock_data['rsi']}
        PE比率：{stock_data['pe']}
        ROE：{stock_data['roe']}%
        
        请给出投资建议和理由。
        """
        
        print(f"🤖 AI分析提示词已生成 ({len(analysis_prompt)} 字符)")
        print("✅ AI分析流程模拟成功")
        
        return True
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        print("🇨🇳 中国股市数据获取综合测试")
        print("=" * 60)
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 测试股票数量: {len(self.test_stocks)}")
        
        results = {
            'yfinance': False,
            'openbb': False,
            'technical': False,
            'ai_simulation': False
        }
        
        # 1. 测试yfinance
        results['yfinance'] = self.test_yfinance_china_stocks()
        
        # 2. 测试OpenBB
        results['openbb'] = await self.test_openbb_china_stocks()
        
        # 3. 测试技术指标
        results['technical'] = self.test_technical_indicators()
        
        # 4. 测试AI分析模拟
        results['ai_simulation'] = self.test_ai_analysis_simulation()
        
        # 生成测试报告
        self.generate_test_report(results)
        
        return results
    
    def generate_test_report(self, results):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📋 测试报告")
        print("=" * 60)
        
        total_tests = len(results)
        passed_tests = sum(results.values())
        
        print(f"📊 总体结果: {passed_tests}/{total_tests} 项测试通过")
        print(f"📈 成功率: {passed_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        test_names = {
            'yfinance': 'YFinance数据获取',
            'openbb': 'OpenBB数据获取', 
            'technical': '技术指标计算',
            'ai_simulation': 'AI分析模拟'
        }
        
        for key, passed in results.items():
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"  {test_names[key]}: {status}")
        
        if passed_tests >= 2:
            print(f"\n🎉 系统可以正常获取中国股市数据!")
            print(f"💡 建议:")
            if results['yfinance']:
                print(f"  - 使用YFinance作为主要数据源")
            if results['openbb']:
                print(f"  - OpenBB可作为备用数据源")
            if results['technical']:
                print(f"  - 技术指标计算正常")
        else:
            print(f"\n⚠️  数据获取存在问题，请检查网络连接和依赖包")

async def main():
    """主函数"""
    tester = ChinaStockDataTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())
