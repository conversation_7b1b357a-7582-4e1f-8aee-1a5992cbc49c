interactions:
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
    method: GET
    uri: https://api.polygon.io/vX/reference/financials?apiKey=MOCK_API_KEY&include_sources=True&limit=5&ticker=AAPL&timeframe=annual
  response:
    body:
      string: !!binary |
        H4sIAAAAAAAA/+y9a3PiSLM/+FUcfvPs7tPF1P3SESd2AYGPe0eicWMz0okTRKlUMmAufQC3MSfm
        u/9DXIy4CYzttruHeTPRtpVVWZWZlZWV+cv/PR/Y4X1nNDz//F//ez4c6cGoEemRPf98jiHGACqA
        2fmnc9uLUj8nyc8JPP90Hrc6rd7tyq8QApCcfzrXxtjvI90zdvrrUau78ie4hvFnKD9jEZx/Ok9+
        Gw/09E90r3evO1PiQ6M7je920OpH55/Py/7yh49WD+bkzj+dm9bd+edzCCEkGCKV/GjYMuefzwkT
        aEre3NlBwuR5Pv/1z/P//nRu+t3vuvfY6M0GzX//3rFnlz2TS77t3w+MbcyZux90zj+fN0ej78PP
        f/yhv7dy3/udx9t+L9fq//ED/TGwsR3YnrF/DK35Y/bR8I/lZECyXhAiyDdIx62OTdF/Ifnkl3b4
        h9bfOyBZGKgIbDRH3dy4O1vOnu6Zlu4Mzz//73mrZ/pd2xiO9Mh2bW+U/KxnR435zzv94bChR6NB
        K7wf6bBjG6N+47sezP/yh+7c2/PPKseVYvbfCH46v++1Ruefz6+/Oeefzjs6tAlXnh0li9rv2j/+
        7A+HZ/kUxbNa/+zrjOKn8/4gsoPzz4RBuFik88/nUWtgzagxsN/7g+Svxt/1qHn++fyPP/6f/+r0
        je6AZPv+r//7P/7l2dFsnGSYf53pXnT2/7Wi//hXDBBk//rv878/nff6vf53O9CjZOVTbKb4ASzH
        mf03lLvYSZFI87VkQB07/xTlGeHS+LvtDe0KL4rPWBnYH7Z3b9NTJzlJsEz2Au2Y/NXio6fJoiMn
        O6dUHvS7xX5vNNBmVG+NmsX74ajftYPS2HTuo1bvNj8c2uHQRjU9XmGDqxkboe3ZuDUaNkx/OBo2
        7IzjNFc4xxWjKoOrwpzGWTGhMR2ltKDzxCheYbSVTHnOZ6PV/X4/sol69Afd+44+/3x++Q1cTn+a
        aNFsuYdWD0yzoRMzaH/YTv97d1UTcE4plKUJV3Ma0xk6KRqp3SDHbseMdL4XpQhvkx9J5wuvhy3T
        sHrQS4xJYmEbw6Ye2BRDPId4mpWzP86mfzJMr31C5qw0J3P21Q7Ovk3JPPFE8ZEitiD61Q6mJKdD
        rem1nDEzE55ka44SoJnc5LfKDT9SbtBMbjbs6Q/d6iyMqel3u/1eYzjqm7tmvxPZwbAx3ZcX2dfF
        CIlxLU5HOPuWGuHT2XQhUwZXHMkiJAmHUatzP7LRXlEi+0TJmRHKFibySsI0H2xVnBCaidOCJf3D
        DvStbcynumQH5ZhEmFGxsi87OcrP6MyYSckWPfaoq9vWbXNkozlh774b2kElno83G6ZyPxqOdC8x
        wmtcihmX36duxcBGMwFsRK0frcj2opki9UdNO2joqH0/HCW2JM3+LlH8uqA4E7gzZ0FxqluVhOJZ
        PkVxKYLqWC0jC/Vv9OPG/Exc0X1EERF7dP+sEp/ND7TUnI7cm4RgJb7o96Nhvhd9s4MfLWOH3/qd
        VWETc9O1z+Hq9XsmOWD7nc7MaRnZgR2ODtiNfY6Xt0L57HJBebkC5MhdkWp5wmTpkKAUE5StQ7MD
        ZqcG0dfVoA3V2XLooPkJOrSzLbm1PTvQnanW6Kjb6rWGo8SL+2G3n0ZUEZxhz7/NyH46u5jR/TQd
        PL9CeMs5hSA6biXm481Hy/ei1aG2+hBzC7L0pbcwynJUrhnIVUYrT270Nm6O3Ncnoguaq84znE18
        rnAjPV7MvDF3RFcElAuKMhiYKddZTY8XHPwxd0VTbuex3s+MeE2P56TnlNf8H7zCz9SAxIN+t5Eo
        dqt3n2zNfJP6vWFDxyM7SLh+jn+RNiGJu39WfKJ9VnmifZZPaCdrkdpEeqxjQVela/tNDeUQJRBl
        2PZK9kUNHXv5qaxe0zbum4rMtuV2kGzI90F/Q7AUojJj4hfJh2dfZx8+zVceOd0ptRmxVTVGW4+g
        FzifqbPj2PsWFDOBnh1GT+r5JAwr911FiP03VDsld0ZkoZ2fzp42LqWg4mgFnVHfZiAFIs9SzNDG
        /YFd00yUQ0QQniEmB6pmYUp9TTePdT2XIr+4+88GXI43G+7JftlhaTwa6P4gavX04PFyZLvDVddj
        sZKrOjQPEHzXg1HLtL7PLMHQmvtBa9Syw0aUHFGJP5M46r2ocd9L/+TpNjJd+vVL1U4HNj3Y2ben
        wT6dOUvaU2f2Oj3a05VluhWbFyx5pC4Icf731JHqTGOow6a1Uzti7gcD2xs1Oi0dtjrTGa7IDWUE
        ZpmX4uz7sz9T3z/N9lh9SBGb0189qxaBh8Sh3Tt/hLPu694Tie0sHGsoU8SWQ6xysfAgZtejFC96
        OLSjVTY4IxBnHU/TC5HX74EFN/kZkXQs9BmCU3gSnCllsOQBzAn//ek8bo0ThVmfLc0RkRm8Kiff
        bU4QH7fSXweJ9Rs9fu3o3ijfi0r/c9+ahqw8u7bgnG+IzcbkcQ4qiLJueSmJ2WDhSGGZ0dklJ2JF
        TrIFXuYwV5me8lRQsrWWvkRS5qRBmvTfn843FprkGGaSZCz0+uoe62LN6Kyt6ZO3+8P2Rv3B40qA
        iSQ3yQxfYPHN8uQ/Mtb6RGpDVpncYRy27zvNQYj53n1PG4jt5g6/kpVY2/5V2d1i4IjAJOu5YVVu
        10UDv8i4FTcsm/2f+9bo8RUDKKUpwefHTdZ8q8M5QyKDj42XN57DiGaJz475rz+4IX6kjqajyrOx
        VtQBo7kNnDH0/JkffZMsrN0mtqsfzilIM2ODW7Xt2NVKEVtzKeZmLTXL2VPG+rLtt76pMWbPGevr
        eOzbZIpwvhft3fgFR/3ebWNkB91GZMPVGzBkCGZy0u/dguTTMyf5dGnrjoxyJfRqdtBNqK3eMxZ+
        aYado4TxrHvYTgv3osNvqxfN50urjenf90bDxnf9mCj2qnZxlBW3ys+/Pfs6/3bpRRy5uguKc4Jb
        Zy6m65wstB42G3Gn/7CZebD81fSyvIwAaTNq/ZipxvIGvXpXhoxmCZRnR2dFPWyelTv9h9lteRke
        yj+R/5S6Qx/7eFwsP5mfpzfAJ85WIqSCZ7gsKzPeGbU6fDJIbUxmtsyJPzVcW+YVsyMgO3Ses5W9
        XFBMrWwqaH98gkcyztdB/0crslHh8Xpoo8ve02DLsVYkjyC+jHOtsT5PiNnFOkA5KKnMuktv4b68
        ILqV+2Pv1zu4fxpsF/cU7uR+m369hVK9/Dqwg/mnwXYwj5Xcyfw2qd9uXF5JAbabluf5iinTQjb5
        2jr751iZ7VNEx5o/xJ6hdttn/3oauJ2354XmUstPZ2dZv/t9YJu2N2z9sPMg+jRCt+Xnr/2+W0yP
        8bKX3mdu8OXTKqjzHYtwePYgZ1mvoYczuZFHePT7fWrE2YCeHVXi9Qw2RMVKiOf4RQAkl1jZnSmH
        82v00Stx7I1lOu6W5UgGXSzJ1/5g1Or30lOo9WcTWFstetBqPUc29qzLLofjGeItMsX7VeR419n4
        jFkmpujvvz9tJm+jafI230jeniV1023J2xggCLB8WT42/vj52HiWMC3fKB97QX4tHxtDhenOfOyN
        d579AThIWdZDyc8NwO18WsAIZYW33+VpoRWRm8fQ6f/4E1/98Il7H7VLP4IL1a50b7pe/abrt6Om
        N7mkgVN9rDg3La97Cf26Pw5qt9R1Sg9u/Uv7T+J1/Mn3TtC+6QTdS+LXzGPlogrd9i3y20HLuyh3
        3fo1rTj5seuUWz6S1PumuqZb7lXaPvXr12NvUr7z2p12UKs+uN2bZsW5e/DrXzput/rgOl6z4lw+
        /kmumn533KlMafrEbZfv/K7/6E6qqFK/ZEHt9sGf3EJvUiV+9/oxqJXbi29Mt3wf4GuezD9om0e3
        ftUK2pcTz7lmfrvTqTiFdsWpEs+JWkHtS8ete7Fbc0fuNzj+s1YaeW2DXOe6ARmJMZQWmJAJQEmM
        QcipAVEcS01DFWFlDn/hkygzc+O9XvhOUpElFZP8hlRwHVuB4hhEzEpAMYqB1JYAgXnEJQkls3D3
        qw3NKZp1OXn7V5vThmdsuNeabbb7DWJvcv3gVRsR00QbGQICiQCUEQx0KCGwLEZcYMs5JTui/iwH
        k7/4aVH/zBApRZmv3e8QIj1JYpbpaW8xPRQazo0GMeIcUKUFCC1EQGllZUiQgcgekjuAROYj5s9N
        HThJQaZbcr0hBTEMtbY4ApRADCgiEEiKEIiwtCrWGoc4XH3g2eWTMKJkVrrOz8+aOglDpknYFAYY
        88gIzYCVCgNKlQWahQTEREOKYgiN3pWiseV2JRgmWdHvd8nf2pqTI1iWBXuznJyTfGYaK39TPgmD
        lgsGjJUGUKk5CI2MgLY8Mhhahqh5RjJA9rZ/hGSAk4RkudfOFqeGhFDyiAIrjQUUJU5NaGIgpBTY
        QEhwyNL+9T8n3enA68M7pTudBD0zcHC3IeiRYHGkkQKUkAhQGmKgmeYAYoOZlcrEKNybmEMYhVkm
        8Gcl5px2P2P3K982ogg0JppELARxiCCgscVAImmBVCEUkYixCMVz8qeV5B8xf3r7dEkOYnhoGt9b
        5R2eJDbTXpW2hL8jpcIoBjCWKLlnSqChJEAwzAliMbfKvmmCG8aIZYbMP0iC2+48DpX5QvouiSh7
        M6BeadXfKQPqvbScuQst7/rjING8+lWrUgva/uSq4028plv70qnUblpB7Y66zuXYbV/dPWl59xK5
        tfyDVwvalXoV+u2rVuXCa7ldr+m1Lx+D7iWt1G46gVNurmu5374eu04eeV2XBc5Vy7u4pD6+fPRx
        CbvtL12/fcu8+s2dWy/Hbu120ytRYWREyAFRBgIaaQOkthhwITBHccg0Mlsyyo4X9HfM6wQ4hwmj
        h870w2V2noQ7S7jbm8JtkUHGxAhoQhSgIhZAhywGNGQiMhoRKtCzMmV3mnoERSbg0rul7L0wE/TV
        VObtc0EPTG9+pZ16p/TmkwnIMgGTTRMgjYkVjjDA1FhATSyANKEBjHIMrRE8EuHci92GDnkIWp9g
        GGfdyX8GWt/vA221G6mmEc1ntHoTzsKu3A1Y8+nMWVBb7sGxp/mC1H4Im5P+ZuhvpbgRNxHEMsUh
        BpIjDaiOKZBGMSAFhEghJQwzz4F3ZB8Y3vG9ZINUlhEK4tVc6LXvsFuvTvxJdex1g1bF8bpBrdAK
        annidpP99ekyQlGaeBc3HbdWxe6kc+dNqg+eU2j5Ne8uqF2T4MLruhdVWnEu8bpsuDUXBvXrsef4
        k0q9SjwnD92LL00XX3W8WnTnY/8hcG6p2w62Pn7GWECDIQSxJhBQIhTQ2mggiMCGSixkRJdwmbvw
        8hL/JrPW8V3w8k7CkCUMtc13JIE0pmFsAQxDCKiFGkiMNLBWCyIMtgqxZ2GhKfJxkdBO0pFpKjaD
        mRGiEYkhAjqCBFAUExAaK4HFWlOIOLaLc2QPxB8iNCuN7qdD/J0kIdNObGYkhIRjRKUCBsYUUKIR
        UEoZoFDMEGZUQa32gNkJKDLzpn4GmN1p3zP3fdMCIEFhhDEBjBIJqJAIhCFTANI4jEREGcN23QLs
        hGFU2TAa7wPDeBKJTJHYjA3oUMJQUgQEVBxQLCWQEYbAxsYgiSw3JH4VVEOVDf3x+6IankQy00/Z
        9GIVVCGLJAGxpCGgghAgKdVAKE6iUISxMDB9380Am+YYMcXJLwU2fZKXTHnZTCoyGoWWUQa4IQbQ
        mCKgGCdAxxLFFmtGw+cBs25HTFYS7r8NvQNi8kFNSjhmWeBAH6BJyUnsM0/u6mZUXxodxxIDjBAH
        lAoOFIUMcBLDWFopjWD7oZ73SPVbQT0f0GyDE8wkUr9cs42TIGcJ8pZqUoppRAyKAMWKAoojBjSy
        FCgRwdgkMrBoXnIQQDjOqT2goO8DEP5ecjF2nbsfQe9LM5j0oTu5o/6k+ujVDPEnl9Crl1t+O+i4
        F5fE65bGfvcae/XLcVBU0K9/GQa1/sR1Lh+CWom6F1ddt1aFgePTivOl6dbLzUqt3PTryfxd7H6b
        f/PXl2bYjTqVtvvgtgsdt3b7EFz4Y8+5uvPat9RNxqmXWPJ9UPeRP/HHf00uN/1AKxgUsQAxYxZQ
        I2OgKWGAMy1FqAll2hzUCWrf4+QbdoI6tE8S+uB9kk5GLdMp3QyxRZyqiKMQ2NAYQDEnIFRSARNz
        bLBhSBl0eHtCkiP0w7YnPMlGpue2eWGxxGiLQw64tBxQHCOgJbNAxIIqjrgWZnuTjmMayT3Pwfvp
        jeT+6Z0efudeYFu7lSpKcBaw30fpVnqyahlWzdus7uE0pJZhBYQOI0CZFSA0KgIWayJ0FBuC9cta
        qDGospIwP3wLtZNEZZ6Tm7ktIePSxjEFPLISUIk0UExyEItQ0EjqkNjtEY5DICifey7+7AbWJ2nJ
        9Lg3r4vYEkulkAAxRgENYQgUlAYIGjELLRfIxotarR0Ari8H9UQ5hDPrlX99WM93KyJ0WwvBLCHP
        ubmr1L1WpV4du071Ieh+abo1f+y2q6RSDzqBcz0JpuM8CSb22522O/nSrDjlrlurTipOibo46Pjt
        Wxq0zdib5ElQu2Obghm0KnV34nZL1K37Y89xqVu7nQTdm1bFaXa9SYm4TtSp1Dpbsy04VZAoxgAJ
        FU8EkwBpYwqUZEQKFoeaP6Xo7QcdlTnJSFYs/41BR5+D/PsbwBwfjly7d2PeG7l2v/k6XrbeGZj5
        ZJMybZLZsElEiNAIA4FWggNKEQOKcAhiQwhCCBIRTTPAtkMMwymUsNiAGJ5BD7NtEMNoCjGsXgYx
        jD4+xDCaYQCzN4IYXpBfgxhGUGG2E2J4W1FPZoonzREpP1QX5wwF97pB96btdqvIn1wjd1JlbjtP
        K1OUgVvoOlUY1Ly2P7mezBW8HXSDjj/5cufVXZR4uJXa5UPF6XTcmqGJ1+ldXE987CI3jVzQvWRB
        Ler4tcvHoBa13MlVM6jdsorT6Qa1qFmpu4+VixJ02z5cKvgtcetX7UrNJe6kfOfVqg+BU6Ve/aoT
        tDtNrxa1g1pw59fch3UFrzhmHFy4zJ2YiVuvIs8p3HnOLQoubrqVegl52Ot43aumOymsOB1uvmEF
        Q8QKBqDiClASIRByKQARsbU6pDrmB+fPCLjhz/7C+TMnUUnnzySiQk3IpLSAMEMAlZAApS0DXCMh
        kQ2RwuJlQRukRNY1+1cO2pykKR20cfMNTVisQyIA5woCGscRkBZKIEJrJYw50WgOxHhI+ofkVP1e
        6R8ngUmnf7j5BlSKRBHXAJkIAsoiDXSMCBBMK8XDUEcQ709holy+QwbT7/tsc0AVKctx8WtWkZ50
        MF1FmrgAUmmISAwiG8WASkSAjpgFXPNQU0K53aGDxzxGP0tVf/pb9PPK7gHNCUEzE81+ncL7N1UL
        5j0uoiS3xHNuuu6k+uC3feq3q7BSL7e8ts/82k3XmzQ7rnP76NVdklKLB3/idSv16kNQu2lXalVa
        qd3SqUjXvrR8XGJ+7UvbxZdb1KLQdrHPAqfQ8eou85xEBW+Jj6+R2/3S9mudptt1J367kC68f2ho
        FUcmtok+cA0oiQ2QEgqAraFa6TCKFg0uTvkLByZlcprVTfEDJmW+qUqM3dr1PCnzeyuo+9C78FqB
        c/ng13xYqRU6lVqn63Yvx277mvnt0ji4KD2mkjIfXedLq+JEreCi3KnU8tCtXT9Uatc0uPCh65Tb
        br3cqtSClv+4npR5+eB2L6mHv9z59WvmOtWxV8szr9a8C9rVB7fujv36JfO6X9rppEw334iRimKK
        GYiIoYASzoFmCAITKU0V01As+hT/PkgsRzwsP/OM+0DvyiffKP2u7OYbhMYMx9gCxkMNqCARkJBC
        oEMhdYxiFkdziT8EI4lxngUq+TMgkvaX9UKpMqHBPlxZ70lo02W9iZk204LeEKCIKUANF0AbLYEI
        Y21oZAXh87T5rfl5nEmUVdf9C+TnnUQilZ/30JBYhTwiBkQ6goDiOATahARojCMkrSWSxs9ChaEM
        Zz0OfVxcmJNgpHFh3HwD25CpmFsgKQ4BVZQBRZUBlksiNdFEmnBhKw6oMkUKZWVofuwq05NwpKtM
        k4MkppCjWAJJkpswUwaEMTYg5jA2imMTSXVQ/dUex+cNy6+yMM8QVnLftD4U5tlJQNOYZ26+wVVs
        rRAQQBpHgMbWAMUlT0xYpKIYxxCTPTBGDEuSBXz3zjBGpy1Pwxi5+QYjRkFuI6B0KABVmoCQUAaw
        DQkJFbQwfh7Www64GqgwzPKC/5FwNSdpTMPVJAaIImwgJSCOE2mEwgBNtEmkMdIUSYiZOLjIE+eY
        /BVrPE9Ska7xdPMNoXkUUQRBiJgGNFIQSMJjEAsq49jGMZN4NQ1i31vrL1wdfpKOdHW4m29opiUS
        LAaKKAloqCEIsWVAUxvDyEKuoXwFtKLsKPQ7gRVNkws6umdsY9i0duqK/S4dDbd1uuczk/4hG92/
        7YvvE9R6++bOq11jz2m23EnhznWqY7991QzaLvIcr+Pi64eKc0n9dimlmIa59ZuOe+HDSu0W+u0q
        CpxEIfMPHnZZpe4/unUfet3SZKPh1+RuHNSvH4J2lVWcq9YsJ7+EXCdP/MkdC9qX46BdxX5tpdH9
        QwNJayQNERAxpIBCZIAUNAQ0hBZzYjE1Yn9nQpp9m/gAnQlP215Jx0aRCiGjoQYGYQSoDEMgubQg
        joxBGDLO4DzGsa0FM4IwCwP3PVown/Z3LRsehVAwYzFgMsKAMm6BtJwCHKswYlBItQDIP6g7OBc4
        S8PfpTv49o6TOCeFQlny+b4dJ0+Smu446eYbVEZhyGMBhDIS0Di5N8jYgMgijIjhEZY0swv0RjIC
        zxGofsHmySfRSDdPdvMNrhGUnE7z0TigiEkglbKAhjaG1sQx5Ghb69wtFgxLzPZbsJ2eyousV3HD
        dB3S6VfkoEBZt5n36vSbPW2Uw4xmvzBkzvjldvjkGD5P51YTo6OQamM1BJoSBSgjIVBRpIBFoY1C
        ZgmR0VNwb6fC4RziiGcp3PJI3/QVXuQwLgmfJOBQ13FFAjgyVNgQAS6QAjTUGihlIhBrYoiSGtt4
        jj+jjenf90bDxnf9mJyiK+E7KnhWCVx+/u3Z1/m3SxNwZJHbguKc4MkMPNMMrMT4BeJMKSyAhDED
        FMYSSMkRoEgaJVmIqBYbZmDXicAxJZmNpZe2YOuhcOzDY4rYySo83xdbEQiFJdaaxYBSxgElSABN
        DATCMMiptSQU8yfIlBTMkiOmLvCzQgipjZtlSMxILF3zYx99UoTzvejkqB8dRHRWrYVSMJQSAgw1
        BZQxDbRADBirOcSKiniBgrchDIdd1o6OxBfW6hfj1thGm/4KySmalcNVTj7bdFTwcVL4ddD/bgej
        x68d3Rvle1HC4zRz6xTGfobT4q6YJ2YwN1EMrAkpoFgLoMMwBBayaFqEbRh5GQzayjsTwzRLaH8S
        5tGLgdtYjquMx/ffGrXtbZ9oK8XFE+31Q+Xi+tFru9hrX5LAuWpWnKDpdv1J4FzCinPT9Cflrl8r
        pWr/om5Qu2n5OGi7k5tOxaliD5dYoj5BzbsL6lddr35J3NryWfdJhXDQ8epf2kHXnXj1a+o5PvFr
        d8SfdDouvuoEFz7y22bsta/WQ8ZKRYIKiEFMOAQ0lByoMJbA0BDHEaU0VHoPYNvvCIX2AvXaayd+
        QWCxk9qkgcXcfEPCUEMVMsBJFAPKiAChwRIgbSXSMgp1HB6Oc3i8yLwOzmEyTT1sNuJO/2EVOqtn
        R43lr6aZGXO8rd5tQ5tR68f6xQ+oHCFsH9xsUQ+bZ+VO/2GWklFe0DzLL2m+ODzo2VEyztdB/0cr
        slHh8Xpoo8ve02DLsT6AFvwShePuZKX8isc0RghHQEJmASWYA62FBoJEikbU2AiaJZ7Cmhy1ej/s
        cLQqR6m0n1WkWcoy2/BuEanLBfmUSH1K5f4sxet5uTDF8iqQwgpnq30sJM9IiFmZ8s6iwsNng9SO
        dV7mYm7VV5SDFJIslPoti7usjNymr8e+/e/Q16fBTvr6bH1dLZckXNkoRDEgkCtAw5AAiQkFMEZQ
        xJiJUMid+rpNjrbr66uJ1HZ9faZnt9RXvIOzbSfaDkv0Wofbds6eB06R4ow+w8a+hWHddbd9se4/
        DXbS/WfrfntF9w0NLZYhBjQUENBIWqClxiBi1CJGJBWIbdP97XrwnMNtu6yjY9UYsR1QvUhNIXnV
        BlTvDMKXb4PqhQBBQODLoHrhx4fqhSD5l+JvBNW7IL8G1QuhwnwnVO9GenR2OpqESGR1tvswb8uJ
        2t+O/folqlxcw6B91fVxuVlxzGNwcdUKHPchuChB9+J6vEThNtRNzEat2fEufBh0vaZfuyVu9+rO
        71Yn7sVV03MKHfcmHaa9fJym4tevJ+5F6cFtV8eBU6Wuc3Xn4auO3y13govgzm/fLU1P96rltUsw
        MU2Bc538bdvDNy0XXz9Wambs1stdz7l+dGv+RsuK4OISu7WrplcP7rza7YN34ZKgfjkJapfMm5TG
        ft2HXu1y7K01OEncDimI5QoDZBP7gxkGMgoN4Cbk0lgqqdCr+ca73hQhIyrrCelDZZmc5GA9y4RD
        ooyNQGRQCKiICdDMQKAYChHEipJIPDfpjxHyK2b9nWRjLeuPhIpLrDnAkdSA0piD0AoIMA4JjzGS
        3MaHpy5TQmEmss97pC7/LtU/GQlANIex4r9MAtBJC9cSgLThAhnBgNbKAEqJAaGAFkALqcEK64jB
        RcHeZgkYzUGe3Y36HWvATnu9UgMmGEzO2yi5/zFAOYmAjrQGTHMklWZQGnxwqhcjiGUhCHzAVK+T
        OKylesWIU4EFB2EUY0ARNEBpJgFBRCkCGbKQbqR6rZX0s0w4vfctwTlt+FoJDtEwFlZJYCUigCa7
        LqkyAMGIoihUVCCzsxwQEymzArzvUQ542uG13A6BYhWFzALJpQQ0ogjIWBMAY46IwKGJJN1WSbNd
        wac5e1m+3XuVpuzM7uOCZ833o6X3neR3Pb0Ph5jC2ACkpnEjy4GMCQKEShNhi0Nk4gMrwYjiLCtw
        9HMrwQ5Ilt5nYD94svRJlteSpRlEhloRAsOIBZQLCGRoKaASasmtZKHkezEXKBGIfGzMhdPGr2Au
        8CgMVYQooIZQQGMsgIyUBoZSwU1MDNd0Z478QbHNV0qSX2LwrOR/nVo+HIQTrHI8G/zmo6IEv6m6
        LiGr2p1m0K3CwPGh67gPXvuq7XZLzHPyY7d+SYN2Ofk3DtoGLZ/Jg7ZbK7Qr9SoO2reTRP3ci5um
        365Sb5Ife+0qrtSu7ir10oa6el137F64LHCCbsVxx95F6SFI5uzcEtcpYc/xmXdRZX4tWEcJhkaq
        kAkFMAopoDYWQFouQagpwwxxQwjcg7MJqcqsOn9nnM3Tlq/hbGoFFSKIAzmtY+JIgTBWFiCMIwyt
        xjCOlti/2yF2UY4rxrKiHx8OYvckB2sQu4JoCrW1QApBAY1QBBSyEESxsJZyFJJl8Pt4vFWeE1Bl
        9R3/R8KtnoRx7SWGRrGOcUyAtojNm5bDWACsCI5tjEIekgMbGwvCMNrXXfTXaWx8EpW1xsY6IphY
        i4HBnAKKYwM0i2PAkUVEkNhowl/S2BjllEJZgbRfubHxSZrWkrUiLGHMIgt0hBCgHIZASiWBjRDR
        JoxDpOHBbTJQTgqGf9k2GSfhWGuTQSPDLBMchBIrQI2KgNZIAEwF1pwRqLU+vEkYFBTTd+4S9vu0
        7zsIYJvksPx1AbZP+rgGsE0jJTHTGMSWC0BDhoBmWAFtMYkjEiMbLl5Inqp3tqgiyUmeiaG9LM7Z
        doy/tA3eguZJCA41yqvPvEJQE8cRwARiQBXjIOQCAwM5t4bhEAqavirsMRBkLwL/x+x2fpKLtXRr
        EjEBQxqCiBMJqIooUFiEgAsaRowrLhb4PM9sBo5ziGUAiPxKvcDfVGjGrpOfNz7uU3dy9+jXTLKB
        1K/dQndSHVfqQcftXrU8J2h7tWviOVXmtp4aH2O33WwGdf+h4hji1S5hUPfHFcdruhfXLHCuk03v
        uNgfexuNj/3HykW5E7Tz1JsY5LavOkG7RF1ceghqnTuv1ml63etJ0C6Rv1ZLukPJKbQmBFpMGyhy
        BDRmEoQMWhFHwiJsDmqEts+5e8NOaC9oJsFygqJXiM69djeJ/d2Zj08j39emeW8+OSFHcibVM5qq
        S0F+rabqb2xb2tVFU/W23/5y501KsOJ02n67BN3aVcfvXj+47cspPffCJ+6k2Qy+PdkW4k68u6B2
        O3bbdw+V2iX0Lrym374eu9hF7sTreM418i4ux5tN1c3ExVedilN6rNTyzG1XoY+TOZcm/uR64uJr
        6NWuod8urzdVx1FsuGERsCqWgOIoAiqMILAMhdwYxsI4PriFlMxB8iu2kDq5KWstpAyDBnFlQcyE
        ABSFBGgBKYhNbAWDLIQ2XL/DbBcJnuNYHnaN+SANvU/CsIZQocIwxlZjwCMIAbVhCDSPQhDFVITS
        MmNJtKxSXzkPf+hWZ3EYmn632+/NQjrz7LWNLJS9R/3GubgYITkUi9MRztL5cZuZJ+LYE5/saFmO
        c4IylFUF/Qu0LD8J/UrLcm1jGIVEgthKC6iNIVCcIIBDGUODYUSx2CHy+5Eenyvi+wDojn1g9uzo
        ZBuff59fcZ+gJUqI2AJtpi2AtQaSMAxgaDUSmmAoyVZBeYFErOTFH2nMRDo6nfVOzbDEa03Vdwan
        d75UHyuhO16q5+NtPFifZPhAGV6tMmMyhFRqCuQUgYoZAXQcCwBDgRQOFYtRvEDsfREi4Q7cGpnj
        Mus98mPBN+2BbpM5yEUWgPUvi9x2Csv9NSnRP5/yFPONGFIRsdgCopECFDEIQiExQBGFiY/AVWyf
        BbWY1gqaw1IdCuf0K6GAneTor8nlihyZ2FLINQEslhrQiGOgNYmBRUZDKYTFlL4CBOBrmaa3RwDc
        hTgKKTkYGO8NEM4ygEWfN7e3hBbdBwX8Sqftx0MCPtmVvybuil3BNoI6siFQcRQCSlEINFYRQJYq
        aCOFoeavAQX8SkfV6yMBZ/SYOAQCPLmYZQKLvjEE+N6+Es+Z66/SCYPmsP03FP/ERhhvezN9QvRv
        B02vXoVB3X3wJ82uN6liv+5Tr/7lzp24zKuVHoN2s+3Xyt3UzbQV1AqtILF2F+W7xLr5uMrcbtAO
        HJe49eQ2e429mr/ZS6b7peNefOlUnOqDX69Cz+m0A+fuMeh6d+7EjP26+xjUfOS1N3onW4KhlSYC
        mmMCqJERCKOQAYEUkrFgUi4coxe1XjlWyz9ub4iTJK31hoghsVqoGBBtDaDGKiCNQABHkhohoVDK
        /ONaqmxH7JUAqhn87gpi7xzJV25B7EVqitiLXoTYi9SHR+xNlgBCNJ3pGyD2PpFfIPYiCO6SXyks
        k//tBO3dWiT+2+RQ789aRzlFKckCO/sZWesHlV5wjMTHLr2IkGAYcsKURRQz8HAHytqMgCgVOVNF
        VlZlSSQpOEWGVREj5ZSlKjmFzAJslZMzkOBdjP+M+us0Z5jSJ86KhLN8GUmVlwWMCkVeKLMpZyVO
        ynmMD010ICrT/36XPIc0z5zjJ56ZKGNZdspUCCQpQgoWSjOeGS2UVeGoHFSSo79SCup0bRAVhFkE
        OXpaG4cJR+U5LZVYERMhJM7DuaTTshD5A1It91ukN0y13FN8SnMcCUnoyoX9o9eepuQYEwyf9irP
        8yUKhZSEFUsUK1iA5bkclzkql2Z7dVAZDkI5qT54HU56GRBdqnMRk3Le4VjlRUmKAuQFqsolShRl
        kKP89ifo3yhdNRMEAgmZiU//LiAQacOM5PIwQoVikZUpLymm8rQkKOJkupOkIItlWT44FxPlJBRZ
        Qbl3TcZMsy/w0vaWCizvlBSHxEn+K0nG+PwsZpQV0bOwHrYnkzPMsgqn3y2Z/ATrlJUis2fX3ipF
        Zmu+H4dIZJVRfJR8v5SSSUmWnnyRl2BeiXyBUpp3uCoLXC6RchkxBp0yew08FSZI1j3n98VTWTFs
        cumoFIolQUVJFGWRFojikpXRzLDBEpTMObgYEKGcEh+4GjDtosCU1DlFSEusWEZOgTolVlQQo5mL
        IhAul/KrnlqW90qlQiTbe/2AGWkrHqxQT+uCCwThMlOqyFAxj5lTovm5YCDGFT86zfR5FvPnp5mm
        NUXBpS9L82UmS5RjUS7jvBJUSjJ3AUQBsdJBtdOU8qxww0+vnU4zy+hSK6iTF1RiXJQO41IRgkR+
        EXsQvLz3Hr4Gcif3l8m9G+JhegkkEUuPtwRLDlECF4qUYiqYUxCLJShBqF6ttOC5CvGTSwsOKrkj
        OSaymvy9V8nddHOxEAxahOnSn8eSlRUqcUGkU6CUF/KFeWyNQCZLzsswkCTObJT54TGQVmKtqUug
        U+ZlXIRFWCiVGS3zAqZqfiZIXCgX59m4G8369kJuM5kNVvxzIbcP7SimEsuWVUP2rh3FZpuomKAW
        IyxSpp2VlUDMgbxYzucLQjA6DyNCSIvscJR/llM0M2frvVD+tzccQjDbQr11w6HUhki0fMCAGJWK
        mLOiUyYECiUo5YuwLka0OO+rkdVIi2PywRpppXhFRCyFT6IyQThPEC86vEhLeaQWwoekgHhnExEi
        GcoCyn2zJiJpTnBq25CDHOpw6FDBuZISCrTwj4v5fJFk97+hEuIDIfvfqv9NmjEJ+RNjvEAZKxby
        ZVimkDoYkrxYPKihcql4eGc9Ign6cJ31dnYAEURk2fJ36QCS3iOolu55kWEiSF7kBS84hHBRKhUX
        lzOhZHZnzF+uieABLTD2mYeP0AJj5UAWKBVad8qOw0qFEi4XC7xA8hzPN7MgCS4e2u5WoCyL8vPb
        3aaFl3G5jPMxqfJUFUme5ZmgCJY5nV+silTmC3tbW3AsM+3Kz2ptkWJQkWWgwMFE0JIoFRwki3mH
        oKLz9FaQLzC8o4fDgd7kK/Vw+LSng7ZgXB3Ypu8ndNBOi1JadbgokFKBKlIQiiqnQMp8bgc5xZwX
        Du5KSDFBH7ApYZpvwUXqIobytMxK+RItU1iGsiDmFzEumZq5UYeWRW7Nncc5SvbW/v7EmpbDakhU
        DiqxL5bycYtIUokfWMqUwSxTpERJinLBwYJCRHFh7pExXBJsZ63EM4pdX2vpflK1654qRZpjMrP/
        3MeuUkwLAk491qiCKBDMhRCFAswXaZkV5ncOBLGE8BWKZl5r5V6/aOaFVYY8p0hmL4UPU2V4SDn3
        a3HzTuXc6RQ3Lpb3GlIuoALMC8UZKgjB8gW5sHSqTIjaIuBHH1qvVOyYUcv14pojnBMyq0n2r191
        NHNwBMfJHYGloiuUSlHKM1YqyQIqQidfWli65MgrrQQpDypDkzDz5f8nlaH9g6o4Xlj8tGe/Pmrx
        U1qgOVIpgS4ViMMdKIWDy4USzJfnCaGIFiV39hT5vMrSvE71599///3fn86TC8X9MFGc///80/nA
        /s+9HY4arej887nhsbDKRiISikQm5mEodGy1RIgrDs15YsHHoz11L3+lClOWZSX/r7kfDPuD//D/
        yivXyT+4jvvoOiXmPl5PqrXLB7d213Fh+aL+LWiaCezqOmuH/3lzF9yoSUhuHn18M/n619VjVL/u
        hhedkf6rqrxvwUPw15fvIb6KQxzEpnvzEJIv8C981Ywuru/D/7zqfJ3RHrnO3cht33YNVo+Rg7K+
        60YXnbbGN49fr8uF69JD8u9RUA8e/TrqfK2X78PeTTPMn//9fwIAAP//OE8PV3RqAQA=
    headers:
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Type:
      - application/json
      Date:
      - Thu, 27 Jun 2024 10:14:52 GMT
      Link:
      - <https://api.polygon.io/vX/reference/financials?cursor=**********************************************************************************************************************************************************************************************************>;
        rel="next"
      Server:
      - nginx/1.19.2
      Strict-Transport-Security:
      - max-age=15724800; includeSubDomains
      Transfer-Encoding:
      - chunked
      Vary:
      - Accept-Encoding
      X-Item-Count:
      - '5'
      X-Request-Id:
      - c6f7e9ed7d793dcf6bb7afea8116960c
    status:
      code: 200
      message: OK
version: 1
