#!/usr/bin/env python3
"""
DeepSeek完整配置指南
包含充值指南、测试工具和备用方案
"""

import asyncio
import webbrowser
from pathlib import Path

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    🧠 DeepSeek AI 完整配置                   ║
    ║                                                              ║
    ║              您的API密钥已配置，但需要充值使用                ║
    ║                                                              ║
    ║  当前状态:                                                   ║
    ║  ✅ API密钥: sk-090bd...4200                                ║
    ║  ❌ 余额: 0.00元 (需要充值)                                  ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def show_recharge_guide():
    """显示充值指南"""
    print("\n💰 DeepSeek充值指南")
    print("=" * 60)
    print("1. 🌐 访问DeepSeek平台")
    print("   https://platform.deepseek.com/")
    print()
    print("2. 🔑 登录您的账户")
    print("   使用注册时的邮箱和密码")
    print()
    print("3. 💳 进入充值页面")
    print("   点击右上角头像 → 账户设置 → 充值")
    print()
    print("4. 💰 选择充值金额")
    print("   建议充值: 10-50元 (足够测试和初期使用)")
    print()
    print("5. 💸 完成支付")
    print("   支持支付宝、微信支付等")
    print()
    print("📊 价格参考:")
    print("• deepseek-chat: 2元/百万输入tokens, 8元/百万输出tokens")
    print("• deepseek-reasoner: 4元/百万输入tokens, 16元/百万输出tokens")
    print("• 优惠时段(00:30-08:30): 享受5折优惠")
    print()
    print("💡 使用建议:")
    print("• 10元可以进行约500-1000次股票分析")
    print("• 推理模型虽然贵一些，但分析质量更高")
    print("• 建议在优惠时段使用，成本更低")

def show_cost_estimation():
    """显示成本估算"""
    print("\n📊 智能选股系统成本估算")
    print("=" * 60)
    
    scenarios = [
        {
            "name": "轻度使用",
            "desc": "每天分析5只股票",
            "daily_cost": 0.5,
            "monthly_cost": 15,
            "suitable": "个人投资者"
        },
        {
            "name": "中度使用", 
            "desc": "每天分析20只股票",
            "daily_cost": 2.0,
            "monthly_cost": 60,
            "suitable": "活跃交易者"
        },
        {
            "name": "重度使用",
            "desc": "每天分析50只股票",
            "daily_cost": 5.0,
            "monthly_cost": 150,
            "suitable": "专业投资机构"
        }
    ]
    
    for scenario in scenarios:
        print(f"📈 {scenario['name']} ({scenario['suitable']})")
        print(f"   {scenario['desc']}")
        print(f"   日成本: ~{scenario['daily_cost']}元")
        print(f"   月成本: ~{scenario['monthly_cost']}元")
        print()

def show_alternative_solutions():
    """显示备用方案"""
    print("\n🔄 备用方案 (充值前可用)")
    print("=" * 60)
    print("1. 📊 基础技术分析")
    print("   • 使用内置算法进行RSI、MACD等指标分析")
    print("   • 提供基本的买卖建议")
    print("   • 完全免费，无API调用")
    print()
    print("2. 🤖 本地AI模型")
    print("   • 使用轻量级本地模型")
    print("   • 分析能力有限但可用")
    print("   • 无网络依赖")
    print()
    print("3. 🔄 混合模式")
    print("   • 平时使用基础分析")
    print("   • 重要决策时使用DeepSeek AI")
    print("   • 平衡成本和效果")

def open_deepseek_platform():
    """打开DeepSeek平台"""
    print("\n🌐 正在打开DeepSeek平台...")
    webbrowser.open("https://platform.deepseek.com/")
    print("✅ 已在浏览器中打开DeepSeek平台")
    print("请登录并进行充值")

def create_demo_with_fallback():
    """创建带备用方案的演示"""
    print("\n🚀 启动智能选股系统 (备用模式)")
    print("=" * 60)
    print("当前配置:")
    print("✅ DeepSeek API密钥已配置")
    print("⚠️  余额不足，将使用备用分析")
    print("💡 充值后可享受完整AI分析功能")
    print()
    print("系统功能:")
    print("• 实时股票数据获取 ✅")
    print("• 技术指标计算 ✅") 
    print("• 基础分析算法 ✅")
    print("• DeepSeek AI分析 ⏳ (需充值)")
    print()
    print("🎯 建议操作:")
    print("1. 先体验基础功能")
    print("2. 充值后享受完整AI分析")
    print("3. 对比分析效果差异")

def main():
    """主函数"""
    print_banner()
    
    print("请选择操作:")
    print("1. 查看充值指南")
    print("2. 查看成本估算")
    print("3. 查看备用方案")
    print("4. 打开DeepSeek平台充值")
    print("5. 启动系统 (备用模式)")
    print("6. 重新检查余额")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择 (0-6): ").strip()
            
            if choice == "0":
                print("👋 退出")
                break
            elif choice == "1":
                show_recharge_guide()
            elif choice == "2":
                show_cost_estimation()
            elif choice == "3":
                show_alternative_solutions()
            elif choice == "4":
                open_deepseek_platform()
            elif choice == "5":
                create_demo_with_fallback()
                print("\n启动系统...")
                import subprocess
                subprocess.run(["python", "complete_project_dashboard.py"])
                break
            elif choice == "6":
                print("🔄 重新检查余额...")
                import subprocess
                subprocess.run(["python", "check_deepseek_balance.py"])
            else:
                print("❌ 无效选择")
                
        except KeyboardInterrupt:
            print("\n👋 用户取消")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

if __name__ == "__main__":
    main()
