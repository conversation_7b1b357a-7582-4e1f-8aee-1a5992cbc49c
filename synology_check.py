#!/usr/bin/env python3
"""
群晖智能选股系统状态检查工具
检查所有服务是否正常运行
"""

import requests
import json
import time
import subprocess
import sys
from typing import Dict, List

class SynologySystemChecker:
    """群晖系统检查器"""
    
    def __init__(self):
        self.services = {
            "nginx": {"port": 1750, "path": "/", "name": "Web主入口"},
            "postgres": {"port": 1751, "path": None, "name": "PostgreSQL数据库"},
            "redis": {"port": 1752, "path": None, "name": "Redis缓存"},
            "ollama": {"port": 1753, "path": "/api/tags", "name": "Ollama AI服务"},
            "backend": {"port": 1754, "path": "/api/health", "name": "后端API"},
            "frontend": {"port": 1755, "path": "/", "name": "前端应用"}
        }
        
        self.results = {}
    
    def check_port(self, port: int) -> bool:
        """检查端口是否开放"""
        try:
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            return result == 0
        except:
            return False
    
    def check_http_service(self, port: int, path: str = "/") -> Dict:
        """检查HTTP服务"""
        try:
            url = f"http://localhost:{port}{path}"
            response = requests.get(url, timeout=5)
            
            return {
                "status": "healthy" if response.status_code == 200 else "unhealthy",
                "status_code": response.status_code,
                "response_time": response.elapsed.total_seconds(),
                "url": url
            }
        except requests.exceptions.ConnectionError:
            return {"status": "unreachable", "error": "连接被拒绝"}
        except requests.exceptions.Timeout:
            return {"status": "timeout", "error": "请求超时"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def check_docker_containers(self) -> Dict:
        """检查Docker容器状态"""
        try:
            result = subprocess.run(
                ["docker-compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                cwd="."
            )
            
            if result.returncode == 0:
                containers = []
                for line in result.stdout.strip().split('\n'):
                    if line:
                        try:
                            container = json.loads(line)
                            containers.append({
                                "name": container.get("Name", ""),
                                "state": container.get("State", ""),
                                "status": container.get("Status", ""),
                                "ports": container.get("Ports", "")
                            })
                        except json.JSONDecodeError:
                            continue
                
                return {"status": "success", "containers": containers}
            else:
                return {"status": "error", "error": result.stderr}
                
        except FileNotFoundError:
            return {"status": "error", "error": "docker-compose命令未找到"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def check_ai_models(self) -> Dict:
        """检查AI模型状态"""
        try:
            response = requests.get("http://localhost:1753/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = data.get("models", [])
                
                return {
                    "status": "success",
                    "model_count": len(models),
                    "models": [model.get("name", "") for model in models]
                }
            else:
                return {"status": "error", "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def check_database_connection(self) -> Dict:
        """检查数据库连接"""
        try:
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "postgres", "pg_isready", "-U", "stock_user"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {"status": "healthy", "message": "数据库连接正常"}
            else:
                return {"status": "unhealthy", "error": result.stderr}
                
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "error": "数据库检查超时"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def check_redis_connection(self) -> Dict:
        """检查Redis连接"""
        try:
            result = subprocess.run(
                ["docker-compose", "exec", "-T", "redis", "redis-cli", "ping"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and "PONG" in result.stdout:
                return {"status": "healthy", "message": "Redis连接正常"}
            else:
                return {"status": "unhealthy", "error": result.stderr}
                
        except subprocess.TimeoutExpired:
            return {"status": "timeout", "error": "Redis检查超时"}
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def run_comprehensive_check(self) -> Dict:
        """运行全面检查"""
        print("🔍 群晖智能选股系统状态检查")
        print("=" * 50)
        
        results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "services": {},
            "containers": {},
            "ai_models": {},
            "database": {},
            "redis": {},
            "summary": {}
        }
        
        # 检查服务端口
        print("\n📡 检查服务端口...")
        healthy_services = 0
        total_services = len(self.services)
        
        for service_name, config in self.services.items():
            print(f"  检查 {config['name']} (端口 {config['port']})...", end=" ")
            
            port_open = self.check_port(config['port'])
            
            if port_open and config['path']:
                service_result = self.check_http_service(config['port'], config['path'])
                if service_result['status'] == 'healthy':
                    print("✅")
                    healthy_services += 1
                else:
                    print(f"❌ ({service_result.get('error', 'HTTP错误')})")
            elif port_open:
                print("✅")
                healthy_services += 1
                service_result = {"status": "healthy", "message": "端口开放"}
            else:
                print("❌ (端口关闭)")
                service_result = {"status": "unreachable", "error": "端口关闭"}
            
            results["services"][service_name] = service_result
        
        # 检查Docker容器
        print("\n🐳 检查Docker容器...")
        container_result = self.check_docker_containers()
        results["containers"] = container_result
        
        if container_result["status"] == "success":
            running_containers = sum(1 for c in container_result["containers"] if "Up" in c.get("state", ""))
            total_containers = len(container_result["containers"])
            print(f"  运行中容器: {running_containers}/{total_containers}")
            
            for container in container_result["containers"]:
                status_icon = "✅" if "Up" in container.get("state", "") else "❌"
                print(f"  {status_icon} {container['name']}: {container['state']}")
        else:
            print(f"  ❌ 容器检查失败: {container_result.get('error', '未知错误')}")
        
        # 检查AI模型
        print("\n🤖 检查AI模型...")
        ai_result = self.check_ai_models()
        results["ai_models"] = ai_result
        
        if ai_result["status"] == "success":
            model_count = ai_result["model_count"]
            print(f"  ✅ 已安装 {model_count} 个AI模型")
            for model in ai_result["models"]:
                print(f"    - {model}")
        else:
            print(f"  ❌ AI模型检查失败: {ai_result.get('error', '未知错误')}")
        
        # 检查数据库
        print("\n📊 检查数据库连接...")
        db_result = self.check_database_connection()
        results["database"] = db_result
        
        if db_result["status"] == "healthy":
            print("  ✅ PostgreSQL连接正常")
        else:
            print(f"  ❌ 数据库连接失败: {db_result.get('error', '未知错误')}")
        
        # 检查Redis
        print("\n🔄 检查Redis连接...")
        redis_result = self.check_redis_connection()
        results["redis"] = redis_result
        
        if redis_result["status"] == "healthy":
            print("  ✅ Redis连接正常")
        else:
            print(f"  ❌ Redis连接失败: {redis_result.get('error', '未知错误')}")
        
        # 生成总结
        results["summary"] = {
            "healthy_services": healthy_services,
            "total_services": total_services,
            "service_health_rate": round(healthy_services / total_services * 100, 1),
            "overall_status": "healthy" if healthy_services >= total_services * 0.8 else "unhealthy"
        }
        
        return results
    
    def print_summary(self, results: Dict):
        """打印检查总结"""
        print("\n" + "=" * 50)
        print("📋 系统状态总结")
        print("=" * 50)
        
        summary = results["summary"]
        overall_status = summary["overall_status"]
        
        status_icon = "✅" if overall_status == "healthy" else "❌"
        print(f"{status_icon} 总体状态: {overall_status.upper()}")
        print(f"📊 服务健康率: {summary['service_health_rate']}% ({summary['healthy_services']}/{summary['total_services']})")
        
        if overall_status == "healthy":
            print("\n🎉 系统运行正常!")
            print("🌐 访问地址: http://你的群晖IP:1750")
        else:
            print("\n⚠️  系统存在问题，请检查以上错误信息")
            print("🔧 建议操作:")
            print("  1. 检查Docker服务是否启动")
            print("  2. 运行: docker-compose restart")
            print("  3. 查看日志: docker-compose logs")
        
        print(f"\n📅 检查时间: {results['timestamp']}")
    
    def save_results(self, results: Dict, filename: str = "system_check_results.json"):
        """保存检查结果"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 检查结果已保存到: {filename}")
        except Exception as e:
            print(f"\n❌ 保存结果失败: {e}")

def main():
    """主函数"""
    checker = SynologySystemChecker()
    
    try:
        # 运行检查
        results = checker.run_comprehensive_check()
        
        # 打印总结
        checker.print_summary(results)
        
        # 保存结果
        checker.save_results(results)
        
        # 返回状态码
        return 0 if results["summary"]["overall_status"] == "healthy" else 1
        
    except KeyboardInterrupt:
        print("\n👋 用户取消检查")
        return 1
    except Exception as e:
        print(f"\n❌ 检查过程中出错: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
