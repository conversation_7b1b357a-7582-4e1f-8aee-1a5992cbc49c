interactions:
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip
      Connection:
      - keep-alive
      X-RapidAPI-Host:
      - biztoc.p.rapidapi.com
      X-RapidAPI-Key:
      - MOCK_API_KEY
    method: GET
    uri: https://biztoc.p.rapidapi.com/sources
  response:
    body:
      string: !!binary |
        H4sIAKSxemYA/4WZa2/bNhSGv+9XCAG2T63d21agQFHYzq1d7aaOkzQZhoKWGIuxTKokZccZ9t93
        RFIWD0V1X9LmPed9dHgTSeWvf45YdvTuSN+rHSkKpY+eHWmmCwraqzfDt8kNqMmllpTWoR1dQiDX
        ulTvhsNXb95a0yAVm+HRv88cjCxTDzMaT5IZ3amOHdI46IOVqP2eXRS+/cvn5JRxwlPaIex2uwFk
        m8ff2xy/jJLbxx5QFz2FmMSgiCUXckMKSXUlOaK4SDI/hMJ2IWvYOYoRWysjqJ2gu4baQKSpvjUo
        95EJVKT7PQKpI9i8ROM1Hk+iRsgKbESmOZH+lBm3UgTggkGHgCwF6uHxQYlC6hiuRDKdi0I/BYx9
        Mm8DGNU4wloof2J8RXxQK0WqccEQU4h0vRNyjVrlixjV5gMIYcRmSeUKUw5apJ4mGhT0o2LpWpM1
        jZGSr160y9yLSldLaogwfpzTYng1yZk8+VKuTidVOR+tf6f6248LMvJqF0oL7j/OCMk5laTI4sWb
        jNwkmKctK8U4VcrvWfb0IGBVkQJ1rUtMPrWxCL/1Bt2TogUw6VkAab0AyFD5PoV8lz0+1X2/pBw/
        c9b3UG6XnV8t58g56xhpxjQTHMwcdaTHyFlKVsLnWCVZSLaseHwqOJe2KX1jlArGM6rWPryVIlQX
        xB2U7Tz/8U3Ume1MDZS3ZajnL9+8fulhCCv2ValY5s/941pNrhq5S9Y59ZzBAGSUFEtJYcFIn4lU
        jPQcAYty2FtLSTmtfNgJlrsF+r4QWcEbEu98J60UQblggLknSoNSEr73SKegTg5qF+a58HjCxlUK
        dL44bHbJhQ1g2mGnq21hbXHOgm1ovI33OqhGyCX1e+i0ESJeE+r4tV0lHkDH1o3LDBsgHpH3MWne
        YT0lPDYzHNexKsSSEp5tYLJ6wLNaTkY8S6Y2EJ3hvjnACrEq6PdlW9OBbCL91boTXZ1kmqxFyVI1
        nIxGPz6tVn9+ZOPjr+vTxfwm215e7fbT87vZ/Ozxj8V1dnf14lRf8+vzuxflzfW3gn+9Ho0/5MV7
        yp9fXf62Kt7Dz5SyDP799fWI8rBcWBA8i9S6aHRcqU1HtRpl+GFFBTyjxTPuv6o/8nQQ7VFIw73I
        eP3ykMjbKDG7iXUQmvEtRsB/q/r9DjP+I99S2DV7iW0qc5nBRLQynGHQI5xmaomT/YwAJmRZkJR2
        gEJeOB0DkSs41ko4TONX2ScC75afrPSHOq7rMKAGD2ULK4h2rob1efQTkEvHBW0U3renl30bt8kM
        OntD5ApeVnAz8Ed06lS4UGxFYYarQ/ScLiWoi8g11bCaNQaDOLZipMKDx5SZU5IV9aIOqeFoTpHa
        x7WjKeCgGuB2RKd5B9eofTgTD9rMFBrNaf170qyPbm0mP6hISA6zGLZm/7QxtWoytnKkpNaGS8KT
        o29qcHfxxZODl/6bYnYxj3tLiZvA9/6Qz24XP99JIL2Z1UNF03omRY6HHE4qWUpKpuHFoTb+A+pI
        MvFDkYdge9BH0PQdpf7xcNZKEZgLBhS2XlPmM4yQ1Nfo7o0cxIF1BJ0ulorKLXpHf2klTGlyA0S5
        VN/rGnOBTnAX40vztcHJ3XaBrx5Jn7TfcO3P6Ivb6WwRv1TY3LAUAb2uMKKVLMTPLuCakAqUfZAi
        j3TB4O6d+tNjPplGngQnX4Uug/NGCG7lRg4aJWkG1xlkdkK3RJsbFEjhZSAVAjRK+HijY7tKN6Xn
        vZxML6KPrvOCymF9+c6TSTJMTk+O43aawlFki9xE106PYJWf7FnO0y7xyPVMwVqqv1kUZU4Q3MjJ
        yOkY7puC/qEbci8kIjVKrEATCzuKFTCvOFxoqiX8z2fZCGyNh1AEiu1x+JYUBd1H0NdNoBdsrSFW
        w/kAfvjEVoqwXDDAaJp5hEXP7ICsji/N0dJdgJBMuiu3TgRvaJUVR7vwwhe7BBuK1LChGxpgnBRr
        hQ2GmJyaD2A+JocrhtOiVxdjwM2iMFIC9ngdcE48PXL5baLBBagiMmOEB6yzVo7fqFy828KcFUUA
        O7dS0Nk2tW/x5pXC7wQDasQuykSC4c+ppCtoMdr1as68laOta2zd1qnmDwQ+ruevBg5mLV2UliQL
        G7hwWhRkDJHzFISCY0aNuokdM1xqCGClJBx9w120UqQWFwwwW3TVvza/dc2QhWfgTj14Nu/PMM1H
        zigGXMHjd6QUCKRyeJHXn2KjX10M5JDTfHrxcKK474z2DYi9w30wYNCe5EJ8vz/8Zadh3dZ67198
        XP7AuE1L65H3mrsXFWyjPk9UZ0YIhlxkZD+w2UFh9ns3nOl0+An/1oaineZ9Jm9ZT1SKnGYrv4F3
        oJ07rYs5OKL3dIHOMQftf+7pLgNof//yH5A6B03xGwAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8994cde11c089bb6-FRA
      Cache-Control:
      - no-store, no-cache, must-revalidate, max-age=0
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Length:
      - '1800'
      Content-Type:
      - application/json
      Date:
      - Tue, 25 Jun 2024 12:01:40 GMT
      NEL:
      - '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}'
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=O1HOQx%2FHZ8gjPS9TkdR9H5vjaetwmc7o%2BMoAZAFPo3dripv5vde5IP7MPse2aWYqlll6n9FZ1xBaJ2GHm87C5gTQJoQQBaQ6pMt%2F8RYSd2b2J8%2FMGAwjmVKgCfk%3D"}],"group":"cf-nel","max_age":604800}'
      Server:
      - RapidAPI-1.2.8
      Strict-Transport-Security:
      - max-age=31556926; includeSubDomains
      Vary:
      - Accept-Encoding, Cookie
      X-RapidAPI-Region:
      - AWS - eu-central-1
      X-RapidAPI-Version:
      - 1.2.8
      X-RateLimit-Requests-Limit:
      - '2000'
      X-RateLimit-Requests-Remaining:
      - '1997'
      X-RateLimit-Requests-Reset:
      - '2163154'
      X-RateLimit-rapid-free-plans-hard-limit-Limit:
      - '500000'
      X-RateLimit-rapid-free-plans-hard-limit-Remaining:
      - '499997'
      X-RateLimit-rapid-free-plans-hard-limit-Reset:
      - '2163154'
      alt-svc:
      - h3=":443"; ma=86400
      permissions-policy:
      - browsing-topics=()
      referrer-policy:
      - strict-origin-when-cross-origin
      rndr-id:
      - 2ae1f676-2d62-4891
      x-content-type-options:
      - nosniff
      x-render-origin-server:
      - gunicorn
    status:
      code: 200
      message: OK
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip
      Connection:
      - keep-alive
      X-RapidAPI-Host:
      - biztoc.p.rapidapi.com
      X-RapidAPI-Key:
      - MOCK_API_KEY
    method: GET
    uri: https://biztoc.p.rapidapi.com/news/source/bloomberg
  response:
    body:
      string: !!binary |
        H4sIAKSxemYA/61ca4/bOJb9vr+CCGYwu0DY0duSgcWinql06hGkKl2YfWBAkZSttiy69SjHPdj/
        vudSciVjZRryWsBMdycp+jD3kuee+6D/6+9vUqN2f9tU+iXX2zfzN2/evsnXizfzv7+p8atl02zq
        +bt3UpU/pfnvjZE/SbN+lyYqDgIVeGESCSn03+qftjrdYG392zGrfuuW/e/bN5s2LfJ6qRWWP7X6
        LfNC9nNbMs/xAua6cz+ehyF7f/cEjCZvCo2f+9Q2ecnOimLHHpcmX7RMlIp9bus6F/j9ar1jF8tc
        Z+xaSM0+XFzg9ypdN+xZVJUoG3xUWxXf7Xe73f6UFsasU10t7JZLva3fiarJZaHrd7QZ7kTcC99t
        CJsLYPPaYnNg86rD5gLYXBI2z4DNcynxe4TNtx32f1Q6+/dvtoEJ/n+eiMJIeTpw0iR0vUi54zwx
        XDXeE4EHZxx44ssjuxOlWOiafWhq9rSFXy51lsscv/rXzFTs3mz/bay96R+Fbhpd/YPJt6YqFNfS
        lAbWLURD5oSxCt4Ar57Koq6fpbM4Sf00FokfyHEWHa46yqLO7MCi3Sn+79Zz3KRmD3nBrr5uTAVz
        XlZmw5qlZncGRxmGflpWmn5VNsuamZJ9wo+xZ1Ot6lMOeHeUec1rLVJTlZobWFp3m+DLvOHpjq9F
        Xja6FCXOONyzmsoHInBnOnJDP/I8XwfhOB8MVx3hA2fuuwc+uP3l7obdtl/basee8gaccq6rUlQK
        PFKKtmjYh7rsXNSwS1NqoqHNJi8XJ5704mW95GleFLkpRV5pnna44BCLy18/iqdtnZe6rrdar+As
        /VUW+J0XUA4cU5HNp/JJmgRx6Lm+ThPHceTIezFcdQTnO3PnkPPf62otyt3+YjyaQlQ48Vtdsfe6
        1JVoYDP22FbERY1hgl2DdpfsM3ijUqdciEWHTDeCQPmGQPniFZTXFpQ3hgueESivLOhUDkh85WZR
        FiWp8BM/UuMcMFx1hAOieRgcOOARl53+ujjlr6GW6OlxmW9q9oxDyz6LvLb0JM1276gLkNVoPvon
        16L+Bv0aaYmUcDNX+FG+BTivCJyvLTj5CtuiG8klbWAqVziBjDMv8n2llZs6wThXDFeNd4U7m/v+
        gSvucbhyyR7KIifuaUyl2bkxvzfsvDByhTi8NC2MphAWMmY9ET/qKhdwkW5acHpVd/455V6Udhfc
        2F3wmnbBU9oFsRR2wZv9LrjJEE0IHzejx58saqsoyrw4DnQUxULGI6P2YNVIjzizuZPMw8Oo/QxR
        UrGLKq/zGqxUg4M+5/WKiOhDqb7F84tKq7yByypdLprlW1wW/J1fSU3sTgrdW9oFl3YXoCnsguO/
        V8RMOe0C10LaDcBb3QZwX4BP1wXQk/FV6s2CVMxCN/I9R4iRfDVYdcQlcYZ89aF80XQoawRtuiRE
        UM8ib4i/SJg+SKEMOehSF4ib1QjDGxAKaOiHtjf0cVx1n7WDvXt0hG66HMRQ2w6dA523KxDTekNx
        Bd5Zi5XmC7hiKhdIP5G+yLRwRZq4zkgXDFeNd4ETzN1DHXWj1UKz65aIiJxw3hb0GaRVScd++chE
        za4KbckdkaLMcqUhKdn7ymxPuglLQuYZIVsHpB0yyAqspMn6ApqpR4Yn9sh8QchTeSFMhfRkorww
        C6UXzMZ5YbhqLDclcwcXITnwwjfNCoLJFR35G/FCEaNdLImLcPgonbggQ4LFHrbgZ9CXXOq1OS1Z
        3svWnAgmV3TUl4BGmCBoXAs690inZQfNDUGDs3roybKKmfZdL0zjNJy5rjPSD8NVx92GQ0LqRBN7
        Qh7VgoXZlxUkSwktU1vrV/lai7fspoXYhPVhHxJPLVK6zy3MOkFK1/TIOP57ZGt8i8yXBAzbEzBu
        BIChtfBBUzlh5iZS6lns60xnjp+Nc8Jw1RFRwZt7hwULJAWlXFpO6isXCBCPyKH64M0/VQaa6VxU
        C5gItJVRYIfEkiv22bQn3YbMYltWAuN32NBFyN/2IZuwedpjc0HYJKzkilfAniw4zPxYxY4rHd9z
        g7H5xHDVEdcBCZ1z6Amt9rrn3Gzp0D+DLqxi/bKpQcdWQdWdhMoKm2q9ZfemQUq+1vTbFye6Azeh
        BhURNlXnSqtTW4ttdVPdCacem5em4Q2g6XfldN5IY+k5sXazINYidkeS03DVUeQUHN6L+3xBAp1d
        6LKpkCecI7ViN1ooCgvK1MaKU6ou1Q256F7klWC/GLJNkTc7ii4P41TUP3UILxclb2vFy24zXHab
        wY0oV3yJzViZSgWnuiFfUY1E8JfXTVCcMdjEZNlelgqRCEfO4izOkmhktjdYddxFOcz2bswWCR3y
        isd2syl2IKqWKOzWGNyOyzzLNHgFt2IpSEO9IMxewTUXS2FOzrstIJdL0NG+7rooTAqXwC+4JSqv
        q3ZDt6O2ZfB90j2VA7xAuUoLx0tmYZBkyTgHDFeNdQCpp7l/GLgvwNXqWwqHv6yuXnDkoPDZM0L3
        bQ5r4s4gqzPlYmkKRQEdqfiWWOqparGZ9pSLIe0GKHv7HptvySsdNiV0PTaFdHzulmiq6bCnckc8
        c2UaRq6Io0w47sjq7HDVUVTleIfuWOblqzfuTJoXmt2CHHSf6vXZxXmVg8JAVbgWX0r8TaWGxG8g
        vC4KUS9PvBoNZQtlY0/9shVbnds8Q0DZNmR5qgAiqVhAb1E/qCTvic2mL5Nkejpdlek41KlyAzeO
        RSxHktRw1XEkdeiUe71lP8NG2kaCC+obVJRp236FqFa6YTbFq3q+ODHb3hjwfS5x8OXrR9osw3A4
        jP9qdzJZhJaxE8yiWSxjP0jGlpiGq07TS2cfrGWXoih0ibSaahjfR2urlLoK7NNS5xV7L9b45PMP
        jyfXlUROtpV7aFvA+D42W6HU1V8bguYLQPM0n7aslIbS8SLpZUGiM38s+wxXjc+m/dncO3TDw0aX
        cEWnVfOmK3zbsis7Q6ZSWzfgB56MKWp2XZk1s4R1iv0NMOGDTqPmTVfvtkVWLiym7T3k+CcweQbM
        jnOmsrySKghEpJSfRUmgnXGWH646Kgx7hx2gLxt2RonRnM43mbwzHSOPvGVnaxCOpHqSykcSzB/x
        e7vhgsBsuehb1408wUUPxXUHNZnaCZUXJ6GjZOpk2Wxkc2G4aryZvWQeHMrNx0YXhSibvLYNZqi+
        0p7pTy0O+kNr1f+Xj1bss6tfwC04rJRKK/zkKYe8fsWFzXtcOtcb4HKKqpScray+5/oFzIL1lEYT
        7mTNhJlQsKZAhiuSTI8dARisGu8B3/+BB/JFKdi1aUtoGnauy5XZy50bg9QXJK8gY9Id+4QwKG3x
        7roSrWKfKpOe5gSCBr9baJ4SNITLEqjE70Cl5v/GopLOzAiVyhbpZC6YpUo5TuSmgStmSTrSBcNV
        R7jgB3LmGgKbkq5yUbO/mpbda20Lpx/LTtQ/NrAe/UnFLsVu/so+J7JORg38xsLynWmhZbQtmq7K
        Ts/XBEt/UnEldq9ENJntHV+HWZjoYCbjQI0sRQxXHUFA8bCXdttmDXLZWtgaz1JUEDvgfvyCboGQ
        M4+a+wUVKu1PECNA02vo/OtWF8d1nH90CYr9Bmylx26AIx7gFzOPWvwW2v6ZJSNJ0DwD9LS95kTE
        risgW2LlpWrmjWyjDVaddhOQvq43VIhGVnsmm5Ym39itgcq8LkyVK/ETu9GV/gtS4eXuRFWfdZ/I
        lV4bWYmmtndhBSWJzI1LKtP1ERkXobEMVMIF9OGTxWDtpakM/VkkZrHwRg44DleNN7obzYPowOhX
        X7s+5YWBuP9u1oiyqWexo2N/j4zqP3VloHzampQ4zWN8Qsy0YzAnCn7d4eM8Q+HnNWxurb4VtltZ
        6ob/DmguXpH5pkOeVPCrSCJFDaWPM+1BwY+UnYNVR/gimHuHF+A5b5bQN7KCXS9z+gvLRqu3rB9I
        Yo/LlobzzBbmb+ulzct+NnnZsKsv7FKnJ9Wodcvrdr2G5lc6g4mpdUY9TZz831pd27QXQvR1RIn2
        whX2wn+lLXCsV9gCRFS9nKybFirHjTNHuS7+L8d20warxrvF8ebOYVfzm/6HGIUOKkiX0r/rObXY
        cl2/ZbdUjqmoYofT3O7z4scCdHKKU76lA9CjEEMFSVP6N+lVQuaFBaZynQXus+KagCcjqmwW+krp
        xFXC992Rl2O46ggvhMOc7N68gIpoEKhe7TXqs16Ylx0720AXvmhbC7UZsL0Zz9rGbQSQkwgKtA8e
        6nAhULcWkgpsFtJ2NW3RjSoVWwvJC0BOZfwABtReFOsgc8H7I4evh6vGlyJCbzgG3/cyz4kR2A0x
        zn7mBdGhouo0lUMvRLGmWHJGDRS6JPuBiwlamR0ZLYlp9gMviA4EbauhsoPmwvZucEn2ExfTjaYK
        X8gwSz0/9iBBx46mHq464hI48+CwJHTVVmZD8vPDp4caStTWPQ17bxvIoma3iBp2ahJ/txfbJxA0
        z40fuRZFc1rPTPfYPN+YGlKUzI0Q3TeQwUQdNq867C6DsHl1ZrGn8oTvJaGrXGcW+8KFDBrnieGq
        ozzhHA4cWbG656En8ZV9Kmi84tmq1+dKy5WVUFfdUwJLSLZAnStVaNsXGMNKfyRhO6na0D4o8pJK
        3S6hll+fL2xpL8is8T9K/yxDrS0+vCemo6jUcZLAT5X203AWqZHFpOGqIygqGU5tf3mk/peEaiUV
        9XNrVepZXQuqYN9qQSQl2F1brZBU6IWQY1KIP6zb1WR8epUAQPxjlSM7W1Hn7LWq8avdBS6H3QUv
        7C6Q161pF/gl7WKyDjLkjg6kEBklB8nIazFcdYQXZvPwkKCgkDpJ1BXxqI9MT0W64rQoVU41NDCX
        sANh1LuEYYmxTq4ntau9JLJlvLpD7kvUr8j4TzsPRp3LDnnamhKSMwcpgRZu5IauGBmuh6uOI6cf
        9fGvxTpH/kzjR7ZfUtWWhD7mqu6uyDlNZpFTpK5wI2zRiQobl2e9oBLFiTdE8JQwyPzAwG3RollT
        K5OYaIWNdDECKspeoczu2A4t9TuerNoU+cibk2DmhcEsS0Z2FYarjvPK4d24sc3b1wlsLSsk1vZp
        Yd5PqT7yTr4+aYitZ1HN2Rl7lLkdVL03JZyS5Q07F3WndS8vThpc7XrJNBdGO+keGvZFj7buVW2D
        nVA3CKRVdxuBFu43wlPaCC1RcjJPyQgBIZnJzJFBFoyMJcNV41ksCIeeusuRg9cma/rBMKTX1/T+
        oRvRO0OqBebvHnnaRyq2Q/Gkxbqmq3OKU9Z75H4sDFl1Rm8eugE9sUe2DToiL1sWbAiZUpLJko4s
        ziLXD2Uaas/zR9YDh6vGeiG29cDDQbE7XUFFfXwvcAkKqJyaue6f92OSJHIUSC4vWhrEyBDbeyq7
        rNrRj+N+6AKC5auFoEGKfhRJ4TPBTxasaz/X36oi+F9b4jrQQHczWe1jlkZx5DohDU8IPbYmO1w1
        /iZ4yTCe3+m8ae20V0nDihWNwVS6mwD7k8vOuweENjFkDzQfdsTLxB8b3+J9P8Yte2i+pnkWKpdz
        d/9y0aaFSPc64MnmXFzHEZl0PU/FgReODBfDVUe0/39QDj8nfW/flZNwvaE3oRdLQ004avvj3tOc
        fSV0sY8o9/qrpaQT8wpq8NOng/K31u5E/zZA5xWJWRpD0r+npijEsq+Xr4mSsrxaI4KkArcntxub
        7CrM9Cxxk0i6M+GlYx83DFcd9bjBOayU37/k3z2vurXEwL6U2xxH/0bQaHFu8/EHOyKEZEQoeO5a
        qBOvRGlxv3FRayH5UtBEsYXkpptKspOSNXhKTXgZZiqIHeV7nUadjSx8DFcdZ/zD7O6ma4ZeGqQU
        dg4l72fv7PMF6lScJIXMtn8jldsUoeyeSdWvUHxD2b2VRvbZAvUnpmObNAwcpMJO6mZZOLINPVx1
        nIEPW6GfRIt0DecGEfQDsre8IQG4gGULO26qdfOXmh7sUN5Q2ure3dn7s1OtronGmwane4MN8Npu
        gNhfVfkLHe2tKOzIKfCp+goqgje2ZqXpRfpkzxQC4YZZ6gsvdnUcjXTBcNVxLhj0GQ5e/l99xamj
        wh4d/ht605bjd+03Bnwou2Ttar3JO/nzgKyX5PkIh2RIw6Bjel90XwTwj4//3002xpskSss0jlUW
        p9nYx4HDVccZ9jAdPjTsZ0HtEs36yHmT140hMyrNavNqVNXSFzMAgSw+iVl5gc+bzLbSTdRsFvuB
        4yeRH4+MisNVp9n2yeat7E7suqd+VGq2lCxoqIWM/jU/tdLZ0vTVrnvPR+Vky8CCOvZk26+5fU3T
        5av9+7PSVNArKwhGYVXLZM8sowjRzFFK+yqLUn/kM8vBquNsfqgMzx4uiKPf96WzJ2NgfxAFFVjW
        tspzAxb/VJlFReGLHmAiUz9pLLfQX8H69FLGSIGISMPQjf7dqr26V4F5Sa8yMppXAU+r2g7uTlf3
        j2WsQQmpo8Mold7YJwGDVUckpfHwvH85u7JnHPIXrPy+LTLobhzIlJ5/PzdLnH56t0SNGspNKKRC
        sT/qTWOHR06qcIru+FtovgA0L/fQfEsFNILmuoemYCohYvbQ030dlEaCk4ahilM3UCP9MFw13g+B
        O7wDz6ImfdKAr29M3dA09FUNfUY9eLNBorpGxvL6dEN0wyof6v2Tje6hwEnqhaJGlUtokv1OuJI0
        JmoFI3XDalvodGMe8nWXr07lA63daCbSyNWhFwbeyDLZcNV4H3jJ8MuLrsqFfWT/nJeaBHNFM3Qb
        XXQjKk+GplVoaNdeGHoG+95WSLpW2UmtyA6YZrQ6YJqdA3A3j9IQsJ3atdeF3sB2pZlOxU8Wf2WU
        hYmA/HZTJ5Ej57eGq47gowhX4TD+mp1p7Bu8HJSPKGzfZdhi/p9ch931BZpLXW9ySJ5Hkelmd3q3
        pbGw9r1eBem+64pitl7jOvvTzlWHCl8Q6rSdlsyfBZk3E9KLpO/7I5/KDFcdYX5vHg7kj9n0VZj+
        Bf1DZatk76kwW1KzoxscLWtZ5RtIT13ki9y04CR90lhEYzZ9laV/QW8sMF+8Au9rZxaYV3tgjj+b
        7nvstIwdGUZOmsWzsanrcNX4b8QJo+G3TnzU5U6wW7G1VFCzswV9WR1VC+hr7IhsSlwI76fwz+zO
        0MjKL3pJ9mS3+uWkssGKgHmxB+aCgMnsCsCWbADMPaJ/wuUvHS4vgDtdw8SBGZNUCvyXF4x8kzdc
        Nf4a0Cz1YXnmqa1WFAmuKzunaQtgtsl49YU9iWJV25pCZdYbq1gfWpKqcklOOv/84eLxpJvQYfPs
        G3ZXrtQtbwibd9930GHT+w6LTX5KEb+nm9QSOglpEDT2k5kXjZ3UGqw64uuhPDjj8EUHFbHgiT7U
        3hmignZN9r960WX/chihWjT90wKTsb/qUVXjf/6WowN9nTzBPdh+V66nPslaUJ9XNP3DAiRsO4Da
        mffpvnzFc0OZZq4Uofaysd/7MVw13gM/ah4+4oQt2UfKPd+ynwVMY59x0Bd/PNMXG9DbSZ2XkIZI
        DC7aiubbduxxKzaI0uN77j/0A0H3ae+vhGxfctDXfmzp2wzsY+EemcsemddARqQWk5XrsyxS0SwN
        pBcLLxQj3TBcdURUwF04fErvzpzD53sXhamx+Iy+G2eat3sAOXi2JwmDCypCTPxmT0dBrKVQzsyP
        U0+OLMQPVx1xuoNhF+TTMi/sNy9o9ssndtnCIlZW4mBfCvWWnVeGsjCwzU4T1T/qEjd/zs7OH/nF
        +f0pp3vzCs1fcGI76O57OpRQIPMOmWuLjHNPyFykNZfp0Av/8y//Bwvu1tuZWgAA
    headers:
      CF-Cache-Status:
      - DYNAMIC
      CF-RAY:
      - 8994cde518bf30fa-FRA
      Cache-Control:
      - no-store, no-cache, must-revalidate, max-age=0
      Connection:
      - keep-alive
      Content-Encoding:
      - gzip
      Content-Length:
      - '5862'
      Content-Type:
      - application/json
      Date:
      - Tue, 25 Jun 2024 12:01:40 GMT
      NEL:
      - '{"success_fraction":0,"report_to":"cf-nel","max_age":604800}'
      Report-To:
      - '{"endpoints":[{"url":"https:\/\/a.nel.cloudflare.com\/report\/v4?s=HD1hdahYJxBUg2bDnv8ap3UYl5AY0k%2B0YTyFPZaFjRFlV0BTQwv15I%2FuVgrzTrPGUkx%2FjSD3e06bHWx8NUxN4v31oUgZdruadCDXmnRYslPKpwPyAoZgs%2FCI0Jk%3D"}],"group":"cf-nel","max_age":604800}'
      Server:
      - RapidAPI-1.2.8
      Strict-Transport-Security:
      - max-age=31556926; includeSubDomains
      Vary:
      - Accept-Encoding, Cookie
      X-RapidAPI-Region:
      - AWS - eu-central-1
      X-RapidAPI-Version:
      - 1.2.8
      X-RateLimit-Requests-Limit:
      - '2000'
      X-RateLimit-Requests-Remaining:
      - '1996'
      X-RateLimit-Requests-Reset:
      - '2163154'
      X-RateLimit-rapid-free-plans-hard-limit-Limit:
      - '500000'
      X-RateLimit-rapid-free-plans-hard-limit-Remaining:
      - '499996'
      X-RateLimit-rapid-free-plans-hard-limit-Reset:
      - '2163154'
      alt-svc:
      - h3=":443"; ma=86400
      permissions-policy:
      - browsing-topics=()
      referrer-policy:
      - strict-origin-when-cross-origin
      rndr-id:
      - 2eb8a4b5-6092-41fc
      x-content-type-options:
      - nosniff
      x-render-origin-server:
      - gunicorn
    status:
      code: 200
      message: OK
version: 1
