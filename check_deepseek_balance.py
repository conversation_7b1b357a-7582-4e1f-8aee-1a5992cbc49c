#!/usr/bin/env python3
"""
DeepSeek API余额检查工具
"""

import asyncio
import aiohttp
import os
from pathlib import Path

def load_env():
    """加载.env文件中的环境变量"""
    env_file = Path('.env')
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key] = value

async def check_balance():
    """检查DeepSeek API余额"""
    load_env()
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ 未找到DeepSeek API密钥")
        return
    
    print(f"🔑 API密钥: {api_key[:8]}...{api_key[-4:]}")
    
    # 检查余额
    balance_url = "https://api.deepseek.com/user/balance"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(balance_url, headers=headers) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ API连接成功")
                    print(f"💰 账户余额: {result.get('balance_infos', [{}])[0].get('total_balance', 'N/A')}")
                    print(f"💳 可用余额: {result.get('balance_infos', [{}])[0].get('available_balance', 'N/A')}")
                elif response.status == 401:
                    print("❌ API密钥无效")
                elif response.status == 402:
                    print("❌ 账户余额不足，需要充值")
                else:
                    error_text = await response.text()
                    print(f"❌ API错误: {response.status} - {error_text}")
                    
    except Exception as e:
        print(f"❌ 连接失败: {e}")

async def test_simple_request():
    """测试简单的API请求"""
    load_env()
    
    api_key = os.getenv('DEEPSEEK_API_KEY')
    if not api_key:
        print("❌ 未找到DeepSeek API密钥")
        return
    
    print("\n🧪 测试简单API请求...")
    
    url = "https://api.deepseek.com/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "deepseek-chat",  # 使用更便宜的模型测试
        "messages": [
            {"role": "user", "content": "Hello"}
        ],
        "max_tokens": 50
    }
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    result = await response.json()
                    print("✅ API请求成功")
                    print(f"📝 响应: {result['choices'][0]['message']['content']}")
                    
                    # 显示用量信息
                    usage = result.get('usage', {})
                    print(f"📊 Token用量:")
                    print(f"   输入: {usage.get('prompt_tokens', 0)} tokens")
                    print(f"   输出: {usage.get('completion_tokens', 0)} tokens")
                    print(f"   总计: {usage.get('total_tokens', 0)} tokens")
                    
                elif response.status == 401:
                    print("❌ API密钥无效")
                elif response.status == 402:
                    print("❌ 账户余额不足，需要充值")
                    print("💡 请访问 https://platform.deepseek.com/ 进行充值")
                else:
                    error_text = await response.text()
                    print(f"❌ API错误: {response.status}")
                    print(f"   详情: {error_text}")
                    
    except Exception as e:
        print(f"❌ 请求失败: {e}")

async def main():
    """主函数"""
    print("🧠 DeepSeek API状态检查")
    print("=" * 50)
    
    # 检查余额
    await check_balance()
    
    # 测试API请求
    await test_simple_request()
    
    print("\n" + "=" * 50)
    print("💡 如果遇到402错误，请:")
    print("1. 访问 https://platform.deepseek.com/")
    print("2. 登录您的账户")
    print("3. 进入充值页面")
    print("4. 充值后重新测试")
    print("\n📋 DeepSeek价格信息:")
    print("• deepseek-chat: 输入2元/百万tokens, 输出8元/百万tokens")
    print("• deepseek-reasoner: 输入4元/百万tokens, 输出16元/百万tokens")
    print("• 优惠时段(00:30-08:30): 5折优惠")

if __name__ == "__main__":
    asyncio.run(main())
