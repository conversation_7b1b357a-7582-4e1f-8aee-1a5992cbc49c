#!/usr/bin/env python3
"""
智能选股系统完整项目展示页面
包含所有功能模块的可视化界面
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
import asyncio
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List
import yfinance as yf
import pandas as pd

app = FastAPI(title="智能选股系统 - 完整项目展示")

class CompleteProjectDashboard:
    """完整项目仪表板"""
    
    def __init__(self):
        self.connected_clients = []
        self.stock_pool = {
            "美股热门": ["AAPL", "MSFT", "GOOGL", "TSLA", "NVDA", "AMZN"],
            "中国A股": ["000001.SZ", "600519.SS", "000858.SZ", "002415.SZ", "300059.SZ", "600036.SS"],
            "港股": ["0700.HK", "0941.HK", "1810.HK", "2318.HK"],
        }
        self.ai_signals = []
        self.portfolio = {
            "total_value": 1000000,
            "available_cash": 500000,
            "positions": [],
            "daily_pnl": 0,
            "total_pnl": 0
        }
    
    async def get_real_stock_data(self, symbol: str) -> Dict:
        """获取真实股票数据"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            hist = ticker.history(period="1d")
            
            if hist.empty:
                return {}
            
            latest = hist.iloc[-1]
            prev_close = info.get('previousClose', latest['Open'])
            
            return {
                'symbol': symbol,
                'name': info.get('longName', symbol),
                'price': float(latest['Close']),
                'change': float(latest['Close'] - prev_close),
                'change_percent': float((latest['Close'] - prev_close) / prev_close * 100),
                'volume': int(latest['Volume']),
                'market_cap': info.get('marketCap', 0),
                'pe_ratio': info.get('trailingPE', 0),
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {'symbol': symbol, 'error': str(e)}
    
    async def calculate_technical_indicators(self, symbol: str) -> Dict:
        """计算技术指标"""
        try:
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="60d")
            
            if len(hist) < 20:
                return {}
            
            # RSI计算
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi.iloc[-1]
            
            # 移动平均线
            ma5 = hist['Close'].rolling(window=5).mean().iloc[-1]
            ma20 = hist['Close'].rolling(window=20).mean().iloc[-1]
            ma60 = hist['Close'].rolling(window=60).mean().iloc[-1] if len(hist) >= 60 else 0
            
            # RSI
            rsi = calculate_rsi(hist['Close'])
            
            # 成交量分析
            avg_volume = hist['Volume'].rolling(window=20).mean().iloc[-1]
            current_volume = hist['Volume'].iloc[-1]
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            # 价格位置
            high_52w = hist['High'].max()
            low_52w = hist['Low'].min()
            current_price = hist['Close'].iloc[-1]
            price_position = (current_price - low_52w) / (high_52w - low_52w) * 100
            
            return {
                'rsi': float(rsi) if not pd.isna(rsi) else 50,
                'ma5': float(ma5) if not pd.isna(ma5) else 0,
                'ma20': float(ma20) if not pd.isna(ma20) else 0,
                'ma60': float(ma60) if not pd.isna(ma60) else 0,
                'volume_ratio': float(volume_ratio),
                'price_position': float(price_position),
                'trend': 'up' if ma5 > ma20 > ma60 else 'down' if ma5 < ma20 < ma60 else 'sideways'
            }
        except Exception as e:
            return {'error': str(e)}
    
    async def generate_ai_signal(self, symbol: str, stock_data: Dict, technical_data: Dict) -> Dict:
        """生成AI交易信号"""
        try:
            # 简化的AI决策逻辑
            score = 0
            reasons = []
            risks = []
            
            # 技术面评分
            rsi = technical_data.get('rsi', 50)
            if 30 <= rsi <= 70:
                score += 20
                reasons.append(f"RSI({rsi:.1f})处于合理区间")
            elif rsi < 30:
                score += 30
                reasons.append(f"RSI({rsi:.1f})超卖，可能反弹")
            else:
                score -= 10
                risks.append(f"RSI({rsi:.1f})超买，注意回调风险")
            
            # 趋势评分
            trend = technical_data.get('trend', 'sideways')
            if trend == 'up':
                score += 25
                reasons.append("均线多头排列，趋势向上")
            elif trend == 'down':
                score -= 20
                risks.append("均线空头排列，趋势向下")
            
            # 成交量评分
            volume_ratio = technical_data.get('volume_ratio', 1)
            if volume_ratio > 1.5:
                score += 15
                reasons.append(f"成交量放大{volume_ratio:.1f}倍，资金关注")
            elif volume_ratio < 0.5:
                score -= 10
                risks.append("成交量萎缩，缺乏资金关注")
            
            # 价格位置评分
            price_position = technical_data.get('price_position', 50)
            if 20 <= price_position <= 80:
                score += 10
                reasons.append("价格位置合理")
            elif price_position < 20:
                score += 20
                reasons.append("价格接近年内低点，安全边际高")
            else:
                score -= 15
                risks.append("价格接近年内高点，注意高位风险")
            
            # 基本面评分
            pe_ratio = stock_data.get('pe_ratio', 0)
            if 0 < pe_ratio < 20:
                score += 15
                reasons.append(f"PE({pe_ratio:.1f})估值合理")
            elif pe_ratio > 50:
                score -= 15
                risks.append(f"PE({pe_ratio:.1f})估值偏高")
            
            # 生成建议
            if score >= 60:
                action = "强烈买入"
                confidence = min(95, score + 20)
            elif score >= 40:
                action = "买入"
                confidence = min(85, score + 10)
            elif score >= 20:
                action = "观望"
                confidence = min(75, score + 5)
            elif score >= 0:
                action = "谨慎观望"
                confidence = min(65, score)
            else:
                action = "卖出"
                confidence = max(30, 50 + score)
            
            return {
                'symbol': symbol,
                'action': action,
                'confidence': confidence,
                'score': score,
                'reasons': reasons,
                'risks': risks,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {'symbol': symbol, 'error': str(e)}
    
    async def broadcast_to_clients(self, message: Dict):
        """广播消息给所有客户端"""
        if not self.connected_clients:
            return
        
        disconnected = []
        for client in self.connected_clients:
            try:
                await client.send_text(json.dumps(message, ensure_ascii=False))
            except:
                disconnected.append(client)
        
        # 清理断开的连接
        for client in disconnected:
            self.connected_clients.remove(client)

# 创建仪表板实例
dashboard = CompleteProjectDashboard()

@app.get("/", response_class=HTMLResponse)
async def get_dashboard():
    """主仪表板页面"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>🤖 智能选股系统 - 完整项目展示</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
                font-family: 'Microsoft YaHei', Arial, sans-serif; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                color: #333;
            }
            
            .header {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                padding: 20px 0;
                box-shadow: 0 2px 20px rgba(0,0,0,0.1);
                position: sticky;
                top: 0;
                z-index: 1000;
            }
            
            .header-content {
                max-width: 1400px;
                margin: 0 auto;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0 20px;
            }
            
            .logo {
                display: flex;
                align-items: center;
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .status-bar {
                display: flex;
                gap: 20px;
                align-items: center;
            }
            
            .status-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                background: #f8f9fa;
                border-radius: 20px;
                font-size: 14px;
            }
            
            .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background: #52c41a;
                animation: pulse 2s infinite;
            }
            
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }
            
            .container {
                max-width: 1400px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .dashboard-grid {
                display: grid;
                grid-template-columns: 1fr 1fr 1fr;
                gap: 20px;
                margin-bottom: 30px;
            }
            
            .card {
                background: rgba(255,255,255,0.95);
                backdrop-filter: blur(10px);
                border-radius: 16px;
                padding: 24px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                border: 1px solid rgba(255,255,255,0.2);
                transition: transform 0.3s ease, box-shadow 0.3s ease;
            }
            
            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 12px 40px rgba(0,0,0,0.15);
            }
            
            .card-title {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 16px;
                display: flex;
                align-items: center;
                gap: 8px;
            }
            
            .main-content {
                display: grid;
                grid-template-columns: 2fr 1fr;
                gap: 20px;
            }
            
            .stock-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 16px;
                max-height: 600px;
                overflow-y: auto;
            }
            
            .stock-card {
                background: #f8f9fa;
                border-radius: 12px;
                padding: 16px;
                border-left: 4px solid #3498db;
                transition: all 0.3s ease;
            }
            
            .stock-card:hover {
                background: #e9ecef;
                border-left-color: #2980b9;
            }
            
            .stock-symbol {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .stock-name {
                font-size: 12px;
                color: #7f8c8d;
                margin-bottom: 8px;
            }
            
            .stock-price {
                font-size: 20px;
                font-weight: bold;
                margin-bottom: 4px;
            }
            
            .stock-change {
                font-size: 14px;
                font-weight: 500;
            }
            
            .positive { color: #27ae60; }
            .negative { color: #e74c3c; }
            .neutral { color: #7f8c8d; }
            
            .ai-signal {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 12px;
                animation: slideIn 0.5s ease;
            }
            
            @keyframes slideIn {
                from { opacity: 0; transform: translateX(20px); }
                to { opacity: 1; transform: translateX(0); }
            }
            
            .signal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;
            }
            
            .signal-action {
                font-weight: bold;
                font-size: 16px;
            }
            
            .signal-confidence {
                background: rgba(255,255,255,0.2);
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 12px;
            }
            
            .signal-reasons {
                font-size: 14px;
                opacity: 0.9;
            }
            
            .portfolio-summary {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                margin-bottom: 16px;
            }
            
            .portfolio-item {
                text-align: center;
                padding: 16px;
                background: #f8f9fa;
                border-radius: 8px;
            }
            
            .portfolio-value {
                font-size: 24px;
                font-weight: bold;
                color: #2c3e50;
            }
            
            .portfolio-label {
                font-size: 12px;
                color: #7f8c8d;
                margin-top: 4px;
            }
            
            .loading {
                text-align: center;
                padding: 40px;
                color: #7f8c8d;
                font-style: italic;
            }
            
            .error {
                background: #fdf2f2;
                color: #e74c3c;
                padding: 12px;
                border-radius: 8px;
                margin: 8px 0;
            }
            
            .tabs {
                display: flex;
                margin-bottom: 20px;
                background: rgba(255,255,255,0.1);
                border-radius: 12px;
                padding: 4px;
            }
            
            .tab {
                flex: 1;
                padding: 12px 20px;
                text-align: center;
                border: none;
                background: transparent;
                color: white;
                cursor: pointer;
                border-radius: 8px;
                transition: all 0.3s ease;
                font-weight: 500;
            }
            
            .tab.active {
                background: rgba(255,255,255,0.2);
                backdrop-filter: blur(10px);
            }
            
            .tab-content {
                display: none;
            }
            
            .tab-content.active {
                display: block;
            }
            
            .refresh-btn {
                background: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s ease;
            }
            
            .refresh-btn:hover {
                background: #2980b9;
            }
            
            .market-overview {
                display: grid;
                grid-template-columns: repeat(3, 1fr);
                gap: 16px;
                margin-bottom: 20px;
            }
            
            .market-item {
                text-align: center;
                padding: 20px;
                background: rgba(255,255,255,0.1);
                border-radius: 12px;
                color: white;
            }
            
            .market-title {
                font-size: 14px;
                opacity: 0.8;
                margin-bottom: 8px;
            }
            
            .market-value {
                font-size: 24px;
                font-weight: bold;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="header-content">
                <div class="logo">
                    🤖 智能选股系统
                </div>
                <div class="status-bar">
                    <div class="status-item">
                        <div class="status-dot"></div>
                        <span id="connectionStatus">AI已连接</span>
                    </div>
                    <div class="status-item">
                        📊 <span id="dataStatus">数据实时</span>
                    </div>
                    <div class="status-item">
                        🕐 <span id="currentTime"></span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="container">
            <!-- 市场概览 -->
            <div class="market-overview">
                <div class="market-item">
                    <div class="market-title">监控股票</div>
                    <div class="market-value" id="totalStocks">18</div>
                </div>
                <div class="market-item">
                    <div class="market-title">AI信号</div>
                    <div class="market-value" id="totalSignals">0</div>
                </div>
                <div class="market-item">
                    <div class="market-title">活跃连接</div>
                    <div class="market-value" id="activeConnections">1</div>
                </div>
            </div>
            
            <!-- 功能标签页 -->
            <div class="tabs">
                <button class="tab active" onclick="showTab('realtime')">实时监控</button>
                <button class="tab" onclick="showTab('signals')">AI信号</button>
                <button class="tab" onclick="showTab('portfolio')">投资组合</button>
                <button class="tab" onclick="showTab('analysis')">技术分析</button>
            </div>
            
            <!-- 实时监控标签页 -->
            <div id="realtime" class="tab-content active">
                <div class="main-content">
                    <div class="card">
                        <div class="card-title">
                            📈 实时股票监控
                            <button class="refresh-btn" onclick="refreshStockData()">刷新数据</button>
                        </div>
                        <div id="stockGrid" class="stock-grid">
                            <div class="loading">正在加载股票数据...</div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-title">🤖 实时AI分析</div>
                        <div id="aiSignals">
                            <div class="loading">等待AI分析信号...</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI信号标签页 -->
            <div id="signals" class="tab-content">
                <div class="card">
                    <div class="card-title">🎯 AI交易信号历史</div>
                    <div id="signalHistory">
                        <div class="loading">加载信号历史...</div>
                    </div>
                </div>
            </div>
            
            <!-- 投资组合标签页 -->
            <div id="portfolio" class="tab-content">
                <div class="card">
                    <div class="card-title">💼 投资组合概览</div>
                    <div class="portfolio-summary">
                        <div class="portfolio-item">
                            <div class="portfolio-value">¥1,000,000</div>
                            <div class="portfolio-label">总资产</div>
                        </div>
                        <div class="portfolio-item">
                            <div class="portfolio-value positive">+2.5%</div>
                            <div class="portfolio-label">今日收益</div>
                        </div>
                    </div>
                    <div id="portfolioDetails">
                        <div class="loading">加载投资组合详情...</div>
                    </div>
                </div>
            </div>
            
            <!-- 技术分析标签页 -->
            <div id="analysis" class="tab-content">
                <div class="card">
                    <div class="card-title">📊 技术分析工具</div>
                    <div id="technicalAnalysis">
                        <div class="loading">加载技术分析工具...</div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            let ws = null;
            let stockData = {};
            let signalCount = 0;
            
            // WebSocket连接
            function connectWebSocket() {
                ws = new WebSocket('ws://localhost:8000/ws');
                
                ws.onopen = function() {
                    console.log('WebSocket连接已建立');
                    document.getElementById('connectionStatus').textContent = 'AI已连接';
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };
                
                ws.onclose = function() {
                    console.log('WebSocket连接已关闭');
                    document.getElementById('connectionStatus').textContent = 'AI已断开';
                    setTimeout(connectWebSocket, 5000);
                };
            }
            
            // 处理WebSocket消息
            function handleWebSocketMessage(data) {
                if (data.type === 'stock_update') {
                    updateStockDisplay(data.data);
                } else if (data.type === 'ai_signal') {
                    addAISignal(data.data);
                    signalCount++;
                    document.getElementById('totalSignals').textContent = signalCount;
                }
            }
            
            // 更新股票显示
            function updateStockDisplay(stocks) {
                const grid = document.getElementById('stockGrid');
                grid.innerHTML = '';
                
                stocks.forEach(stock => {
                    if (stock.error) return;
                    
                    const changeClass = stock.change > 0 ? 'positive' : stock.change < 0 ? 'negative' : 'neutral';
                    const changeSymbol = stock.change > 0 ? '+' : '';
                    
                    const card = document.createElement('div');
                    card.className = 'stock-card';
                    card.innerHTML = `
                        <div class="stock-symbol">${stock.symbol}</div>
                        <div class="stock-name">${stock.name || stock.symbol}</div>
                        <div class="stock-price ${changeClass}">¥${stock.price.toFixed(2)}</div>
                        <div class="stock-change ${changeClass}">
                            ${changeSymbol}${stock.change.toFixed(2)} (${stock.change_percent.toFixed(2)}%)
                        </div>
                        <div style="font-size: 12px; color: #7f8c8d; margin-top: 8px;">
                            成交量: ${stock.volume.toLocaleString()}
                        </div>
                    `;
                    grid.appendChild(card);
                });
            }
            
            // 添加AI信号
            function addAISignal(signal) {
                const container = document.getElementById('aiSignals');
                if (container.querySelector('.loading')) {
                    container.innerHTML = '';
                }
                
                const signalDiv = document.createElement('div');
                signalDiv.className = 'ai-signal';
                signalDiv.innerHTML = `
                    <div class="signal-header">
                        <div class="signal-action">${signal.symbol} - ${signal.action}</div>
                        <div class="signal-confidence">${signal.confidence}%</div>
                    </div>
                    <div class="signal-reasons">
                        ${signal.reasons.join(' | ')}
                    </div>
                `;
                
                container.insertBefore(signalDiv, container.firstChild);
                
                // 保留最近10个信号
                while (container.children.length > 10) {
                    container.removeChild(container.lastChild);
                }
            }
            
            // 刷新股票数据
            async function refreshStockData() {
                try {
                    const response = await fetch('/api/stocks');
                    const data = await response.json();
                    updateStockDisplay(data);
                } catch (error) {
                    console.error('刷新数据失败:', error);
                }
            }
            
            // 标签页切换
            function showTab(tabName) {
                // 隐藏所有标签页
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });
                
                // 显示选中的标签页
                document.getElementById(tabName).classList.add('active');
                event.target.classList.add('active');
            }
            
            // 更新时间
            function updateTime() {
                const now = new Date();
                document.getElementById('currentTime').textContent = 
                    now.toLocaleTimeString('zh-CN');
            }
            
            // 初始化
            window.onload = function() {
                connectWebSocket();
                updateTime();
                setInterval(updateTime, 1000);
                refreshStockData();
            };
        </script>
    </body>
    </html>
    """)

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket.accept()
    dashboard.connected_clients.append(websocket)
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        dashboard.connected_clients.remove(websocket)

@app.get("/api/stocks")
async def get_all_stocks():
    """获取所有股票数据"""
    all_stocks = []
    
    for category, symbols in dashboard.stock_pool.items():
        for symbol in symbols:
            stock_data = await dashboard.get_real_stock_data(symbol)
            if stock_data and not stock_data.get('error'):
                all_stocks.append(stock_data)
    
    return all_stocks

# 后台任务：定期更新数据和生成AI信号
async def background_tasks():
    """后台任务"""
    while True:
        try:
            # 获取股票数据
            all_stocks = []
            for category, symbols in dashboard.stock_pool.items():
                for symbol in symbols[:2]:  # 限制数量避免API限制
                    stock_data = await dashboard.get_real_stock_data(symbol)
                    if stock_data and not stock_data.get('error'):
                        all_stocks.append(stock_data)
                        
                        # 生成AI信号
                        technical_data = await dashboard.calculate_technical_indicators(symbol)
                        if technical_data and not technical_data.get('error'):
                            ai_signal = await dashboard.generate_ai_signal(symbol, stock_data, technical_data)
                            if ai_signal and not ai_signal.get('error'):
                                await dashboard.broadcast_to_clients({
                                    'type': 'ai_signal',
                                    'data': ai_signal
                                })
            
            # 广播股票更新
            if all_stocks:
                await dashboard.broadcast_to_clients({
                    'type': 'stock_update',
                    'data': all_stocks
                })
            
            await asyncio.sleep(30)  # 30秒更新一次
            
        except Exception as e:
            print(f"后台任务错误: {e}")
            await asyncio.sleep(60)

@app.on_event("startup")
async def startup_event():
    """启动事件"""
    asyncio.create_task(background_tasks())

if __name__ == "__main__":
    import uvicorn
    print("🚀 启动智能选股系统完整项目展示")
    print("📱 访问地址: http://localhost:8000")
    print("🎯 功能包括:")
    print("  • 实时股票监控 (美股 + 中国A股 + 港股)")
    print("  • AI智能分析和交易信号")
    print("  • 投资组合管理")
    print("  • 技术分析工具")
    print("  • WebSocket实时推送")
    uvicorn.run(app, host="0.0.0.0", port=8000)
