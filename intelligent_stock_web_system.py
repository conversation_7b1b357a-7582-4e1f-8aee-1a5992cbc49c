"""
基于OpenBB + 本地AI的Web端智能选股系统
整合OpenBB Platform的强大数据能力和本地AI的智能分析
"""

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
import numpy as np

# OpenBB导入
from openbb import obb

# 本地AI引擎导入
from ai_engine.local_llm import LocalLLMClient, ModelResponse
from ai_engine.decision_engine import IntelligentDecisionEngine, TradingSignal

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="智能选股系统",
    description="基于OpenBB数据和本地AI的智能股票分析系统",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局变量
connected_clients: List[WebSocket] = []
decision_engine: Optional[IntelligentDecisionEngine] = None
market_scanner_task: Optional[asyncio.Task] = None

class OpenBBDataProvider:
    """OpenBB数据提供者"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    async def get_stock_quote(self, symbol: str, provider: str = "yfinance") -> Dict:
        """获取股票实时报价"""
        try:
            quote_data = obb.equity.price.quote(symbol=symbol, provider=provider)
            return quote_data.results[0].__dict__ if quote_data.results else {}
        except Exception as e:
            self.logger.error(f"获取股票报价失败 {symbol}: {e}")
            return {}
    
    async def get_historical_data(self, symbol: str, days: int = 60, provider: str = "yfinance") -> pd.DataFrame:
        """获取历史数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            historical_data = obb.equity.price.historical(
                symbol=symbol,
                start_date=start_date.strftime("%Y-%m-%d"),
                end_date=end_date.strftime("%Y-%m-%d"),
                provider=provider
            )
            
            return historical_data.to_df() if historical_data.results else pd.DataFrame()
        except Exception as e:
            self.logger.error(f"获取历史数据失败 {symbol}: {e}")
            return pd.DataFrame()
    
    async def get_technical_indicators(self, symbol: str) -> Dict:
        """获取技术指标"""
        try:
            # 获取历史数据
            df = await self.get_historical_data(symbol)
            if df.empty:
                return {}
            
            # 使用OpenBB的技术分析功能
            # RSI
            rsi_data = obb.technical.rsi(data=df.to_dict('records'), length=14)
            
            # MACD
            macd_data = obb.technical.macd(data=df.to_dict('records'))
            
            # 移动平均线
            ma20_data = obb.technical.sma(data=df.to_dict('records'), length=20)
            ma60_data = obb.technical.sma(data=df.to_dict('records'), length=60)
            
            # 布林带
            bb_data = obb.technical.bbands(data=df.to_dict('records'))
            
            # 提取最新值
            latest_data = {
                'rsi': rsi_data.results[-1].rsi if rsi_data.results else 50,
                'macd': macd_data.results[-1].macd if macd_data.results else 0,
                'macd_signal': macd_data.results[-1].macd_signal if macd_data.results else 0,
                'ma20': ma20_data.results[-1].sma if ma20_data.results else 0,
                'ma60': ma60_data.results[-1].sma if ma60_data.results else 0,
                'bb_upper': bb_data.results[-1].bb_upper if bb_data.results else 0,
                'bb_lower': bb_data.results[-1].bb_lower if bb_data.results else 0,
                'bb_middle': bb_data.results[-1].bb_middle if bb_data.results else 0,
            }
            
            return latest_data
            
        except Exception as e:
            self.logger.error(f"获取技术指标失败 {symbol}: {e}")
            return {}
    
    async def get_fundamental_data(self, symbol: str, provider: str = "fmp") -> Dict:
        """获取基本面数据"""
        try:
            # 获取公司概况
            overview = obb.equity.fundamental.overview(symbol=symbol, provider=provider)
            
            # 获取财务比率
            ratios = obb.equity.fundamental.ratios(symbol=symbol, provider=provider)
            
            fundamental_data = {}
            if overview.results:
                overview_data = overview.results[0].__dict__
                fundamental_data.update({
                    'pe': overview_data.get('pe_ratio', 0),
                    'pb': overview_data.get('pb_ratio', 0),
                    'market_cap': overview_data.get('market_cap', 0),
                    'revenue': overview_data.get('revenue', 0),
                })
            
            if ratios.results:
                ratio_data = ratios.results[0].__dict__
                fundamental_data.update({
                    'roe': ratio_data.get('return_on_equity', 0),
                    'roa': ratio_data.get('return_on_assets', 0),
                    'debt_to_equity': ratio_data.get('debt_to_equity', 0),
                })
            
            return fundamental_data
            
        except Exception as e:
            self.logger.error(f"获取基本面数据失败 {symbol}: {e}")
            return {}
    
    async def get_news_sentiment(self, symbol: str, provider: str = "benzinga") -> Dict:
        """获取新闻和情感分析"""
        try:
            # 获取公司新闻
            news_data = obb.news.company(symbol=symbol, provider=provider, limit=10)
            
            if not news_data.results:
                return {'sentiment_score': 0, 'news_count': 0}
            
            # 简单的情感分析（可以用更复杂的NLP模型）
            positive_keywords = ['上涨', '增长', '盈利', '突破', '利好', 'positive', 'growth', 'profit']
            negative_keywords = ['下跌', '亏损', '风险', '下降', '利空', 'negative', 'loss', 'risk']
            
            sentiment_score = 0
            for news in news_data.results[:5]:  # 分析最近5条新闻
                title = news.title.lower() if news.title else ""
                text = news.text.lower() if hasattr(news, 'text') and news.text else ""
                content = title + " " + text
                
                positive_count = sum(1 for keyword in positive_keywords if keyword in content)
                negative_count = sum(1 for keyword in negative_keywords if keyword in content)
                
                sentiment_score += positive_count - negative_count
            
            return {
                'sentiment_score': sentiment_score,
                'news_count': len(news_data.results),
                'latest_news': [{'title': news.title, 'date': str(news.date)} for news in news_data.results[:3]]
            }
            
        except Exception as e:
            self.logger.error(f"获取新闻情感失败 {symbol}: {e}")
            return {'sentiment_score': 0, 'news_count': 0}

class StockAnalysisService:
    """股票分析服务"""
    
    def __init__(self):
        self.data_provider = OpenBBDataProvider()
        self.ai_client = LocalLLMClient()
        self.logger = logging.getLogger(__name__)
    
    async def comprehensive_analysis(self, symbol: str) -> Dict:
        """综合分析股票"""
        try:
            # 并行获取各种数据
            quote_task = self.data_provider.get_stock_quote(symbol)
            technical_task = self.data_provider.get_technical_indicators(symbol)
            fundamental_task = self.data_provider.get_fundamental_data(symbol)
            news_task = self.data_provider.get_news_sentiment(symbol)
            
            quote_data, technical_data, fundamental_data, news_data = await asyncio.gather(
                quote_task, technical_task, fundamental_task, news_task,
                return_exceptions=True
            )
            
            # 整合数据
            stock_data = {
                'symbol': symbol,
                'price': quote_data.get('last_price', 0) if isinstance(quote_data, dict) else 0,
                'volume': quote_data.get('volume', 0) if isinstance(quote_data, dict) else 0,
                'change_percent': quote_data.get('change_percent', 0) if isinstance(quote_data, dict) else 0,
                **technical_data if isinstance(technical_data, dict) else {},
                **fundamental_data if isinstance(fundamental_data, dict) else {},
                **news_data if isinstance(news_data, dict) else {},
            }
            
            # AI分析
            ai_analysis = self.ai_client.analyze_stock_decision(stock_data)
            
            return {
                'stock_data': stock_data,
                'ai_analysis': {
                    'recommendation': ai_analysis.recommendations.get('action', '观望'),
                    'confidence': ai_analysis.confidence,
                    'reasoning': ai_analysis.reasoning,
                    'risks': ai_analysis.recommendations.get('risks', []),
                    'full_analysis': ai_analysis.content
                },
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"综合分析失败 {symbol}: {e}")
            return {
                'error': str(e),
                'symbol': symbol,
                'timestamp': datetime.now().isoformat()
            }

# 初始化服务
analysis_service = StockAnalysisService()

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    global decision_engine, market_scanner_task
    
    logger.info("启动智能选股系统...")
    
    # 初始化决策引擎
    decision_engine = IntelligentDecisionEngine(initial_capital=100000)
    
    # 启动市场扫描任务
    market_scanner_task = asyncio.create_task(market_scanner())
    
    logger.info("系统启动完成")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    global market_scanner_task
    
    if market_scanner_task:
        market_scanner_task.cancel()
    
    logger.info("系统已关闭")

async def market_scanner():
    """市场扫描任务"""
    # 中国A股主要股票池
    stock_pool = [
        "000001.SZ",  # 平安银行
        "000002.SZ",  # 万科A
        "600000.SS",  # 浦发银行
        "600036.SS",  # 招商银行
        "600519.SS",  # 贵州茅台
        "000858.SZ",  # 五粮液
        "002415.SZ",  # 海康威视
        "300059.SZ",  # 东方财富
    ]
    
    while True:
        try:
            logger.info("开始市场扫描...")
            
            for symbol in stock_pool:
                try:
                    # 分析股票
                    analysis_result = await analysis_service.comprehensive_analysis(symbol)
                    
                    # 如果AI建议买入且信心度高
                    if (analysis_result.get('ai_analysis', {}).get('recommendation') == '买入' and
                        analysis_result.get('ai_analysis', {}).get('confidence', 0) > 70):
                        
                        # 发送买入通知
                        await broadcast_buy_notification(analysis_result)
                        
                        # 模拟执行交易
                        await simulate_trade(analysis_result)
                    
                    # 避免请求过于频繁
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    logger.error(f"扫描股票 {symbol} 失败: {e}")
                    continue
            
            logger.info("市场扫描完成，等待下次扫描...")
            await asyncio.sleep(300)  # 5分钟扫描一次
            
        except Exception as e:
            logger.error(f"市场扫描任务错误: {e}")
            await asyncio.sleep(60)  # 出错后1分钟重试

async def broadcast_buy_notification(analysis_result: Dict):
    """广播买入通知"""
    notification = {
        "type": "buy_signal",
        "data": {
            "symbol": analysis_result['stock_data']['symbol'],
            "price": analysis_result['stock_data']['price'],
            "recommendation": analysis_result['ai_analysis']['recommendation'],
            "confidence": analysis_result['ai_analysis']['confidence'],
            "reasoning": analysis_result['ai_analysis']['reasoning'],
            "risks": analysis_result['ai_analysis']['risks'],
            "timestamp": analysis_result['timestamp'],
            "auto_executed": True
        }
    }
    
    # 发送给所有连接的客户端
    disconnected_clients = []
    for client in connected_clients:
        try:
            await client.send_text(json.dumps(notification, ensure_ascii=False))
        except:
            disconnected_clients.append(client)
    
    # 清理断开的连接
    for client in disconnected_clients:
        connected_clients.remove(client)

async def simulate_trade(analysis_result: Dict):
    """模拟交易执行"""
    # 这里可以添加模拟交易逻辑
    logger.info(f"模拟买入: {analysis_result['stock_data']['symbol']} "
                f"价格: {analysis_result['stock_data']['price']} "
                f"信心度: {analysis_result['ai_analysis']['confidence']}")

# WebSocket连接管理
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    await websocket.accept()
    connected_clients.append(websocket)
    
    try:
        while True:
            # 保持连接活跃
            data = await websocket.receive_text()
            # 可以处理客户端发送的消息
            
    except WebSocketDisconnect:
        connected_clients.remove(websocket)

# API端点
@app.get("/api/analyze/{symbol}")
async def analyze_stock(symbol: str):
    """分析单只股票"""
    result = await analysis_service.comprehensive_analysis(symbol)
    return result

@app.get("/api/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "connected_clients": len(connected_clients)
    }

# 静态文件服务（前端）
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/")
async def read_root():
    """主页"""
    return HTMLResponse("""
    <!DOCTYPE html>
    <html>
    <head>
        <title>智能选股系统</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>智能选股系统</h1>
        <p>基于OpenBB数据和本地AI的智能股票分析系统</p>
        <div id="notifications"></div>
        
        <script>
            const ws = new WebSocket("ws://localhost:8000/ws");
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'buy_signal') {
                    showNotification(data.data);
                }
            };
            
            function showNotification(signal) {
                const div = document.createElement('div');
                div.innerHTML = `
                    <h3>🤖 AI买入建议</h3>
                    <p>股票: ${signal.symbol}</p>
                    <p>价格: ¥${signal.price}</p>
                    <p>信心度: ${signal.confidence}%</p>
                    <p>理由: ${signal.reasoning.join(', ')}</p>
                    <hr>
                `;
                document.getElementById('notifications').appendChild(div);
            }
        </script>
    </body>
    </html>
    """)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
