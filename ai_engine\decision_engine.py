"""
智能决策引擎
整合技术分析、基本面分析和AI大模型的综合决策系统
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from datetime import datetime
import logging

from .local_llm import LocalLLMClient, ModelResponse


@dataclass
class TradingSignal:
    """交易信号数据结构"""
    stock_code: str
    stock_name: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float  # 0-100
    price: float
    quantity: int
    reasons: List[str]
    risks: List[str]
    target_price: float
    stop_loss: float
    holding_period: str  # 'short', 'medium', 'long'
    timestamp: datetime


class IntelligentDecisionEngine:
    """智能决策引擎"""
    
    def __init__(self, initial_capital: float = 100000):
        self.initial_capital = initial_capital
        self.available_capital = initial_capital
        self.llm_client = LocalLLMClient()
        self.logger = logging.getLogger(__name__)
        
        # 决策权重配置
        self.weights = {
            'technical': 0.4,      # 技术面权重
            'fundamental': 0.3,    # 基本面权重
            'ai_analysis': 0.3     # AI分析权重
        }
        
        # 风控参数
        self.risk_params = {
            'max_single_position': 0.15,    # 单只股票最大仓位15%
            'max_sector_position': 0.30,    # 单个行业最大仓位30%
            'min_confidence': 60,           # 最低信心度要求
            'max_daily_trades': 5,          # 每日最大交易次数
            'stop_loss_ratio': 0.08         # 止损比例8%
        }
    
    def analyze_stock(self, stock_data: Dict) -> Optional[TradingSignal]:
        """综合分析股票并生成交易信号"""
        try:
            # 1. 技术面分析
            technical_score = self._analyze_technical(stock_data)
            
            # 2. 基本面分析
            fundamental_score = self._analyze_fundamental(stock_data)
            
            # 3. AI智能分析
            ai_analysis = self.llm_client.analyze_stock_decision(stock_data)
            ai_score = ai_analysis.confidence
            
            # 4. 综合评分
            final_score = (
                technical_score * self.weights['technical'] +
                fundamental_score * self.weights['fundamental'] +
                ai_score * self.weights['ai_analysis']
            )
            
            # 5. 生成交易决策
            if final_score >= 70 and self._pass_risk_check(stock_data, final_score):
                return self._generate_buy_signal(stock_data, final_score, ai_analysis)
            elif final_score <= 30:
                return self._generate_sell_signal(stock_data, final_score, ai_analysis)
            else:
                return None  # 观望
                
        except Exception as e:
            self.logger.error(f"分析股票 {stock_data.get('code')} 失败: {e}")
            return None
    
    def _analyze_technical(self, stock_data: Dict) -> float:
        """技术面分析评分 (0-100)"""
        score = 50  # 基础分数
        
        # RSI分析
        rsi = stock_data.get('rsi', 50)
        if 30 <= rsi <= 70:  # 正常区间
            score += 10
        elif rsi < 30:  # 超卖
            score += 20
        elif rsi > 80:  # 超买
            score -= 20
        
        # MACD分析
        macd = stock_data.get('macd', 0)
        macd_signal = stock_data.get('macd_signal', 0)
        if macd > macd_signal and macd > 0:  # 金叉且在零轴上方
            score += 15
        elif macd < macd_signal and macd < 0:  # 死叉且在零轴下方
            score -= 15
        
        # 均线分析
        price = stock_data.get('price', 0)
        ma20 = stock_data.get('ma20', 0)
        ma60 = stock_data.get('ma60', 0)
        
        if price > ma20 > ma60:  # 多头排列
            score += 15
        elif price < ma20 < ma60:  # 空头排列
            score -= 15
        
        # 成交量分析
        volume_ratio = stock_data.get('volume_ratio', 1)
        if volume_ratio > 1.5:  # 放量
            score += 10
        elif volume_ratio < 0.5:  # 缩量
            score -= 5
        
        # 布林带分析
        bb_position = stock_data.get('bb_position', 0.5)
        if bb_position < 0.2:  # 接近下轨
            score += 10
        elif bb_position > 0.8:  # 接近上轨
            score -= 10
        
        return max(0, min(100, score))
    
    def _analyze_fundamental(self, stock_data: Dict) -> float:
        """基本面分析评分 (0-100)"""
        score = 50  # 基础分数
        
        # PE分析
        pe = stock_data.get('pe', 20)
        if 10 <= pe <= 25:  # 合理估值
            score += 15
        elif pe < 10:  # 低估值
            score += 25
        elif pe > 40:  # 高估值
            score -= 20
        
        # PB分析
        pb = stock_data.get('pb', 2)
        if pb < 1:  # 破净
            score += 20
        elif pb > 5:  # 高溢价
            score -= 15
        
        # ROE分析
        roe = stock_data.get('roe', 10)
        if roe > 15:  # 高ROE
            score += 20
        elif roe < 5:  # 低ROE
            score -= 15
        
        # 营收增长率分析
        revenue_growth = stock_data.get('revenue_growth', 0)
        if revenue_growth > 20:  # 高增长
            score += 15
        elif revenue_growth < -10:  # 负增长
            score -= 20
        
        # 资金流向分析
        net_inflow = stock_data.get('net_inflow', 0)
        if net_inflow > 0:  # 资金净流入
            score += 10
        else:  # 资金净流出
            score -= 5
        
        return max(0, min(100, score))
    
    def _pass_risk_check(self, stock_data: Dict, confidence: float) -> bool:
        """风险检查"""
        # 信心度检查
        if confidence < self.risk_params['min_confidence']:
            return False
        
        # ST股票检查
        stock_name = stock_data.get('name', '')
        if 'ST' in stock_name or '*ST' in stock_name:
            return False
        
        # 流动性检查
        volume_ratio = stock_data.get('volume_ratio', 0)
        if volume_ratio < 0.1:  # 成交量过低
            return False
        
        # 涨跌停检查
        pct_change = stock_data.get('pct_change', 0)
        if abs(pct_change) >= 9.5:  # 接近涨跌停
            return False
        
        return True
    
    def _generate_buy_signal(self, stock_data: Dict, confidence: float, ai_analysis: ModelResponse) -> TradingSignal:
        """生成买入信号"""
        price = stock_data.get('price', 0)
        
        # 计算买入数量（基于仓位管理）
        max_investment = self.available_capital * self.risk_params['max_single_position']
        quantity = int(max_investment / price / 100) * 100  # 按手数计算
        
        # 计算目标价和止损价
        target_price = price * 1.15  # 目标收益15%
        stop_loss = price * (1 - self.risk_params['stop_loss_ratio'])
        
        # 整合买入理由
        reasons = [
            f"技术面评分: {self._analyze_technical(stock_data):.1f}/100",
            f"基本面评分: {self._analyze_fundamental(stock_data):.1f}/100",
            f"AI信心度: {ai_analysis.confidence:.1f}%"
        ]
        reasons.extend(ai_analysis.reasoning[:3])  # 添加AI分析理由
        
        return TradingSignal(
            stock_code=stock_data.get('code', ''),
            stock_name=stock_data.get('name', ''),
            action='BUY',
            confidence=confidence,
            price=price,
            quantity=quantity,
            reasons=reasons,
            risks=ai_analysis.recommendations.get('risks', []),
            target_price=target_price,
            stop_loss=stop_loss,
            holding_period='medium',
            timestamp=datetime.now()
        )
    
    def _generate_sell_signal(self, stock_data: Dict, confidence: float, ai_analysis: ModelResponse) -> TradingSignal:
        """生成卖出信号"""
        return TradingSignal(
            stock_code=stock_data.get('code', ''),
            stock_name=stock_data.get('name', ''),
            action='SELL',
            confidence=100 - confidence,  # 卖出信心度
            price=stock_data.get('price', 0),
            quantity=0,  # 卖出数量需要根据持仓确定
            reasons=[f"综合评分过低: {confidence:.1f}/100"],
            risks=[],
            target_price=0,
            stop_loss=0,
            holding_period='immediate',
            timestamp=datetime.now()
        )
    
    def batch_analyze(self, stock_list: List[Dict]) -> List[TradingSignal]:
        """批量分析股票"""
        signals = []
        
        for stock_data in stock_list:
            signal = self.analyze_stock(stock_data)
            if signal and signal.action == 'BUY':
                signals.append(signal)
        
        # 按信心度排序
        signals.sort(key=lambda x: x.confidence, reverse=True)
        
        # 限制每日交易次数
        return signals[:self.risk_params['max_daily_trades']]


# 使用示例
if __name__ == "__main__":
    # 初始化决策引擎
    decision_engine = IntelligentDecisionEngine(initial_capital=100000)
    
    # 测试股票数据
    test_stocks = [
        {
            'code': '000001',
            'name': '平安银行',
            'price': 12.50,
            'pct_change': 2.5,
            'rsi': 45,
            'macd': 0.15,
            'macd_signal': 0.10,
            'ma20': 12.0,
            'ma60': 11.5,
            'volume_ratio': 1.8,
            'bb_position': 0.3,
            'pe': 8.5,
            'pb': 0.8,
            'roe': 12.5,
            'revenue_growth': 15.0,
            'net_inflow': 5000
        }
    ]
    
    # 批量分析
    signals = decision_engine.batch_analyze(test_stocks)
    
    for signal in signals:
        print(f"股票: {signal.stock_name}")
        print(f"建议: {signal.action}")
        print(f"信心度: {signal.confidence:.1f}%")
        print(f"买入价: {signal.price:.2f}")
        print(f"数量: {signal.quantity}股")
        print(f"理由: {signal.reasons}")
        print("-" * 50)
